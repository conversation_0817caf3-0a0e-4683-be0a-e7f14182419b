(function ($) {

    $('#siteriaform').parsley({ excluded: "input[type=button], input[type=submit], input[type=reset], input[type=hidden], [disabled], :hidden" });    

    console.log('form script');
    
    // bind register button
    $('#siteriaform').submit(function(event) {
        event.preventDefault();
        
        if ($("#siteriaform").parsley().validate()) {
            var form = $(this).closest("form");


			_iub.cons_instructions.push(["submit", {
				writeOnLocalStorage: false, // default: false
				form: {
					selector: form,
				},
				consent: {
					legal_notices: [
						{
							identifier: 'privacy_policy',
						},
						{
							identifier: 'cookie_policy',
						},
						{
							identifier: 'terms',
						}
					],
				}
			},{
				success: function(response) {
					// continue with form submit...
					console.log('iubenda ' + response.responseText);


					// post to url
					var url = form.attr('action');

					form = new FormData($(document.getElementById("siteriaform"))[0]);

					$.blockUI();
					$.ajax({
						url: url,
						type: 'POST',
						data: form,
						cache: false,
						contentType: false,
						processData: false,
						success:
							function (returndata) {
								$.unblockUI();
								// warn
								$.alert({
									theme: 'supervan',
									escapeKey: true,
									animation: 'top',
									closeAnimation: 'bottom',
									backgroundDismiss: true,
									title: 'Email inviata! :)',
									content: 'Ti risponderemo appena possibile' + '<br/>',
									buttons: {
										ok: {
											text: 'Continua <i class="icon-material-outline-arrow-right-alt"></i>',
											btnClass: 'button full-width button-sliding-icon ripple-effect'
										}
									}
								});
							},
						error:
							function (response, status, errorThrown) {
								var msg = 'Errore invio.';
								if (response) {
									if (response.responseText) {
										msg = response.responseText;
									}
								}
								$.unblockUI();
								// warn
								$.alert({
									theme: 'supervan',
									escapeKey: true,
									animation: 'top',
									closeAnimation: 'bottom',
									backgroundDismiss: true,
									title: 'Oh oh! :(',
									content: msg + '<br/>',
									buttons: {
										ok: {
											text: 'Continua <i class="icon-material-outline-arrow-right-alt"></i>',
											btnClass: 'button full-width button-sliding-icon ripple-effect'
										}
									}
								});
							}
					});
				},
				error: function (response) {
                    var msg = 'Errore invio.';
					$.unblockUI();
					// warn
					$.alert({
						theme: 'supervan',
						escapeKey: true,
						animation: 'top',
						closeAnimation: 'bottom',
						backgroundDismiss: true,
						title: 'Oh oh! :(',
						content: msg + '<br/>',
						buttons: {
							ok: {
								text: 'Continua <i class="icon-material-outline-arrow-right-alt"></i>',
								btnClass: 'button full-width button-sliding-icon ripple-effect'
							}
						}
					});
				}
			}])
        }
        
        return false;
    });
})(jQuery)