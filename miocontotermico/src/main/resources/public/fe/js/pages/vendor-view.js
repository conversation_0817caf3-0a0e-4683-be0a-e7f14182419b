$(function() {
    // Switch
    // -------------------------
    if (Array.prototype.forEach) {
        var elems = Array.prototype.slice.call(document.querySelectorAll('.switchery'));
        elems.forEach(function(html) {
            var switchery = new Switchery(html);
        });
    }
    else {
        var elems = document.querySelectorAll('.switchery');

        for (var i = 0; i < elems.length; i++) {
            var switchery = new Switchery(elems[i]);
        }
    }
    // Table setup
    // ------------------------------

    // Setting datatable defaults
    $.extend( $.fn.dataTable.defaults, {
        autoWidth: false,
        responsive: true,
        dom: '<"datatable-header"fl><"datatable-scroll-wrap"t><"datatable-footer"ip>',
        language: {
            processing: 'Elaborazione in corso...',
            loadingRecords: 'Caricamento in corso...',
            search: '<span>Filtra:</span> _INPUT_',
            searchPlaceholder: 'Ricerca ordine...',
            lengthMenu: '<span>Mostra:</span> _MENU_',
            zeroRecords: "Nessun risultato corrisponde alla ricerca",
            emptyTable: 'Non ci sono dati disponibili',
            info:       'Visualizzo da _START_ a _END_ di _TOTAL_ elementi',
            infoEmpty:  'Visualizzo da 0 a 0 di 0 elementi',
            infoFiltered: '(filtrati da _MAX_ elementi totali)',            
            paginate: { 'first': 'Prima', 'last': 'Ultima', 'next': '&rarr;', 'previous': '&larr;' }
        },
        drawCallback: function () {
            $(this).find('tbody tr').slice(-3).find('.dropdown, .btn-group').addClass('dropup');
        },
        preDrawCallback: function() {
            $(this).find('tbody tr').slice(-3).find('.dropdown, .btn-group').removeClass('dropup');
        }
    });

    $.extend( $.fn.dataTableExt.oSort, {
        'sortable-value-pre': function ( a ) {
            if (a) {
                var nodes = $.parseHTML(a);
                if (nodes) {
                    var node = nodes[0];
                    if (node) {
                        if (node.hasAttribute('sortable-value')) {
                            a = node.getAttribute('sortable-value');
                        }
                    }
                }
            }
            return a;
        },

        'sortable-value-asc': function ( a, b ) {
            return a - b;
        },

        'sortable-value-desc': function ( a, b ) {
            return b - a;
        }
    } );

    // Control position
    var tableVendor = $('.datatable-responsive-control-right').DataTable({
        responsive: {
            details: {
                type: 'column',
                target: -1
            }
        },
        order: [[ 0, "desc" ]],
        columnDefs: [
            {
                className: 'control',
                orderable: false,
                targets: -1
            },
            { 
                orderable: true,
                type: 'sortable-value',
                targets: [0, 2]
            },
            { 
                orderable: false,
                width: '100px',
                targets: [5]
            }
        ]
    });
    
    tableVendor.on( 'responsive-display', function ( e, datatable, row, showHide, update ) {
        bindStatusUpdate();
        bindPaymentStatusUpdate();
        bindOrderClone();
    });
    
    tableVendor.on( 'responsive-resize', function ( e, datatable, columns ) {
        bindStatusUpdate();
        bindPaymentStatusUpdate();
        bindOrderClone();
    });
    // Control position Statements
    var tableEc = $('.datatable-responsive-control-right-ec').DataTable({
        responsive: {
            details: {
                type: 'column',
                target: -1
            }
        },
        language: {            
            searchPlaceholder: 'Ricerca E/C...'
        },
        order: [[ 0, "desc" ]],
        columnDefs: [
            {
                className: 'control',
                orderable: false,
                targets: -1
            },
            { 
                orderable: true,
                type: 'sortable-value',
                targets: [ 0, 1, 3 ]
            },
            { 
                orderable: false,
                width: '270px',
                targets: [ 4 ]
            }
        ]
    });
    
    tableEc.on( 'responsive-display', function ( e, datatable, row, showHide, update ) {
        
    });
    
    tableEc.on( 'responsive-resize', function ( e, datatable, columns ) {
        
    });    
    // External table additions
    // ------------------------------

    // Enable Select2 select for the length option
    $('.dataTables_length select').select2({
        minimumResultsForSearch: Infinity,
        width: 'auto'
    });
    
    // Form components
    // ------------------------------

    // Select with search
    $('.select-search').select2(); 

    // Select2 selects
    $('.select').select2({
        minimumResultsForSearch: Infinity
    });


    // Styled file input
    $(".file-styled").uniform({
        fileButtonClass: 'action btn bg-warning'
    });


    // Styled checkboxes, radios
    $(".styled, .multiselect-container input").uniform({
        radioClass: 'choice'
    });
    
    // Lunghezza campi
    // -------------------------
    $('.maxlength').maxlength({
        alwaysShow: true,
        placement: 'top-right'
    });
    
    // Campo valuta
    // -------------------------
    $('.currency').mask('#.##0,00', {reverse: true});

    // Autosize Descrizione
    // -------------------------
    autosize($('.elastic'));    
    
    // Schedule
    // ------------------------------

    var calendarEvents = $.parseJSON($("#calendar-events").html());

    // Initialize calendar with options
	$('#calendar').fullCalendar({
		header: {
			left: 'prev,next today',
			center: 'title',
			right: 'month,agendaWeek,agendaDay,listMonth'
		},
		allDaySlot: false,
		defaultView: 'listMonth',            
		minTime: '00:00:01',
		maxTime: '24:00:00',
		events: calendarEvents,
		eventClick:  function(event, jsEvent, view) {
			
			var orderId = event.id;
			var orderUrl = $('#orderView').attr('href');
			var url = new URI(orderUrl);
			url.removeSearch("orderId");
			url.addSearch("orderId", orderId);
            
			window.location.href = url;
		}
	});
    
    // Render in hidden elements
    $('a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
        $('.schedule').fullCalendar('render');
    });


    // Simple toolbar
    $('.wysihtml5-min').wysihtml5({
        parserRules:  wysihtml5ParserRules,
        stylesheets: ["https://siteria.it/tpls/be/1/1.6/assets/css/components.css"],
        "font-styles": true, // Font styling, e.g. h1, h2, etc. Default true
        "emphasis": true, // Italics, bold, etc. Default true
        "lists": false, // (Un)ordered lists, e.g. Bullets, Numbers. Default true
        "html": false, // Button which allows you to edit the generated HTML. Default false
        "link": false, // Button to insert a link. Default true
        "image": false, // Button to insert an image. Default true,
        "action": false, // Undo / Redo buttons,
        "color": true // Button to change color of font
    });
    
    // Initialize validation
    $(".form-horizontal").validate({
        ignore: 'input[type=hidden], .select2-search__field', // ignore hidden fields
        errorClass: 'validation-error-label',
        successClass: 'validation-valid-label',
        highlight: function (element, errorClass) {
            $(element).removeClass(errorClass);
        },
        unhighlight: function (element, errorClass) {
            $(element).removeClass(errorClass);
        },
        // Different components require proper error label placement
        errorPlacement: function (error, element) {

            // Styled checkboxes, radios, bootstrap switch
            if (element.parents('div').hasClass("checker") || element.parents('div').hasClass("choice") || element.parent().hasClass('bootstrap-switch-container')) {
                if (element.parents('label').hasClass('checkbox-inline') || element.parents('label').hasClass('radio-inline')) {
                    error.appendTo(element.parent().parent().parent().parent());
                } else {
                    error.appendTo(element.parent().parent().parent().parent().parent());
                }
            }

            // Unstyled checkboxes, radios
            else if (element.parents('div').hasClass('checkbox') || element.parents('div').hasClass('radio')) {
                error.appendTo(element.parent().parent().parent());
            }

            // Input with icons and Select2
            else if (element.parents('div').hasClass('has-feedback') || element.hasClass('select2-hidden-accessible')) {
                error.appendTo(element.parent());
            }

            // Inline checkboxes, radios
            else if (element.parents('label').hasClass('checkbox-inline') || element.parents('label').hasClass('radio-inline')) {
                error.appendTo(element.parent().parent());
            }

            // Input group, styled file input
            else if (element.parent().hasClass('uploader') || element.parents().hasClass('input-group')) {
                error.appendTo(element.parent().parent());
            } else {
                error.insertAfter(element);
            }
        },
        rules: {
            email: {
                email: true
            }
        }
    });

    // bind delete action
    $('#deleteVendor').click(function (event) {

        // disable the default form submission
        event.preventDefault();

        var postToUrl = $('#vendorRemoveUri').attr('href');
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-checkmark icon-2x"></i><br/><br/> Aggiorna venditore!',
            content: "Conferma aggiornamento?",
            columnClass:"col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
            buttons: {
                annulla: {
                    text: 'Annulla',
                    btnClass: 'btn-default',
                    action: function () {
                        //close
                    }
                },
                conferma: {
                    text: 'Conferma',
                    btnClass: 'bg-black',
                    action: function () {
                        $.blockUI()
                        $.ajax({
                            url: postToUrl,
                            type: 'POST',
                            data: null,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success:
                                    function (returndata) {
                                        $.unblockUI();
                                        var url = $("#vendorsSuccessUri").attr("href");
                                        window.location.href = url;
                                    },
                            error:
                                function (response, status, errorThrown) {
                                    $.unblockUI();
                                    // warn
                                    $.alert({
                                        theme: 'supervan',
                                        escapeKey: true,
                                        animation: 'top',
                                        closeAnimation: 'bottom',
                                        backgroundDismiss: true,
                                        title: 'Oh oh! :(',
                                        content: 'Aggiornamento non riuscito.<br/>'
                                    });
                                }
                        });                    
                    }                    
                }
            }
        });        

        return false;
    });
    
    bindStatusUpdate();
    bindPaymentStatusUpdate();
    bindOrderClone();
   
    
    $("#form-edit-vendor").on('submit', function(e) {
        $(this).find('input[type="checkbox"]').each( function () {
            var checkbox = $(this);
            if( checkbox.is(':checked')) {
                checkbox.attr('value','1');
            } else {
                checkbox.after().append(checkbox.clone().attr({type:'hidden', value:0}));
                checkbox.prop('disabled', true);
            }
        });
    });
    
    $('.btn-cancel').click(function () {
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-cancel-square icon-2x"></i><br/><br/> Sei sicuro?',
            content: "Le modifiche fatte fin'ora verranno perse.",
            buttons: {
                annulla: {
                    btnClass: 'btn-default',
                    action: function () {
                        //
                    }
                },
                conferma: {
                    btnClass: 'bg-black',
                    action: function () {
                        window.location.href = $('#vendorViewUri').attr('href');
                    }
                }
            }
        });
    });
    
});

// responsive table redraw even if table is inside an hidden tab
$(document).ready(function() {
    $('a[data-toggle="tab"]').on( 'shown.bs.tab', function (e) {        
        $($.fn.dataTable.tables( true ) ).css('width', '100%');
        $($.fn.dataTable.tables( true ) ).DataTable().columns.adjust().draw();
    } ); 
});

function bindOrderClone() {
    
    $('.order-clone').click(function (event) {
        // disable the default form submission
        event.preventDefault();

        var postToUrl = $(this).attr('href');
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-checkmark icon-2x"></i><br/><br/> Attenzione!',
            content: "L'attuale carrello verrà perso. Vuoi continuare?",
            columnClass:"col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
            buttons: {
                annulla: {
                    text: 'Annulla',
                    btnClass: 'btn-cancel',
                    action: function () {
                        //close
                    }
                },
                conferma: {
                    text: 'Conferma',
                    btnClass: 'bg-black',
                    action: function () {
                        $.blockUI();
                        $.ajax({
                            url: postToUrl,
                            type: 'POST',
                            data: null,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success:
                                    function (returndata) {
                                        $.unblockUI();
                                        var url = $("#ordersCheckout").attr("href");
                                        window.location.href = url;
                                    },
                            error:
                                function (response, status, errorThrown) {
                                    $.unblockUI();
                                    // warn
                                    $.alert({
                                        theme: 'supervan',
                                        escapeKey: true,
                                        animation: 'top',
                                        closeAnimation: 'bottom',
                                        backgroundDismiss: true,
                                        title: 'Oh oh! :(',
                                        content: 'Riordino non riuscito.<br/>'
                                    });
                                }
                        });                    
                    }                    
                }
            }
        });        

        return false;        
    }); 
}

function bindStatusUpdate() {
    
    $('.status-update').click(function (event) {
        // disable the default form submission
        event.preventDefault();

        var postToUrl = $(this).attr('href');
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-checkmark icon-2x"></i><br/><br/> Aggiornamento stato!',
            content: "Confermi?",
            columnClass:"col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
            buttons: {
                annulla: {
                    text: 'Annulla',
                    btnClass: 'btn-default',
                    action: function () {
                        //close
                    }
                },
                conferma: {
                    text: 'Conferma',
                    btnClass: 'bg-black',
                    action: function () {
                        $.blockUI();
                        $.ajax({
                            url: postToUrl,
                            type: 'POST',
                            data: null,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success:
                                    function (returndata) {
                                        $.unblockUI();
                                        var url = $("#vendorViewSuccessUri").attr("href");
                                        window.location.href = url;
                                    },
                            error:
                                function (response, status, errorThrown) {
                                    $.unblockUI();
                                    // warn
                                    $.alert({
                                        theme: 'supervan',
                                        escapeKey: true,
                                        animation: 'top',
                                        closeAnimation: 'bottom',
                                        backgroundDismiss: true,
                                        title: 'Oh oh! :(',
                                        content: 'Aggiornamento non riuscito.<br/>'
                                    });
                                }
                        });                    
                    }                    
                }
            }
        });        

        return false;        
    });
}

function bindPaymentStatusUpdate() {
    
    $('.payment-status-update').click(function (event) {
        // disable the default form submission
        event.preventDefault();

        var postToUrl = $(this).attr('href');
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-checkmark icon-2x"></i><br/><br/> Aggiornamento stato pagamento!',
            content: "Confermi?",
            columnClass:"col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
            buttons: {
                annulla: {
                    text: 'Annulla',
                    btnClass: 'btn-default',
                    action: function () {
                        //close
                    }
                },
                conferma: {
                    text: 'Conferma',
                    btnClass: 'bg-black',
                    action: function () {
                        $.blockUI();
                        $.ajax({
                            url: postToUrl,
                            type: 'POST',
                            data: null,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success:
                                    function (returndata) {
                                        $.unblockUI();
                                        var url = $("#vendorViewSuccessUri").attr("href");
                                        window.location.href = url;
                                    },
                            error:
                                function (response, status, errorThrown) {
                                    $.unblockUI();
                                    // warn
                                    $.alert({
                                        theme: 'supervan',
                                        escapeKey: true,
                                        animation: 'top',
                                        closeAnimation: 'bottom',
                                        backgroundDismiss: true,
                                        title: 'Oh oh! :(',
                                        content: 'Aggiornamento non riuscito.<br/>'
                                    });
                                }
                        });                    
                    }                    
                }
            }
        });        

        return false;        
    }); 
}