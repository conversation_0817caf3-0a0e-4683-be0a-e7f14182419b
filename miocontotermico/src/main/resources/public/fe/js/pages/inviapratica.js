$(function() {
    
    // Initialize validation
    $("#form-register").validate({
        rules: {
          "password": "required",
          "password-confirm": {
            equalTo: "#password"
          }
        }          
    });
    
    // bind register button
    $('#form-register').submit(function(event) {
        event.preventDefault();
        
        if ($("#form-register").valid()) {
            
            var form = $(this).closest("form");
            // post to url
			var url = $("#registerSendUri").attr("href");

			// data to post
			var data = new FormData();
			data.append('email', $('#email').val());
			data.append('password', $('#password').val());
			data.append('password-confirm', $('#password-confirm').val());
			data.append('profileType', $('#profileType').val());
			

			$.blockUI();
			$.ajax({
				url: url,
				type: 'POST',
				data: data,
				cache: false,
				contentType: false,
				processData: false,
				success:
					function (returndata) {
						$.unblockUI();                        
						var url = $("#proceduresAddUri").attr("href");
						window.location.href = url;
					},
				error:
					function (response, status, errorThrown) {
						var msg = 'Registrazione non riuscita.';
						if (response) {
							if (response.responseText) {
								msg = response.responseText;
							}
						}
						$.unblockUI();
						// warn
						$.alert({
							theme: 'supervan',
							escapeKey: true,
							animation: 'top',
							closeAnimation: 'bottom',
							backgroundDismiss: true,
							title: 'Oh oh! :(',
							content: msg + '<br/>'
						});
					}
			});  
		}
        
        return false;
    });
    
});