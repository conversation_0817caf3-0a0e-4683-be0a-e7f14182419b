.mainmenu-area {
    z-index: 99;
}
.main-slider .slide-content .big-title {
  font-size: 55px;
}
.qoute-form-one .image-box .phone-box i[class*=-icon-] {
    color: #3aa40c;    
}
.qoute-form-one .form-block button[type=submit] {
    background-color: #3aa40c;    
}
.thm-btn {
    background: #3aa40c;
    color: #fff;
}
.top-bar-style1 {
  background: #006a3b;
}

.single-choose-box .overlay-content h3 a {
  color: #006a3b;
}

.single-service-style1 .text-holder:before {
  background: rgb(51, 152, 253);
  background: linear-gradient(90deg, #28a745 0%, #3aa40c 40%, #91c942a1 100%);
}

.txt-grey {
  color: #83888d;
}

.btn-two {
  color: #3aa40b;
}

.top-social-style1 li a {
  border-right: none;
}

.main-menu .navigation li:hover > a, .main-menu .navigation li.current > a {
    color: #ffffff;
}

.main-menu .navigation li a:before {
    bottom: 0px;
}

.top-social-style1 li:first-child a {
  border-left: none;
}

.for-enquiry p span:before {
  color: #3aa40b;
}

.mission-vision-content .overlay-content {
  padding: 30px 20px 0 !important;
  padding-top: 5% !important;
}

.contact-form .inner {
  display: block;
}

.contact-form {
  display: block;
}

.thm-btn.home-three {
  background-color: #3aa40b;
}

.thm-btn.home-three:hover {
  background: #3498fd;
}

.challenge-solution-box {
  border: 0px;
}

.mission-vision-area .pd0 {
  padding: 20px;
}

.bl {
  color: #212529;
}

.bl:hover {
  color: #212529;
}

.pd {
  padding: 100px 0 52px;
}

.pt-0 {
  padding-top: 0px !important;
}

.pb-0 {
  padding-bottom: 0px;
}

.pd-20 {
  padding: 20px;
}

.pb-50 {
  padding-bottom: 50px;
}

.coll {
  padding: 1.375rem .75rem;
}

.bold {
  font-weight: bold;
}

.contact-form form input[type='date'] {
  position: relative;
  display: block;
  background: #ffffff;
  border: 1px solid #e2e8f0;
  width: 100%;
  height: 60px;
  font-size: 16px;
  padding-left: 20px;
  padding-right: 20px;
  border-radius: 5px;
  margin-bottom: 30px;
  transition: all 500ms ease;
}

.txt-white {
  color: #ffffff !important;
}

/* RADIO BUTTON */
label {
  position: relative;
  display: inline-block;
  text-align: center;
  padding: 10px;
}

.btn:hover {
  cursor: pointer !important;
}

.single-footer-widget .contact-info-box .text a:hover {
  color: #3398fd;
}

.btn:active {
  box-shadow: 0 1px #666 !important;
  transform: translateY(2px) !important;
}

.btn {
  font-size: 15px;
  font-weight: bold;
  height: 80px;
  width: 100px;
  text-align: center;
}

.btn-sık {
  transition: all 0.2s ease;
  background-color: #f3f7fa;
  border: 2px solid #3aa40b !important;
  min-width: 150px;
  border-radius: 20px;
}

.parsley-errors-list li {
  color: darkred;
  margin-top: -20px;
}

btn-sık::selection {
  background: green;
}

.nav-item:focus div {
  outline: 0;
  box-shadow: none;
  background-color: #3aa40b;
  ;
  color: white;
}

.btn:focus {
  box-shadow: none;
}

input[type="radio"] {
  position: relative;
  visibility: hidden;
}

input[type="radio"]+div {
  position: relative;
}

input[type="radio"]:checked+div {
  background-color: #3aa40b;
}

input[type="radio"]:checked+div>span {
  color: white;
}

input[type="radio"]+div>span {
  position: relative;
  top: 25%;
}

@keyframes fall {
  100% {
    -webkit-transform: translate(-5px, 5px) rotate(30deg);
    -moz-transform: translate(-5px, 5px) rotate(30deg);
    -o-transform: translate(-5px, 5px) rotate(30deg);
    -ms-transform: translate(-5px, 5px) rotate(30deg);
    transform: translate(-5px, 5px) rotate(30deg);
  }
}

@-moz-keyframes fall {
  100% {
    -webkit-transform: translate(-5px, 5px) rotate(30deg);
    -moz-transform: translate(-5px, 5px) rotate(30deg);
    -o-transform: translate(-5px, 5px) rotate(30deg);
    -ms-transform: translate(-5px, 5px) rotate(30deg);
    transform: translate(-5px, 5px) rotate(30deg);
  }
}

@-webkit-keyframes fall {
  100% {
    -webkit-transform: translate(-5px, 5px) rotate(30deg);
    -moz-transform: translate(-5px, 5px) rotate(30deg);
    -o-transform: translate(-5px, 5px) rotate(30deg);
    -ms-transform: translate(-5px, 5px) rotate(30deg);
    transform: translate(-5px, 5px) rotate(30deg);
  }
}

@media (min-width: 1200px) {
  .col-xl-8 {
    flex: none;
    max-width: 100%;
  }
}

@media (max-width: 998px) {
  .main-slider .slide-content .big-title {
    font-size: 40px;
  }
}

@media (max-width: 767px) {
  .main-menu .navigation li a {
    position: relative;
    display: block;
    color: #ffffff;
    font-size: 14px;
    line-height: 0px;
    font-weight: 900;
    text-transform: uppercase;
    opacity: 1;
    padding: 25px 18px 21px;
    transition: all 300ms ease;
    font-family: 'Roboto', sans-serif;
  }

  .main-slider .slide-content .big-title {
    font-size: 25px;
  }

  .choose-carousel.owl-carousel .owl-nav [class*="owl-"] {
    display: none;
  }

  .mainmenu-area.stricky-fixed {
    border-radius: 10px;
  }

  .txt-12 {
    font-size: 12px;
  }

  .mission-vision-content .overlay-content .text {
    padding-top: 0px;
  }

  .contact-form-text-box {
    display: none;
  }
}
