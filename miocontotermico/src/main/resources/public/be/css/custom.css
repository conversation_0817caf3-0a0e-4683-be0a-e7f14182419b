.wizard>.steps>ul>li.current .number {    
    border-color: #4caf50;
    color: #4caf50;
}
.wizard>.steps>ul>li.done .number {    
    background-color: #4caf50;
    border-color: #4caf50;    
}
.wizard>.steps>ul>li:after, .wizard>.steps>ul>li:before {    
    background-color: #4caf50;
}
.wizard>.actions>ul>li>a {
    background: #4caf50;
}
.form-control:focus {
    outline: 0;
    border-color: transparent;
    border-bottom-color: #4caf50;
    -webkit-box-shadow: 0 1px 0 #4caf50;
    box-shadow: 0 1px 0 #4caf50;
}
.dropdown-menu>.active>a, .dropdown-menu>.active>a:focus, .dropdown-menu>.active>a:hover {
    color: #fff;
    text-decoration: none;
    outline: 0;
    background-color: #4caf50;
}
.file-drop-area label {
    display: block;
    padding: 2em;
    background: rgba(3,155,229,.05);
    text-align: center;
    cursor: pointer;
    border: 1px dashed #039be5;
    font-size: 16px;
    color: #039be5;
    border-radius: 10px
}

.drop_uploader.drop_zone .text {
    font-family: Roboto,Helvetica Neue,Helvetica,Arial,sans-serif;
    font-size: 16px;
    color: #039be5
}

.drop_uploader .fa {
    display: none
}

.drop_uploader.drop_zone ul.files li {
    font-family: Roboto,Helvetica Neue,Helvetica,Arial,sans-serif;
    font-size: 14px;
    color: #039be5;
    background-color: transparent;
    border: none
}

.drop_uploader.drop_zone {
    position: relative;
    border: 1px dashed #039be5;
    color: #039be5;
    background: rgba(3,155,229,.05);
    border-radius: 10px;
    min-height: 0
}

.file_browse {
    position: absolute;
    border-color: transparent!important;
    background-color: transparent!important;
    color: transparent!important;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0
}
@media (max-width: 768px) {
    .nav-tabs.nav-justified>li.active>a,.nav-tabs.nav-justified>li.active>a:focus,.nav-tabs.nav-justified>li.active>a:hover {
        border-width:0 0 0 2px;
        border-left-color: #4caf50
    }
}

@media (min-width: 769px) {

    .nav-tabs.nav-tabs-highlight>li.active>a,.nav-tabs.nav-tabs-highlight>li.active>a:focus,.nav-tabs.nav-tabs-highlight>li.active>a:hover {
        border-top-color: #4caf50
    }
}
legend .control-arrow {    
    width: 100%;
    position: absolute;
    right: 20px;
    text-align: right;
}
.property-thumb {
    width: 90px;
    height: 90px;
}
.vetrina {
    background-size: cover;
    background-position: 50% 50%;
}
.media-vetrina {
    display: block;
    width: 100%;
}
.semi-transparent {
    opacity: 0.5;
}
.width-130 {
    width: 130px;
}
.datatable-scroll-wrap {
    overflow-x: visible;
}
.truncate {
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.jconfirm.jconfirm-supervan .jconfirm-bg {
    background-color: rgba(0, 0, 0, 0.7);
}
.jconfirm.jconfirm-supervan .jconfirm-box .jconfirm-buttons button {
    font-family: "Roboto", Helvetica Neue, Helvetica, Arial, sans-serif;
    display: inline-block;
    margin-bottom: 0;
    font-weight: normal;
    text-align: center;
    vertical-align: middle;
    touch-action: manipulation;
    cursor: pointer;
    background-image: none;
    border: 1px solid transparent;
    white-space: nowrap;
    padding: 8px 16px;
    font-size: 13px;
    line-height: 1.5384616;
    border-radius: 3px;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;   
    font-weight: 500;
    text-transform: uppercase;
    border-width: 0;
    padding: 9px 17px;
}
.jconfirm.jconfirm-supervan .jconfirm-box .jconfirm-buttons button:hover {
    transform: none;
    -webkit-transform: none;
}
@media (max-width: 768px) {
    .property-thumb {
        width: 45px;
        height: 45px;
    }
    .panel-footer > .heading-elements:not(.not-collapsible) > .pull-right {
        float: right!important;
    }
    .panel-footer > .heading-elements:not(.not-collapsible) > .pull-right h3 {
        margin-bottom: 0!important;
        margin-top: 20px!important;
    }
}
.file-drop-area label {
    display: block;
    padding: 2em;
    background: rgba(251, 140, 0, 0.07);
    text-align: center;
    cursor: pointer;
    border: 1px dashed #FB8C00;
    font-size: 16px;
    color: #FB8C00;
    border-radius: 10px;
}
.slim {
    background-color: #fff !important;
}
.slim .slim-file-hopper {
    background: rgba(0,0,0,0.1) !important;
    border-radius: 6px;
}
.slim-item-multiple {
    cursor: move;
    cursor: grab;
    cursor: -moz-grab;
    cursor: -webkit-grab;
    border: 10px solid white;
    border-radius: 10px;
    padding-bottom: 0 !important;
}
.slim-item-multiple:active { 
    cursor: grabbing;
    cursor: -moz-grabbing;
    cursor: -webkit-grabbing;
}
.autocomplete-suggestions{text-align:left;cursor:default;border:2px solid #d9d9d9;border-top:0;background:#fff;box-shadow:0 2px 6px rgba(0,0,0,.3);;position:absolute;display:none;z-index:9999;max-height:254px;overflow:hidden;overflow-y:auto;box-sizing:border-box}.autocomplete-suggestion{position:relative;padding:10px 20px 10px 20px;line-height:30px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;font-size:18px;color:#999}.autocomplete-suggestion b{font-weight:400;color:#000}.autocomplete-suggestion.selected{background:#f0f0f0}

.bg-gold {
    background-color: #f5c542;
    border-color: #f5c542;
    color: #fff;
}
.bg-silver {
    background-color: #d6d5d2;
    border-color: #d6d5d2;
    color: #fff;
}
.bg-bronze {
    background-color: #b0691c;
    border-color: #b0691c;
    color: #fff;
}
.discount-price {
    font-size: 15px;
    text-decoration: line-through;
}

/* Procedure expiration row styling */
.procedure-expired {
    background-color: #ffebee !important;
}

.procedure-near-expiration {
    background-color: #fff3e0 !important;
}

.procedure-expired td {
    background-color: #ffebee !important;
}

.procedure-near-expiration td {
    background-color: #fff3e0 !important;
}