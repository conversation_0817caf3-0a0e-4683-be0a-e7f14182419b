$(function() {

    // Lunghezza campi
    // -------------------------
    $('.maxlength').maxlength({
        alwaysShow: true,
        placement: 'top-right'
    });

    // Autosize Descrizione
    // -------------------------
    autosize($('.elastic'));

    // Default initialization
    $(".styled, .multiselect-container input").uniform({
        radioClass: 'choice'
    }); 
    
    // Select with search
    $('.select-search').select2();

    // Dropdown
    $('.select').select2({
        minimumResultsForSearch: Infinity
    });

    // Switch
    // -------------------------
    $(".switchery").each(function() {
        if ($(this).val() === 'true') {
            $(this).attr("checked", "checked");
        }
    });

    if (Array.prototype.forEach) {
        var elems = Array.prototype.slice.call(document.querySelectorAll('.switchery'));
        elems.forEach(function(html) {
            var switchery = new Switchery(html);
        });
    }
    else {
        var elems = document.querySelectorAll('.switchery');

        for (var i = 0; i < elems.length; i++) {
            var switchery = new Switchery(elems[i]);
        }
    }
    
    // Initialize validation
    $(".form-horizontal").validate({
        ignore: 'input[type=hidden], .select2-search__field', // ignore hidden fields
        errorClass: 'validation-error-label',
        successClass: 'validation-valid-label',
        highlight: function (element, errorClass) {
            $(element).removeClass(errorClass);
        },
        unhighlight: function (element, errorClass) {
            $(element).removeClass(errorClass);
        },
        // Different components require proper error label placement
        errorPlacement: function (error, element) {

            // Styled checkboxes, radios, bootstrap switch
            if (element.parents('div').hasClass("checker") || element.parents('div').hasClass("choice") || element.parent().hasClass('bootstrap-switch-container')) {
                if (element.parents('label').hasClass('checkbox-inline') || element.parents('label').hasClass('radio-inline')) {
                    error.appendTo(element.parent().parent().parent().parent());
                } else {
                    error.appendTo(element.parent().parent().parent().parent().parent());
                }
            }

            // Unstyled checkboxes, radios
            else if (element.parents('div').hasClass('checkbox') || element.parents('div').hasClass('radio')) {
                error.appendTo(element.parent().parent().parent());
            }

            // Input with icons and Select2
            else if (element.parents('div').hasClass('has-feedback') || element.hasClass('select2-hidden-accessible')) {
                error.appendTo(element.parent());
            }

            // Inline checkboxes, radios
            else if (element.parents('label').hasClass('checkbox-inline') || element.parents('label').hasClass('radio-inline')) {
                error.appendTo(element.parent().parent());
            }

            // Input group, styled file input
            else if (element.parent().hasClass('uploader') || element.parents().hasClass('input-group')) {
                error.appendTo(element.parent().parent());
            } else {
                error.insertAfter(element);
            }
        },
        rules: {
            email: {
                email: true
            }
        }
    });

    $("#paperwork-form").on('submit', function(e) {        
        $(this).find('input[type="checkbox"]').each( function () {
            var checkbox = $(this);
            if( checkbox.is(':checked')) {
                checkbox.attr('value','1');
            } else {
                checkbox.after().append(checkbox.clone().attr({type:'hidden', value:0}));
                checkbox.prop('disabled', true);
            }
        });
    });
    
    $('.btn-cancel').click(function () {
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-cancel-square icon-2x"></i><br/><br/> Sei sicuro?',
            content: "Le modifiche andranno perse.",
            buttons: {
                annulla: {
                    btnClass: 'btn-default',
                    action: function () {
                        //
                    }
                },
                conferma: {
                    btnClass: 'bg-danger-700',
                    action: function () {
                        window.location.href = $('#paperworksUri').attr('href');
                    }
                }
            }
        });
    });	
    
    // bind delete action
    $('#btn-delete').click(function (event) {

        // disable the default form submission
        event.preventDefault();

        var postToUrl = $('#paperworkRemoveUri').attr('href');
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-trash icon-2x"></i><br/><br/> Rimozione documento',
            content: "Procedere?",
            columnClass:"col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
            buttons: {
                Annulla: {
                    text: 'Anuulla',
                    btnClass: 'btn-default mb-5',
                    action: function () {
                        //close
                    }
                },
                Conferma: {
                    text: 'Conferma',
                    btnClass: 'bg-danger-600 mb-5',
                    action: function () {
                        $.blockUI();
                        $.ajax({
                            url: postToUrl,
                            type: 'POST',
                            data: null,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success:
                                    function (returndata) {
                                        $.unblockUI();
                                        var url = $("#paperworksSuccessUri").attr("href");
                                        window.location.href = url;
                                    },
                            error:
                                function (response, status, errorThrown) {
                                    $.unblockUI();
                                    // warn
                                    $.alert({
                                        theme: 'supervan',
                                        escapeKey: true,
                                        animation: 'top',
                                        closeAnimation: 'bottom',
                                        backgroundDismiss: true,
                                        title: 'Oh oh! :(',
                                        content: 'Impossibile rimuovere i dati.<br/>'
                                    });
                                }
                        });                    
                    }                    
                }
            }
        });        

        return false;
    });    

    
    // Drop Uploader
    // ------------------------------
    $('input[attachment=true]').drop_uploader({
        uploader_text: $('#uploader-text').text(),
        browse_text: 'browse',
        browse_css_class: '',
        browse_css_selector: 'file_browse',
        uploader_icon: '<i class="fa fa-upload fa-2x"></i>',
        file_icon: '<i class="fa fa-file-o"></i>',
        time_show_errors: 5,
        layout: 'list',
        method: 'normal',
        url: '',
        delete_url: ''
    });
    bindPaperworkFileRemove();
    
    
});

function bindPaperworkFileRemove() {
    $('.delete-fileid').click(function (event){
        event.preventDefault();
        
        var postToUrl = $(this).attr('href');
        var fileId = $(this).attr('fileid');
        var paperworkId = $(this).attr('paperworkId');
        
        if (postToUrl && fileId && paperworkId) {
            
            var url = new URI(postToUrl);
            url.removeSearch("paperworkId");
            url.addSearch("paperworkId", paperworkId);
            url.removeSearch("fileId");
            url.addSearch("fileId", fileId);
            
            $.confirm({
                theme: 'supervan',
                escapeKey: true,
                animation: 'top',
                closeAnimation: 'bottom',
                backgroundDismiss: true,
                title: '<i class="icon-checkmark icon-2x"></i><br/><br/> Aggiornamento documenti',
                content: "Confermi?",
                columnClass:"col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
                buttons: {
                    annulla: {
                        text: 'Annulla',
                        btnClass: 'btn-default mb-5',
                        action: function () {
                            //close
                        }
                    },
                    conferma: {
                        text: 'Conferma',
                        btnClass: 'btn-success mb-5',
                        action: function () {
                            $.blockUI();
                            $.ajax({
                                url: url,
                                type: 'POST',
                                //data: formData,
                                cache: false,
                                contentType: false,
                                processData: false,
                                success:
                                    function (returndata) {
                                        $.unblockUI();
                                        window.location.reload();
                                    },
                                error:
                                    function (response, status, errorThrown) {
                                        $.unblockUI();
                                    }
                            });                    
                        }                    
                    }                    
                }
            });        

            return false;
        }
    });  
}