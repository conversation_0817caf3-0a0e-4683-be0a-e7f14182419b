document.addEventListener('DOMContentLoaded', function() {

   
    // Default initialization
    $(".styled, .eneaprocedure-container input").uniform({
        radioClass: 'choice'
    });
    
    $("#filter-form").on('submit', function(event) {
        event.preventDefault();
        
        var selectedStatuses = "";
        $('.man-status-filter:checked:enabled').each(function() {
            selectedStatuses += $(this).attr('man-status-value') + "|";
        });

        var url = new URI();
        url.removeSearch("selectedStatuses");
        if (selectedStatuses) {
            url.addSearch("selectedStatuses", selectedStatuses);
        }

        window.location.href = url;
        
        return false;
    });
    
    bindEneaprocedureSave();
    bindEneaprocedureFileRemove();

    //
    // Wizard with validation
    //

    // Show form
    var form = $(".steps-validation").show();


    // Initialize wizard
    $(".steps-validation").steps({
        headerTag: "h6",
        bodyTag: "fieldset",
        transitionEffect: "fade",
        labels: 
        {
            previous: "INDIETRO",
            next: "CONTINUA",
            finish: "INVIA PRATICA"            
        },
        titleTemplate: '<span class="number">#index#</span> #title#',
        autoFocus: true,
        onStepChanging: function (event, currentIndex, newIndex) {

            // Allways allow previous action even if the current form is not valid!
            if (currentIndex > newIndex) {
                return true;
            }

            // Needed in some cases if the user went back (clean up)
            if (currentIndex < newIndex) {

                // To remove error styles
                form.find(".body:eq(" + newIndex + ") label.error").remove();
                form.find(".body:eq(" + newIndex + ") .error").removeClass("error");
            }

            form.validate().settings.ignore = ":disabled,:hidden";
            return form.valid();
        },
        onFinishing: function (event, currentIndex) {
            form.validate().settings.ignore = ":disabled";
            return form.valid();
        },
        onFinished: function (event, currentIndex) {
            alert("Submitted!");
        }
    });

    // Initialize validation
    $(".steps-validation").validate({
        ignore: 'input[type=hidden], .select2-search__field', // ignore hidden fields
        errorClass: 'validation-error-label',
        successClass: 'validation-valid-label',
        highlight: function(element, errorClass) {
            $(element).removeClass(errorClass);
        },
        unhighlight: function(element, errorClass) {
            $(element).removeClass(errorClass);
        },

        // Different components require proper error label placement
        errorPlacement: function(error, element) {

            // Styled checkboxes, radios, bootstrap switch
            if (element.parents('div').hasClass("checker") || element.parents('div').hasClass("choice") || element.parent().hasClass('bootstrap-switch-container') ) {
                if(element.parents('label').hasClass('checkbox-inline') || element.parents('label').hasClass('radio-inline')) {
                    error.appendTo( element.parent().parent().parent().parent() );
                }
                 else {
                    error.appendTo( element.parent().parent().parent().parent().parent() );
                }
            }

            // Unstyled checkboxes, radios
            else if (element.parents('div').hasClass('checkbox') || element.parents('div').hasClass('radio')) {
                error.appendTo( element.parent().parent().parent() );
            }

            // Input with icons and Select2
            else if (element.parents('div').hasClass('has-feedback') || element.hasClass('select2-hidden-accessible')) {
                error.appendTo( element.parent() );
            }

            // Inline checkboxes, radios
            else if (element.parents('label').hasClass('checkbox-inline') || element.parents('label').hasClass('radio-inline')) {
                error.appendTo( element.parent().parent() );
            }

            // Input group, styled file input
            else if (element.parent().hasClass('uploader') || element.parents().hasClass('input-group')) {
                error.appendTo( element.parent().parent() );
            }

            else {
                error.insertAfter(element);
            }
        },
        rules: {
            email: {
                email: true
            }
        }
    });



    // Initialize plugins
    // ------------------------------

    // Select2 selects
    $('.select').select2();

    // Select with search
    $('.select-search').select2();
    
    // Simple select without search
    $('.select-simple').select2({
        minimumResultsForSearch: Infinity
    });

    // Default initialization
    $(".styled, .eneaprocedure-container input").uniform({
        radioClass: 'choice'
    });

    // Styled file input
    $('.file-styled').uniform({
        fileDefaultText: 'Seleziona un file',
        fileBtnText: 'Carica file',
        fileButtonClass: 'action btn bg-blue'
    });
    
    // Drop Uploader
    // ------------------------------
    $('input[attachment=true]').drop_uploader({
        uploader_text: $('#uploader-text').text(),
        browse_text: 'browse',
        browse_css_class: '',
        browse_css_selector: 'file_browse',
        uploader_icon: '<i class="fa fa-upload fa-2x"></i>',
        file_icon: '<i class="fa fa-file-o"></i>',
        time_show_errors: 5,
        layout: 'list',
        method: 'normal',
        url: '',
        delete_url: ''
    });
    
    // Basic selects
    // ------------------------------

    // Basic select
    $('#select-default').editable({
        prepend: "Nessuno stato",        
        source: [
            {value: 'opened', text: 'Ricevuta'},
            {value: 'processing', text: 'In lavorazione'},
            {value: 'approved', text: 'Approvata'}            
        ],
        display: function(value, sourceData) {
            var colors = {"": "gray", 1: "#2196f3", 2: "#FFB74D", 3: "#4caf50"},
            elem = $.grep(sourceData, function(o){return o.value == value;});

            if(elem.length) {    
                $(this).text(elem[0].text).css("color", colors[value]); 
            }
            else {
                $(this).empty(); 
            }
        }   
    });
});



function bindEneaprocedureUpdate() {

    $('.eneaprocedure-update').click(function (event) {

        // disable the default form submission
        event.preventDefault();

        var postToUrl = $(this).attr('href');
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-checkmark icon-2x"></i><br/><br/> Aggiornamento stato',
            content: "Confermi?",
            columnClass:"col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
            buttons: {
                annulla: {
                    text: 'Annulla',
                    btnClass: 'btn-default mb-5',
                    action: function () {
                        //close
                    }
                },
                conferma: {
                    text: 'Conferma',
                    btnClass: 'btn-success mb-5',
                    action: function () {
                        $.blockUI();
                        $.ajax({
                            url: postToUrl,
                            type: 'POST',
                            data: null,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success:
                                    function (returndata) {
                                        $.unblockUI();
                                        var url = $("#eneaproceduresSuccessUri").attr("href");
                                        window.location.href = url;
                                    },
                            error:
                                function (response, status, errorThrown) {
                                    $.unblockUI();
                                    // warn
                                    $.alert({
                                        theme: 'supervan',
                                        escapeKey: true,
                                        animation: 'top',
                                        closeAnimation: 'bottom',
                                        backgroundDismiss: true,
                                        title: 'Oh oh! :(',
                                        content: 'Aggiornamento non riuscito.<br/>'
                                    });
                                }
                        });                    
                    }                    
                }
            }
        });        

        return false;
    });  
}

function bindEneaprocedureSave() {

    $('.eneaprocedureSave').click(function (event) {

        // disable the default form submission
        event.preventDefault();

        var url = new URI($(this).attr('href'));
        var formData = new FormData($('#form-edit-eneaprocedure')[0]);
    
        if (url && formData) {
//            url.removeSearch("assignedUserId");
//            url.addSearch("assignedUserId", $('#assignedUserId').val());
//            url.removeSearch("status");
//            url.addSearch("status", $('#status').val());
            $.confirm({
                theme: 'supervan',
                escapeKey: true,
                animation: 'top',
                closeAnimation: 'bottom',
                backgroundDismiss: true,
                title: '<i class="icon-checkmark icon-2x"></i><br/><br/> Aggiornamento richiesta',
                content: "Confermi?",
                columnClass:"col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
                buttons: {
                    annulla: {
                        text: 'Annulla',
                        btnClass: 'btn-default mb-5',
                        action: function () {
                            //close
                        }
                    },
                    conferma: {
                        text: 'Conferma',
                        btnClass: 'btn-success mb-5',
                        action: function () {
                            $.blockUI();
                            $.ajax({
                                url: url,
                                type: 'POST',
                                data: formData,
                                cache: false,
                                enctype: 'multipart/form-data',
                                contentType: false,
                                processData: false,
                                success:
                                        function (returndata) {
                                            $.unblockUI();
                                            var url = $("#eneaproceduresSuccessUri").attr("href");
                                            window.location.href = url;
                                        },
                                error:
                                    function (response, status, errorThrown) {
                                        $.unblockUI();
                                        // warn
                                        $.alert({
                                            theme: 'supervan',
                                            escapeKey: true,
                                            animation: 'top',
                                            closeAnimation: 'bottom',
                                            backgroundDismiss: true,
                                            title: 'Oh oh! :(',
                                            content: 'Aggiornamento non riuscito.<br/>'
                                        });
                                    }
                            });                    
                        }                    
                    }
                }
            });        

            return false;
        }
    });  
}

function bindEneaprocedureFileRemove() {
    $('.delete-fileid').click(function (event){
        event.preventDefault();
        
        var postToUrl = $(this).attr('href');
        var listName = $(this).attr('listname');
        var fileId = $(this).attr('fileid');
        var eneaprocedureId = $(this).attr('eneaprocedureId');
        
        if (postToUrl && listName && fileId && eneaprocedureId) {
            
            var url = new URI(postToUrl);
            url.removeSearch("eneaprocedureId");
            url.addSearch("eneaprocedureId", eneaprocedureId);
            url.removeSearch("listName");
            url.addSearch("listName", listName);
            url.removeSearch("fileId");
            url.addSearch("fileId", fileId);
            
            $.confirm({
                theme: 'supervan',
                escapeKey: true,
                animation: 'top',
                closeAnimation: 'bottom',
                backgroundDismiss: true,
                title: '<i class="icon-checkmark icon-2x"></i><br/><br/> Aggiornamento richiesta',
                content: "Confermi?",
                columnClass:"col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
                buttons: {
                    annulla: {
                        text: 'Annulla',
                        btnClass: 'btn-default mb-5',
                        action: function () {
                            //close
                        }
                    },
                    conferma: {
                        text: 'Conferma',
                        btnClass: 'btn-success mb-5',
                        action: function () {
                            $.blockUI();
                            $.ajax({
                                url: url,
                                type: 'POST',
                                //data: formData,
                                cache: false,
                                contentType: false,
                                processData: false,
                                success:
                                    function (returndata) {
                                        $.unblockUI();
                                        window.location.reload();
                                    },
                                error:
                                    function (response, status, errorThrown) {
                                        $.unblockUI();
                                    }
                            });                    
                        }                    
                    }                    
                }
            });        

            return false;
        }
    });  
}