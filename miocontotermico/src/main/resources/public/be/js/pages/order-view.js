$(document).ready(function() {

    history.pushState(null, null, location.href);
    window.onpopstate = function () {
        history.go(1);
    };

    if ($('#receipt-panel')) {
        setInterval(function() {
            if ($('#receipt-panel')) {
                var url = $('#orderViewReceiptPanelUri').attr('href');
                $('#receipt-panel').load(url + ' #receipt-panel-inner', function() {
                    // ...refreshed
                });
            }
        }, 1000);
    }

    $('.receipt-print').click(function(event) {

        event.preventDefault();

        var url = event.target.getAttribute('href');

        $.blockUI();
        $.ajax({
            url: url,
            type: 'POST',
            //data: formData,
            cache: false,
            contentType: false,
            processData: false,
            success:
                    function (returndata) {
                        $.unblockUI();
                        location.reload();
                    },
            error:
                function (response, status, errorThrown) {
                    $.unblockUI();
                    // warn
                    $.alert({
                        theme: 'supervan',
                        escapeKey: true,
                        animation: 'top',
                        closeAnimation: 'bottom',
                        backgroundDismiss: true,
                        title: 'Oh oh! :(',
                        content: 'Stampa scontrino non riuscita.<br/>'
                    });
                }
        });

        return false;
    });

    $('.order-clone').click(function (event) {
        // disable the default form submission
        event.preventDefault();

        var postToUrl = $(this).attr('href');
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class=" icon-checkmark3 icon-2x"></i><br/><br/> Procedo con l\'operazione?',
            content: "",
            columnClass:"col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
            buttons: {
                annulla: {
                    text: 'Annulla',
                    btnClass: 'btn-cancel mb-5',
                    action: function () {
                        //close
                    }
                },
                conferma: {
                    text: 'Conferma',
                    btnClass: 'bg-black mb-5',
                    action: function () {
                        $.blockUI();
                        $.ajax({
                            url: postToUrl,
                            type: 'POST',
                            data: null,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success:
                                    function (returndata) {
                                        $.unblockUI();
                                        var url = $("#ordersCheckout").attr("href");
                                        window.location.href = url;
                                    },
                            error:
                                function (response, status, errorThrown) {
                                    $.unblockUI();
                                    // warn
                                    $.alert({
                                        theme: 'supervan',
                                        escapeKey: true,
                                        animation: 'top',
                                        closeAnimation: 'bottom',
                                        backgroundDismiss: true,
                                        title: 'Oh oh! :(',
                                        content: 'Copia non riuscita.<br/>'
                                    });
                                }
                        });
                    }
                }
            }
        });

        return false;
    });

    $('.order-change').click(function (event) {
        // disable the default form submission
        event.preventDefault();

        var postToUrl = $(this).attr('href');
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class=" icon-checkmark3 icon-2x"></i><br/><br/> Procedo con l\'operazione?</br></br>l\'ordine attuale sarà eliminato!',
            content: "",
            columnClass:"col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
            buttons: {
                annulla: {
                    text: 'Annulla',
                    btnClass: 'btn-cancel mb-5',
                    action: function () {
                        //close
                    }
                },
                conferma: {
                    text: 'Conferma',
                    btnClass: 'bg-black mb-5',
                    action: function () {
                        $.blockUI();
                        $.ajax({
                            url: postToUrl,
                            type: 'POST',
                            data: null,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success:
                                    function (returndata) {
                                        $.unblockUI();
                                        var url = $("#ordersCheckout").attr("href");
                                        window.location.href = url;
                                    },
                            error:
                                function (response, status, errorThrown) {
                                    $.unblockUI();
                                    // warn
                                    $.alert({
                                        theme: 'supervan',
                                        escapeKey: true,
                                        animation: 'top',
                                        closeAnimation: 'bottom',
                                        backgroundDismiss: true,
                                        title: 'Oh oh! :(',
                                        content: 'Modifica non riuscita.<br/>'
                                    });
                                }
                        });
                    }
                }
            }
        });

        return false;
    });

    $('.order-print').click(function (event) {
        // disable the default form submission
        event.preventDefault();

        var postToUrl = $(this).attr('href');
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: "Creazione documento",
            content: "Continuare?",
            columnClass:"col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
            buttons: {
                annulla: {
                    text: 'Annulla',
                    btnClass: 'btn-default mb-5',
                    action: function () {
                        //close
                    }
                },
                conferma: {
                    text: 'Conferma',
                    btnClass: 'bg-black mb-5',
                    action: function () {
                        $.blockUI();
                        $.ajax({
                            url: postToUrl,
                            type: 'POST',
                            data: null,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success:
                                    function (returndata) {
                                        if (returndata) {
                                            $.unblockUI();
                                            var url = $("#ordersPrint").attr("href") + returndata.toString();
                                            window.open(url,'_blank');
                                        }
                                    },
                            error:
                                function (response, status, errorThrown) {
                                    $.unblockUI();
                                    // warn
                                    $.alert({
                                        theme: 'supervan',
                                        escapeKey: true,
                                        animation: 'top',
                                        closeAnimation: 'bottom',
                                        backgroundDismiss: true,
                                        title: 'Oh oh! :(',
                                        content: 'Creazione documento non riuscita.<br/>'
                                    });
                                }
                        });
                    }
                }
            }
        });

        return false;
    });

});
