// init
moment.locale('it');

/**
 * Number.prototype.format(n, x, s, c)
 * 
 * @param integer n: length of decimal
 * @param integer x: length of whole part
 * @param mixed   s: sections delimiter
 * @param mixed   c: decimal delimiter
 */
Number.prototype.format = function(n, x, s, c) {
    var re = '\\d(?=(\\d{' + (x || 3) + '})+' + (n > 0 ? '\\D' : '$') + ')',
        num = this.toFixed(Math.max(0, ~~n));

    return (c ? num.replace('.', c) : num).replace(new RegExp(re, 'g'), '$&' + (s || ','));
};

// globals
var table = null;

$(function() {

    // const
    var ordersDataUri = $("#ordersDataUri").attr("href");
    
    var orderViewUri = $("#orderViewUri").attr("href");
    var userViewUri = $("#userViewUri").attr("href");
    var orderPaymentStatusUpdateUnpaidUri = $("#orderPaymentStatusUpdateUnpaidUri").attr("href");
    var orderStatusUpdateAnnulledUri = $("#orderStatusUpdateAnnulledUri").attr("href");
    var orderPaymentStatusUpdatePaidUri = $("#orderPaymentStatusUpdatePaidUri").attr("href");
    var isAdmin = ($("#isAdmin").text() === 'true');

    // Table setup
    // ------------------------------

    // Setting datatable defaults
    $.extend( $.fn.dataTable.defaults, {
        autoWidth: false,
        responsive: true,        
        dom: '<"datatable-header"fBl><"datatable-scroll-wrap"t><"datatable-footer"ip>',
        language: {
            processing: 'Elaborazione in corso...',
            loadingRecords: 'Caricamento in corso...',
            search: '<span>Filtra:</span> _INPUT_',
            searchPlaceholder: 'Ricerca numero, cliente...',
            lengthMenu: '<span>Mostra:</span> _MENU_',
            emptyTable: 'Non ci sono dati disponibili',
            info:       'Visualizzo da _START_ a _END_ di _TOTAL_ elementi',
            infoEmpty:  'Visualizzo da 0 a 0 di 0 elementi',
            infoFiltered: '(filtrati da _MAX_ elementi totali)',
            zeroRecords: 'Nessun risultato corrisponde alla ricerca',
            paginate: { 'first': 'Prima', 'last': 'Ultima', 'next': '&rarr;', 'previous': '&larr;' }
        },
        drawCallback: function () {
            $(this).find('tbody tr').slice(-3).find('.dropdown, .btn-group').addClass('dropup');
        },
        preDrawCallback: function() {
            $(this).find('tbody tr').slice(-3).find('.dropdown, .btn-group').removeClass('dropup');
        }
    });

    $.extend( $.fn.dataTableExt.oSort, {
        'sortable-value-pre': function ( a ) {
            if (a) {
                var nodes = $.parseHTML(a);
                if (nodes) {
                    var node = nodes[0];
                    if (node) {
                        if (node.hasAttribute('sortable-value')) {
                            a = node.getAttribute('sortable-value');
                        }
                    }
                }
            }
            return a;
        },

        'sortable-value-asc': function ( a, b ) {
            return a - b;
        },

        'sortable-value-desc': function ( a, b ) {
            return b - a;
        }
    } );

    // Default initialization
    $(".styled, .multiselect-container input").uniform({
        radioClass: 'choice'
    });

    // Control position
    var current = new URI();
    var params = current.search(true);
    var firstUri = new URI(ordersDataUri);
    
    var startDate = moment($('#startDate').text());
    if (startDate) {
        firstUri.addSearch("startDate", startDate.format('DD/MM/YYYY'));
    }
    
    var endDate = moment($('#endDate').text());
    if (endDate) {
        firstUri.addSearch("endDate", endDate.format('DD/MM/YYYY'));
    }
    if (params.selectedStatuses) {
        firstUri.addSearch("selectedStatuses", params.selectedStatuses);
    }
    if (params.selectedPaymentStatuses) {
        firstUri.addSearch("selectedPaymentStatuses", params.selectedPaymentStatuses);
    }
    if (params.selectedVendors) {
        firstUri.addSearch("selectedVendors", params.selectedVendors);
    }
    if (params.selectedCities) {
        firstUri.addSearch("selectedCities", params.selectedCities);
    }
    
    table = $('.datatable-responsive-control-right').DataTable({
        processing: true,
        //serverSide: true,
        ajax: firstUri.toString(),
        responsive: {
            details: {
                type: 'column',
                target: -1
            }
        },
        stateSave: true,
        order: [[ 2, "desc" ]],
        buttons: {            
            buttons: [                
                {
                    extend: 'excelHtml5',
                    className: 'btn btn-default',
                    exportOptions: {
                        columns: [0, 1, 2, 3]
                    }
                },
                {
                    extend: 'pdfHtml5',
                    className: 'btn btn-default',
                    exportOptions: {
                        columns: [0, 1, 2, 3]
                    }
                }
            ]
        },
        columnDefs: [
            {
                className: 'control',
                orderable: false,
                targets: -1
            },
            {
                targets: 0,
                data: null,
                render: function (entry, type, row, meta) {
                    
                    var user =  'Non disponibile';
                    if (entry.order.userId && (entry.order.userId.length > 0)) {
                        user =  '<a href="' + userViewUri + entry.order.userId + '">' + name(entry.user) + '</a>' +
                                    '<div class="text-muted text-size-small">' +
                                    '    ' + empty(entry.user.city) + ' (' + empty(entry.user.provinceCode) + ')' +
                                    '</div>'
                        ;
                    }
                    
                    return  user;
                }
            },
            {
                targets: 1,
                data: null,
                render: function (entry, type, row, meta) {
                    
                    var paddedProtocol = ('' + entry.order.protocol).padStart(10, '0');
                    
                    var productReturn = '';
                    if (entry.order.productReturn && (entry.order.productReturn === true)) {
                        productReturn = ' (RESO)';
                    }
                    
                    var rowOrder = '<a href="' + orderViewUri + entry.order._id + '" class="text-bold" sortable-value="' + paddedProtocol + '" camicettasnob-order-id="' + entry.order._id + '">Ordine #' + entry.order.protocol + productReturn + '</a>'
                    if (entry.order.status) {
                        rowOrder += '<div class="text-muted text-size-small">' +
                            '</div>';
                    }

                    return rowOrder;
                },
                orderable: true,
                type: 'sortable-value'
            },
            {
                targets: 2,
                data: null,
                render: function (entry, type, row, meta) {
                    return  '<span sortable-value="' + date(entry.order.date, 'YYYYMMDDHHmm') + '">' + date(entry.order.date, 'DD MMMM YYYY HH:mm') + '</span>';
                },
                orderable: true,
                type: 'sortable-value'
            },
            {
                targets: 3,
                data: null,
                render: function (entry, type, row, meta) {
                    
                    var price = 'n.d.';
                    if (entry.order.finalPrice && entry.order.finalPrice > 0) {
                        price = '€ ' + entry.order.finalPrice.format(2, 13, '.', ',');
                    } else {
                        price = '€ ' + (0).format(2, 13, '.', ',');
                    }
                    
                    var payment = '';
                    if (entry.order.paymentStatus && (entry.order.paymentStatus === 'paid')) {
                        payment = '<span class="label label-flat border-success-700 text-success-700">PAGATO</span>';
                    } else if (entry.order.paymentStatus && (entry.order.paymentStatus === 'installment')) {
                        payment = '<span class="label label-flat border-green-400 text-green-400">ACCONTO</span>';
                    } else {
                        payment = '<span class="label label-flat border-danger-600 text-danger-600">ARRETRATO</span>';
                    }
                    
                    return  '<span class="no-margin text-semibold">' + price + '</span>' +
                            '<div class="text-muted text-size-small">' +
                            '    ' + payment +
                            '</div>'
                    ;
                }
            },
            {
                targets: 4,
                data: null,
                render: function (entry, type, row, meta) {
                    
                    var actions = '';
                    var menu = '';
                    if (isTrue(isAdmin)) {
                        
                        if ((entry.order.status !== 'annulled') && (entry.order.status !== 'delivered')) {
                            actions +=  '<li class="dropdown-header">CAMBIO STATO</li>';

                            if (entry.order.paymentStatus === 'paid') {
                                actions += '<li><a class="payment-status-update" href="' + orderPaymentStatusUpdateUnpaidUri + entry.order._id + '"><span class="label label-flat border-danger-600 text-danger-600 width-full">ARRETRATO</span></a></li>';
                            } else {
                                actions += '<li><a class="payment-status-update" href="' + orderPaymentStatusUpdatePaidUri + entry.order._id + '"><span class="label label-flat border-success-700 text-success-700 width-full">PAGATO</span></a></li>';
                                actions += '<li><a class="status-update" href="' + orderStatusUpdateAnnulledUri + entry.order._id + '"><span class="label label-flat border-danger text-danger width-full">ANNULLA</span></a></li>';
                            }
                        }

                        if (actions && actions.length > 0) {
                            // ?????? @ballanz: lo style="bottom: initial;" è stato messo per evitare che il menù sparisse in alto quanto troppo lungo
                            menu =  '<ul class="icons-list">' +
                                    '    <li class="dropdown">' +
                                    '        <a href="#" class="dropdown-toggle" data-toggle="dropdown">' +
                                    '            <i class="icon-menu9"></i>' +
                                    '        </a>' +
                                    '' +
                                    '        <ul class="dropdown-menu dropdown-menu-right" style="bottom: initial;">' +
                                    '            ' + actions +
                                    '        </ul>' +
                                    '    </li>' +
                                    '</ul>'
                            ;
                        }
                    }
                    return menu;
                    
                },
                width: "100px",
                orderable: false
            },
            {
                /*
                                    <td></td>
                */
                targets: 5,
                data: null,
                render: function (entry, type, row, meta) {
                    return '';
                }
            }
        ]
    });

    table.on( 'draw.dt', function (  e, settings ) {
        bindStatusUpdate();
        bindPaymentStatusUpdate();
    });    
    
    table.on( 'responsive-display', function ( e, datatable, row, showHide, update ) {
        bindStatusUpdate();
        bindPaymentStatusUpdate();
    });
    
    table.on( 'responsive-resize', function ( e, datatable, columns ) {
        bindStatusUpdate();
        bindPaymentStatusUpdate();
    });

    // Enable Select2 select for the length option
    $('.dataTables_length select').select2({
        minimumResultsForSearch: Infinity,
        width: 'auto'
    });

    // Initialize with options
    var startDate = moment($('#startDate').text());
    var endDate = moment($('#endDate').text());
    $('.daterange-predefined').daterangepicker(
        {
            startDate: startDate,
            endDate: endDate,
            minDate: '01/01/2017',
            maxDate: '12/31/2099',
            dateLimit: { days: 366 },
            ranges: {
                'Oggi': [moment(), moment()],
                'Ieri': [moment().subtract(1, 'days'), moment().subtract(1, 'days')],
                'Ultimi 7 giorni': [moment().subtract(6, 'days'), moment()],
                'Ultimi 30 giorni': [moment().subtract(29, 'days'), moment()],
                'Questo mese': [moment().startOf('month'), moment().endOf('month')],
                'Ultimo mese': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
            },
            locale: {
                format: 'DD/MM/YYYY',
                applyLabel: 'Applica',
                cancelLabel: 'Annulla',
                startLabel: 'Data inizio',
                endLabel: 'Data fine',
                customRangeLabel: 'Personalizzato',
                daysOfWeek: ['Do', 'Lu', 'Ma', 'Me', 'Gi', 'Ve','Sa'],
                monthNames: ['Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno', 'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'],
                firstDay: 1
            },
            opens: 'left',
            applyClass: 'btn-small bg-black filter-date',
            cancelClass: 'btn-small btn-default'
        },
        function(start, end) {
            $('.daterange-predefined span').html(start.format('MMMM D, YYYY') + ' &nbsp; - &nbsp; ' + end.format('MMMM D, YYYY'));
            
            var url = new URI(ordersDataUri);
            
            var startDate = start.format('DD/MM/YYYY');
            var endDate = end.format('DD/MM/YYYY');

            var selectedStatuses = "";
            $('.mct-status-filter:checked:enabled').each(function() {
                selectedStatuses += $(this).attr('mct-status-value') + "|";
            });

            var selectedPaymentStatuses = "";
            $('.mct-payment-status-filter:checked:enabled').each(function() {
                selectedPaymentStatuses += $(this).attr('mct-payment-status-value') + "|";
            });

            var selectedVendors = "";
            $('.mct-vendor-filter:checked:enabled').each(function() {
                selectedVendors += $(this).attr('mct-vendor-value') + "|";
            });

            var selectedCities = "";
            $('.mct-city-filter:checked:enabled').each(function() {
                selectedCities += $(this).attr('mct-city-value') + "|";
            });

            url.removeSearch("startDate");
            if (startDate) {
                url.addSearch("startDate", startDate);
            }

            url.removeSearch("endDate");
            if (endDate) {
                url.addSearch("endDate", endDate);
            }
            
            url.removeSearch("selectedStatuses");
            if (selectedStatuses) {
                url.addSearch("selectedStatuses", selectedStatuses);
            }
            
            url.removeSearch("selectedPaymentStatuses");
            if (selectedPaymentStatuses) {
                url.addSearch("selectedPaymentStatuses", selectedPaymentStatuses);
            }
            
            url.removeSearch("selectedVendors");
            if (selectedVendors) {
                url.addSearch("selectedVendors", selectedVendors);
            }
           
            url.removeSearch("selectedCities");
            if (selectedCities) {
                url.addSearch("selectedCities", selectedCities);
            }

            if (table) {
                table.ajax.url(url.toString()).load(function () {
                    bindStatusUpdate();
                    bindPaymentStatusUpdate();
               });
            }
           
        }
    );

    // Display date format
    $('.daterange-predefined span').html(startDate.format('MMMM D, YYYY') + ' &nbsp; - &nbsp; ' + endDate.format('MMMM D, YYYY'));

    // bind filter-apply action (button)
    $("#filter-apply").click(function(event) {

        var url = new URI(ordersDataUri);
        
        var startDate = $('.daterange-predefined').data('daterangepicker').startDate.format('DD/MM/YYYY');
        var endDate = $('.daterange-predefined').data('daterangepicker').endDate.format('DD/MM/YYYY');

        var selectedStatuses = "";
        $('.mct-status-filter:checked:enabled').each(function() {
            selectedStatuses += $(this).attr('mct-status-value') + "|";
        });

        var selectedPaymentStatuses = "";
        $('.mct-payment-status-filter:checked:enabled').each(function() {
            selectedPaymentStatuses += $(this).attr('mct-payment-status-value') + "|";
        });

        var selectedVendors = "";
        $('.mct-vendor-filter:checked:enabled').each(function() {
            selectedVendors += $(this).attr('mct-vendor-value') + "|";
        });

        var selectedCities = "";
        $('.mct-city-filter:checked:enabled').each(function() {
            selectedCities += $(this).attr('mct-city-value') + "|";
        });

        url.removeSearch("startDate");
        if (startDate) {
            url.addSearch("startDate", startDate);
        }

        url.removeSearch("endDate");
        if (endDate) {
            url.addSearch("endDate", endDate);
        }
        
        url.removeSearch("selectedStatuses");
        if (selectedStatuses) {
            url.addSearch("selectedStatuses", selectedStatuses);
        }

        url.removeSearch("selectedPaymentStatuses");
        if (selectedPaymentStatuses) {
            url.addSearch("selectedPaymentStatuses", selectedPaymentStatuses);
        }
        
        url.removeSearch("selectedVendors");
        if (selectedVendors) {
            url.addSearch("selectedVendors", selectedVendors);
        }

        url.removeSearch("selectedCities");
        if (selectedCities) {
            url.addSearch("selectedCities", selectedCities);
        }

        if (table) {
            table.ajax.url(url.toString()).load(function () {
                bindStatusUpdate();
                bindStatusUpdate();
           });
        }
        
        return false;
    });

    $('.orders-print').click(function (event) {
        // disable the default form submission
        event.preventDefault();

        var url = $("#ordersPrintUri").attr("href");
        var data = new FormData();
        data.append('orderIds', orderIds());
        
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: "Creazione stampa ordini",
            content: "Continuare?",
            columnClass:"col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
            buttons: {
                annulla: {
                    text: 'Annulla',
                    btnClass: 'btn-default mb-5',
                    action: function () {
                        //close
                    }
                },
                conferma: {
                    text: 'Conferma',
                    btnClass: 'bg-black mb-5',
                    action: function () {
                        $.blockUI();
                        $.ajax({
                            url: url,
                            type: 'POST',
                            data: data,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success:
                                    function (returndata) {
                                        if (returndata) {
                                            $.unblockUI();
                                            var url = $("#printTempUri").attr("href") + returndata.toString();
                                            window.open(url,'_blank');
                                        }
                                    },
                            error:
                                function (response, status, errorThrown) {
                                    $.unblockUI();
                                    // warn
                                    $.alert({
                                        theme: 'supervan',
                                        escapeKey: true,
                                        animation: 'top',
                                        closeAnimation: 'bottom',
                                        backgroundDismiss: true,
                                        title: 'Oh oh! :(',
                                        content: 'Creazione documento non riuscita.<br/>'
                                    });
                                }
                        });                    
                    }                    
                }
            }
        });        

        return false;        
    });  
});

function orderIds() {
    var data = [];
    
    var rows = table.rows({search: 'applied'});
    var count = rows.count();

    if (count > 0) {
        for (r = 0; r < count; r++) {
            data.push(rows.data()[r].order._id);
        }
    }
    
    return data;
}

function name(entity) {
    
    var result = '*Nome* *Cognome*';
    
    if (entity) {
        // nome / cognome
        if (entity.name && entity.lastname) {
            var parts = [];
            parts.push(entity.name);
            parts.push(entity.lastname);
            result = parts.join(' ');
            result = result.trim();
        }
        if (entity.fullname) {
            result = entity.fullname.trim();
        }
    }
    
    return result;
}

function date(value, format) {
    
    var result = '';
    
    if (!format) {
        format = 'DD MMMM YYYY';
    }
    if (value && (value.length > 0)) {
        result = moment.utc(value).format(format);
    }
    
    return result;
}

function empty(value) {
    return value ? value : '';
}

function isTrue(value) {
    return value && (value === true);
}

function isFalse(value) {
    return !isTrue(value);
}


function bindStatusUpdate() {
    $('.status-update').off();
    $('.status-update').click(function(event) {
        
        var url = $(this).attr('href');
        
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-checkmark icon-2x"></i><br/><br/> Aggiornamento stato!',
            content: "Confermi?",
            columnClass:"col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
            buttons: {
                annulla: {
                    text: 'Annulla',
                    btnClass: 'btn-default mb-5',
                    action: function () {
                        //close
                    }
                },
                conferma: {
                    text: 'Conferma',
                    btnClass: 'bg-dark-blue mb-5',
                    action: function () {
                        $.blockUI();
                        $.ajax({
                            url: url,
                            type: 'POST',
                            data: null,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success:
                                    function (returndata) {
                                        $.unblockUI();
                                        if (table) {
                                            table.ajax.reload( function () {
                                                bindStatusUpdate();
                                                bindPaymentStatusUpdate();
                                            }, false);
                                        }
                                    },
                            error:
                                function (response, status, errorThrown) {
                                    $.unblockUI();
                                    // warn
                                    $.alert({
                                        theme: 'supervan',
                                        escapeKey: true,
                                        animation: 'top',
                                        closeAnimation: 'bottom',
                                        backgroundDismiss: true,
                                        title: 'Oh oh! :(',
                                        content: 'Aggiornamento non riuscito.<br/>'
                                    });
                                }
                        });
                    }
                }
            }
        });

        return false;
    });
}


function bindPaymentStatusUpdate() {
    $('.payment-status-update').off();
    $('.payment-status-update').click(function(event) {
        var url = $(this).attr('href');
        
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-checkmark icon-2x"></i><br/><br/> Aggiornamento stato pagamento!',
            content: "Confermi?",
            columnClass:"col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
            buttons: {
                annulla: {
                    text: 'Annulla',
                    btnClass: 'btn-default mb-5',
                    action: function () {
                        //close
                    }
                },
                conferma: {
                    text: 'Conferma',
                    btnClass: 'bg-dark-blue mb-5',
                    action: function () {
                        $.blockUI();
                        $.ajax({
                            url: url,
                            type: 'POST',
                            data: null,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success:
                                    function (returndata) {
                                        $.unblockUI();
                                        if (table) {
                                            table.ajax.reload( function () {
                                                bindStatusUpdate();
                                                bindPaymentStatusUpdate();
                                            }, false);
                                        }
                                    },
                            error:
                                function (response, status, errorThrown) {
                                    $.unblockUI();
                                    // warn
                                    $.alert({
                                        theme: 'supervan',
                                        escapeKey: true,
                                        animation: 'top',
                                        closeAnimation: 'bottom',
                                        backgroundDismiss: true,
                                        title: 'Oh oh! :(',
                                        content: 'Aggiornamento non riuscito.<br/>'
                                    });
                                }
                        });
                    }
                }
            }
        });

        return false;
    });
}

function isTrue(value) {
    return value && (value === true);
}

function isFalse(value) {
    return !isTrue(value);
}