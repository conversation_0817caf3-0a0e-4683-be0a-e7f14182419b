$(function() {

    // Table setup
    // ------------------------------

    // Setting datatable defaults
    $.extend( $.fn.dataTable.defaults, {
        autoWidth: false,
        responsive: true,
        dom: '<"datatable-header"fBl><"datatable-scroll-wrap"t><"datatable-footer"ip>',
        language: {
            search: '<span>Filtra:</span> _INPUT_',
            searchPlaceholder: 'Filtra utenti...',
            lengthMenu: '<span>Mostra:</span> _MENU_',
            emptyTable: 'Non ci sono dati disponibili',
            info:       'Visualizzo da _START_ a _END_ di _TOTAL_ elementi',
            infoEmpty:  'Visualizzo da 0 a 0 di 0 elementi',
            infoFiltered: '(filtrati da _MAX_ elementi totali)',            
            paginate: { 'first': 'Prima', 'last': 'Ultima', 'next': '&rarr;', 'previous': '&larr;' }
        },        
        drawCallback: function () {
            $(this).find('tbody tr').slice(-3).find('.dropdown, .btn-group').addClass('dropup');
        },
        preDrawCallback: function() {
            $(this).find('tbody tr').slice(-3).find('.dropdown, .btn-group').removeClass('dropup');
        }
    });      
    
    bindUserUpdate();
    
    // State saving
    var table = $('.datatable-users').DataTable({
        responsive: {
            details: {
                type: 'column',
                target: -1
            }
        },
        buttons: [
            {
                extend: 'colvis',
                text: '<i class="icon-grid3"></i> <span class="caret"></span>',
                className: 'btn bg-success-600 btn-icon'
            }
        ],
        stateSave: true,        
        columnDefs: [
            {
                className: 'control',
                orderable: false,
                targets: -1
            },
            {
                type: 'sortable-value',
                orderable: true,
                targets: 3
            }
        ],
        order: [3, 'desc']
    });       
    
    table.on( 'responsive-display', function ( e, datatable, row, showHide, update ) {
        bindUserUpdate();        
    });
    
    table.on( 'responsive-resize', function ( e, datatable, columns ) {
        bindUserUpdate();        
    });
        // 
  // External table additions
    // ------------------------------
    
    // Launch Uniform styling for checkboxes
    $('.ColVis_Button').addClass('btn btn-primary btn-icon').on('click mouseover', function() {
        $('.ColVis_collection input').uniform();
    });

    // Enable Select2 select for the length option
    $('.dataTables_length select').select2({
        minimumResultsForSearch: Infinity,
        width: 'auto'
    });
    
    // Default initialization
    $(".styled, .multiselect-container input").uniform({
        radioClass: 'choice'
    });
  
});

function bindUserUpdate() {    
    // bind delete action
    $('.user-remove').click(function (event) {

        // disable the default form submission
        event.preventDefault();

        var postToUrl = $(this).attr('href');
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-checkmark icon-2x"></i><br/><br/> Eliminazione utente!',
            content: "Conferma eliminazione?",
            columnClass:"col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
            buttons: {
                annulla: {
                    text: 'Annulla',
                    btnClass: 'btn-default mb-5',
                    action: function () {
                        //close
                    }
                },
                conferma: {
                    text: 'Conferma',
                    btnClass: 'bg-black mb-5',
                    action: function () {
                        $.blockUI()
                        $.ajax({
                            url: postToUrl,
                            type: 'POST',
                            data: null,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success:
                                    function (returndata) {
                                        $.unblockUI();
                                        var url = $("#usersSuccessUri").attr("href");
                                        window.location.href = url;
                                    },
                            error:
                                function (response, status, errorThrown) {
                                    $.unblockUI();
                                    // warn
                                    $.alert({
                                        theme: 'supervan',
                                        escapeKey: true,
                                        animation: 'top',
                                        closeAnimation: 'bottom',
                                        backgroundDismiss: true,
                                        title: 'Oh oh! :(',
                                        content: 'Eliminazione non riuscita.<br/>'
                                    });
                                }
                        });                    
                    }                    
                }
            }
        });        

        return false;
    });
}       
