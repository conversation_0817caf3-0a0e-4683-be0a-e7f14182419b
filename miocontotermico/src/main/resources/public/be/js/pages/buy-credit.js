$(function() {
    // init login
    bindConfirmPayment();
    bindPackAdd();
    bindCreditChange();
    
});

function bindConfirmPayment() {
    $('.buy-confirm').off();
    $('.buy-confirm').click(function(event) {
        
        var numCredit = $('#numCredit').val();
        if (numCredit > 0) {
            $.confirm({
                theme: 'supervan',
                escapeKey: true,
                closeAnimation: 'bottom',
                backgroundDismiss: true,
                title: '<i class="icon-checkmark4 icon-2x"></i><br/><br/> Conferma ordine!',
                content:'Cliccando su OK si procederà con l\'acquisto dei crediti',
                buttons: {
                    ok: {
                        btnClass: 'btn-default',
                        action: function () {
                            // prepare call
                            var url = new URI($('#buyCreditSendUri').attr('href'));

                            url.removeSearch("numCredit");
                            url.addSearch("numCredit", numCredit);
                            url.addSearch("payment", $("input[type='radio'][name='payment']:checked").val());

                            if ($('#numCredit').val() > 0) {
                                $.blockUI();
                                $.ajax({
                                    url: url,
                                    type: 'POST',
                                    data: null,
                                    cache: false,
                                    contentType: false,
                                    processData: false,
                                    success:
                                            function (returndata) {
                                                $.unblockUI();
                                                if (returndata && (returndata !== 'ok')) {
                                                    var url = new URI(returndata);
                                                }
                                                window.location.href = url;
                                            },
                                    error:
                                            function (response, status, errorThrown) {
                                                $.unblockUI();

                                                var msg = "Errore durante il salvataggio dei dati";

                                                // warn
                                                $.alert({
                                                    theme: 'supervan',
                                                    escapeKey: true,
                                                    animation: 'top',
                                                    closeAnimation: 'bottom',
                                                    backgroundDismiss: true,
                                                    title: 'Oh oh! :(',
                                                    content: msg + '<br/>'
                                                });
                                            }
                                });
                            } else {
                                // warn
                                $.alert({
                                    theme: 'supervan',
                                    escapeKey: true,
                                    animation: 'top',
                                    closeAnimation: 'bottom',
                                    backgroundDismiss: true,
                                    title: 'Oh oh! :(',
                                    content: 'Numero crediti non valido<br/>'
                                });
                            }
                        }
                    },
                    annulla: {
                        btnClass: 'btn-danger',
                        action: function () {
                            //
                        }
                    }

                }
            });
        } else {
            // warn
            $.alert({
                theme: 'supervan',
                escapeKey: true,
                animation: 'top',
                closeAnimation: 'bottom',
                backgroundDismiss: true,
                title: 'Oh oh! :(',
                content: 'Numero crediti non valido<br/>'
            });
        }
        
    });

}
function bindPackAdd() {
    $('.pack-add').off();
    $('.pack-add').click(function(event) {
        
        var quantity = $(this).attr('data-quantity');
        $("#numCredit").val(quantity);
        let numCredit = $('#numCredit').val();
        let totalPrice = 0;
        let totalNetPrice = 0;
        if (numCredit > 0) {
            if (numCredit >= 15000) {
                totalNetPrice = numCredit * 0.92;
            } else if (numCredit >= 10000) {
                totalNetPrice = numCredit * 0.95;
            } else if (numCredit >= 5000) {
                totalNetPrice = numCredit * 0.98;
            } else {
                totalNetPrice = numCredit * 1;
            }
        }
        totalPrice = totalNetPrice * 1.22;
        
        let outputPrice = $('#priceVatIncluded');
        outputPrice.val(totalPrice.toFixed(2));
        
        let outputNetPrice = $('#priceVatNotIncluded');
        outputNetPrice.val(totalNetPrice.toFixed(2));        
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-checkmark4 icon-2x"></i><br/><br/> Pacchetto selezionato!',
            content:'',
            buttons: {
                ok: {
                    btnClass: 'btn-default',
                    action: function () {
                        //
                    }
                }
            }
        });
    });

}

function bindCreditChange() {
    $('#numCredit').on('change', function() {
        let numCredit = $('#numCredit').val();
        let totalPrice = 0;
        let totalNetPrice = 0;
        if (numCredit > 0) {
            if (numCredit >= 15000) {
                totalNetPrice = numCredit * 0.92;
            } else if (numCredit >= 10000) {
                totalNetPrice = numCredit * 0.95;
            } else if (numCredit >= 5000) {
                totalNetPrice = numCredit * 0.98;
            } else {
                totalNetPrice = numCredit * 1;
            }
        }
        totalPrice = totalNetPrice * 1.22;
        
        let outputPrice = $('#priceVatIncluded');
        outputPrice.val(totalPrice.toFixed(2));
        
        let outputNetPrice = $('#priceVatNotIncluded');
        outputNetPrice.val(totalNetPrice.toFixed(2));
        
    });
}