var tab = '#highlighted-justified-tab1';
document.addEventListener('DOMContentLoaded', function() {
    tab = $('#tab').val();
    if (!tab) {
        tab = '#highlighted-justified-tab1';
    }
    $('.tabApplication').click(function () {
        tab = $(this).attr('href');
        $('#tab').val(tab);
    });  
    //
    // Wizard with validation
    //
    // Show form
    var form = $(".steps-validation").show();

    // current step
    var step = parseInt($('#step').val());
    if (!step) {
        step = 0;
    }
    
    // Initialize wizard
    $(".steps-validation").steps({
        headerTag: "h6",
        bodyTag: "fieldset",
        transitionEffect: "fade",
        labels: 
        {
            previous: "INDIETRO",
            next: "CONTINUA",
            finish: "INVIA PRATICA"            
        },
        startIndex: step,
        titleTemplate: '<span class="number">#index#</span> #title#',
        autoFocus: true,
        onStepChanging: function (event, currentIndex, newIndex) {
            
            // Allways allow previous action even if the current form is not valid!
            if (currentIndex > newIndex) {
                return true;
            }

            // Needed in some cases if the user went back (clean up)
            if (currentIndex < newIndex) {

                // To remove error styles
                form.find(".body:eq(" + newIndex + ") label.error").remove();
                form.find(".body:eq(" + newIndex + ") .error").removeClass("error");
            }

            form.validate().settings.ignore = ":disabled,:hidden";
            
            saveInfo();
            
            var valid = true;
            if (currentIndex === 0) {
                // ...
            } else if (currentIndex === 1) {
                $('input[type="file"][required]').each(function () {
                    if ($($(this).closest('div[id*=drop_uploader_]')[0]).is(':visible')) {
                        if (!$(this).val()) {
                            //console.log('name = ' + $(this).attr('name'));
                            valid &= false;
                            $.alert({
                                theme: 'supervan',
                                escapeKey: true,
                                animation: 'top',
                                closeAnimation: 'bottom',
                                backgroundDismiss: true,
                                title: 'Oh oh! :(',
                                content: 'Inserire allegati obbligatori <br/>'
                            });
                            return false;
                        }
                    }
                });
            } else if (currentIndex === 2) {
                // ...
            } else {
                // ...
            }
            valid &= form.valid();
            $('.tabApplication').click(function () {
                tab = $(this).attr('href');
                $('#tab').val(tab);
            });             
            return valid;
        },
        onStepChanged: function (event, currentIndex, priorIndex) {
            $('.tabApplication').click(function () {
                tab = $(this).attr('href');
                $('#tab').val(tab);
            });            
            $('#step').val(currentIndex);
        },
        onFinishing: function (event, currentIndex) {
            form.validate().settings.ignore = ":disabled,:hidden";
            
            saveInfo();
            
            var valid = true;
            if (currentIndex === 0) {
                // ...
            } else if (currentIndex === 1) {
                // ...
            } else if (currentIndex === 2) {
                // ...
            } else {
                // ...
            }
            $('.tabApplication').click(function () {
                tab = $(this).attr('href');
                $('#tab').val(tab);
            });  
            valid &= form.valid();
            return valid;
        },
        onFinished: function (event, currentIndex) {
            $('#step').val(currentIndex);
            $('.tabApplication').click(function () {
                tab = $(this).attr('href');
                $('#tab').val(tab);
            });  
            save();
        }
    });

    // Initialize validation
    $(".steps-validation").validate({
        ignore: 'input[type=hidden], .select2-search__field', // ignore hidden fields
        errorClass: 'validation-error-label',
        successClass: 'validation-valid-label',
        highlight: function(element, errorClass) {
            $(element).removeClass(errorClass);
        },
        unhighlight: function(element, errorClass) {
            $(element).removeClass(errorClass);
        },

        // Different components require proper error label placement
        errorPlacement: function(error, element) {

            // Styled checkboxes, radios, bootstrap switch
            if (element.parents('div').hasClass("checker") || element.parents('div').hasClass("choice") || element.parent().hasClass('bootstrap-switch-container') ) {
                if(element.parents('label').hasClass('checkbox-inline') || element.parents('label').hasClass('radio-inline')) {
                    error.appendTo( element.parent().parent().parent().parent() );
                }
                 else {
                    error.appendTo( element.parent().parent().parent().parent().parent() );
                }
            }

            // Unstyled checkboxes, radios
            else if (element.parents('div').hasClass('checkbox') || element.parents('div').hasClass('radio')) {
                error.appendTo( element.parent().parent().parent() );
            }

            // Input with icons and Select2
            else if (element.parents('div').hasClass('has-feedback') || element.hasClass('select2-hidden-accessible')) {
                error.appendTo( element.parent() );
            }

            // Inline checkboxes, radios
            else if (element.parents('label').hasClass('checkbox-inline') || element.parents('label').hasClass('radio-inline')) {
                error.appendTo( element.parent().parent() );
            }

            // Input group, styled file input
            else if (element.parent().hasClass('uploader') || element.parents().hasClass('input-group')) {
                error.appendTo( element.parent().parent() );
            }

            else {
                error.insertAfter(element);
            }
        },
        rules: {
            email: {
                email: true
            }
        }
    });

    // Initialize plugins
    // ------------------------------

    // Select2 selects
    $('.select').select2();

    // Select with search
    $('.select-search').select2();
    
    // Simple select without search
    $('.select-simple').select2({
        minimumResultsForSearch: Infinity
    });

    // Default initialization
    $(".styled, .multiselect-container input").uniform({
        radioClass: 'choice'
    });

    // Styled file input
    $('.file-styled').uniform({
        fileDefaultText: 'Seleziona un file',
        fileBtnText: 'Carica file',
        fileButtonClass: 'action btn bg-blue'
    });
    
    // Drop Uploader
    // ------------------------------
    $('input[attachment=true]').drop_uploader({
        uploader_text: $('#uploader-text').text(),
        browse_text: 'browse',
        browse_css_class: '',
        browse_css_selector: 'file_browse',
        uploader_icon: '<i class="fa fa-upload fa-2x"></i>',
        file_icon: '<i class="fa fa-file-o"></i>',
        time_show_errors: 5,
        layout: 'list',
        method: 'normal',
        url: '',
        delete_url: ''
    });
    
    // Basic selects
    // ------------------------------

    // Basic select
    $('#select-default').editable({
        prepend: "Nessuno stato",        
        source: [
            {value: 1, text: 'Ricevuta'},
            {value: 2, text: 'In lavorazione'},
            {value: 3, text: 'Approvata'}            
        ],
        display: function(value, sourceData) {
            var colors = {"": "gray", 1: "#2196f3", 2: "#FFB74D", 3: "#4caf50"},
            elem = $.grep(sourceData, function(o){return o.value == value;});

            if(elem.length) {    
                $(this).text(elem[0].text).css("color", colors[value]); 
            }
            else {
                $(this).empty(); 
            }
        }   
    });
    
    if ($("#profileType").children("option:selected").val() !== 'azienda') {
        $("#pecForm").addClass("hidden");
        $("#phoneNumberForm").removeClass("hidden");
        $("#businessForm").addClass("hidden");
        $("#nameForm").removeClass("hidden");
        $("#lastnameForm").removeClass("hidden");
        $("#tinForm").removeClass("hidden");
    } else {
        $("#nameForm").addClass("hidden");
        $("#lastnameForm").addClass("hidden");
        $("#tinForm").addClass("hidden");
        $("#pecForm").removeClass("hidden");
        $("#phoneNumberForm").addClass("hidden");
        $("#businessForm").removeClass("hidden");
    }
    
    $("#profileType" ).change(function() {
        if ($("#profileType").children("option:selected").val() !== 'azienda') {
            $("#pecForm").addClass("hidden");
            $("#phoneNumberForm").removeClass("hidden");
            $("#businessForm").addClass("hidden");
            $("#nameForm").removeClass("hidden");
            $("#lastnameForm").removeClass("hidden");
            $("#tinForm").removeClass("hidden");
        } else {
            $("#nameForm").addClass("hidden");
            $("#lastnameForm").addClass("hidden");
            $("#tinForm").addClass("hidden");
            $("#pecForm").removeClass("hidden");
            $("#phoneNumberForm").addClass("hidden");
            $("#businessForm").removeClass("hidden");
        }
    });

    $('.tabApplication').click(function () {
        tab = $(this).attr('href');
        $('#tab').val(tab);
    }); 
    
    bindFileChange();
    $('div[id*="drop_uploader_"]').off();
    $('div[id*="drop_uploader_"]').on('DOMNodeRemoved', function (event) {
        bindFileChange();
        $('.tabApplication').click(function () {
            tab = $(this).attr('href');
            $('#tab').val(tab);
        }); 
    });
    
    $('.delete-fileid').click(function (event){
        event.preventDefault();
        
        var postToUrl = $(this).attr('href');
        var listName = $(this).attr('listname');
        var fileId = $(this).attr('fileid');
        
        if (postToUrl && listName && fileId) {
            
            var url = new URI(postToUrl);
            url.removeSearch("listName");
            url.addSearch("listName", listName);
            url.removeSearch("fileId");
            url.addSearch("fileId", fileId);

            $.blockUI();
            $.ajax({
                url: url,
                type: 'POST',
                //data: formData,
                cache: false,
                contentType: false,
                processData: false,
                success:
                    function (returndata) {
                        $.unblockUI();
                        // current step
                        var step = parseInt($('#step').val());
                        if (step) {
                            // reload step
                            var url = new URI();
                            url.removeSearch("step");
                            if (step) {
                                url.addSearch("step", step);
                            }
                            window.location.href = url;
                        }
                    },
                error:
                    function (response, status, errorThrown) {
                        $.unblockUI();
                    }
            });
            
        }
        
        return false;
    });

});

function saveInfo(step, tab) {
    
    var url = new URI($("#invoicediscountsAddInfoSaveUri").attr("href"));
    var formData = new FormData($('#form-invoicediscount')[0]);
    
    if (url && formData) {
        $.blockUI();
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            cache: false,
            enctype: 'multipart/form-data',
            contentType: false,
            processData: false,
            success:
                function (returndata) {
                    $.unblockUI();
                    if (step) {
                        // reload step
                        var url = new URI();
                        url.removeSearch("step");
                        if (step) {
                            url.addSearch("step", step);
                        }
                        url.removeSearch("tab");
                        if (tab) {
                            url.addSearch("tab", tab);
                        }
                        window.location.href = url;
                    }
                },
            error:
                function (response, status, errorThrown) {
                    $.unblockUI();
                }
        });
    }

}

function save() {
    
    var url = new URI($("#invoicediscountsAddSaveUri").attr("href"));
    var formData = new FormData($('#form-invoicediscount')[0]);
    
    if (url && formData) {
        $.blockUI();
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            cache: false,
            enctype: 'multipart/form-data',
            contentType: false,
            processData: false,
            success:
                function (returndata) {
                    $.unblockUI();
                    var url = new URI($("#invoicediscountsUri").attr("href"));
                    window.location.href = invoicediscountsUri;
                },
            error:
                function (response, status, errorThrown) {
                    $.unblockUI();
                }
        });
    }

}

function bindFileChange() {
    $('input[attachment=true]').off();
    $('input[attachment=true]').change(function (event){
        // current step
        var step = parseInt($('#step').val());
        if (!step) {
            step = 0;
        }
        var tab = $('#tab').val();
        
        // save and reload step
        saveInfo(step, tab);
    });
}