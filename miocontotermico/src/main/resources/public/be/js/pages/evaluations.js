// init
moment.locale('it');

$(function() {

    // Table setup
    // ------------------------------

    // Setting datatable defaults
    $.extend( $.fn.dataTable.defaults, {
        autoWidth: false,
        responsive: true,
        dom: '<"datatable-header"fBl><"datatable-scroll-wrap"t><"datatable-footer"ip>',
        language: {
            search: '<span>Filtra:</span> _INPUT_',
            searchPlaceholder: 'Filtra valutazioni...',
            lengthMenu: '<span>Mostra:</span> _MENU_',
            emptyTable: 'Non ci sono dati disponibili',
            info:       'Visualizzo da _START_ a _END_ di _TOTAL_ elementi',
            infoEmpty:  'Visualizzo da 0 a 0 di 0 elementi',
            infoFiltered: '(filtrati da _MAX_ elementi totali)',            
            paginate: { 'first': 'Prima', 'last': 'Ultima', 'next': '&rarr;', 'previous': '&larr;' }
        },        
        drawCallback: function () {
            $(this).find('tbody tr').slice(-3).find('.dropdown, .btn-group').addClass('dropup');
            var bLazy = new Blazy({ 
                offset: 350
            });
        },
        preDrawCallback: function() {
            $(this).find('tbody tr').slice(-3).find('.dropdown, .btn-group').removeClass('dropup');
        }
    });      

    bindEvaluationUpdate();
    
    // State saving
    var table = $('.datatable-evaluations').DataTable({
        responsive: {
            details: {
                type: 'column',
                target: -1
            }
        },
        buttons: [
            {
                extend: 'colvis',
                text: '<i class="icon-grid3"></i> <span class="caret"></span>',
                className: 'btn bg-success-600 btn-icon'
            }
        ],        
        stateSave: true,        
        columnDefs: [
            {
                className: 'control',
                orderable: false,
                targets: -1
            },
            {
                type: 'sortable-value',
                orderable: true,
                targets: 0
            },
            {
                type: 'sortable-value',
                orderable: true,
                targets: 3
            },
            {
                type: 'sortable-value',
                orderable: true,
                targets: 4
            },
            {
                type: 'sortable-value',
                orderable: true,
                targets: 5
            }
        ],
        order: [5, 'desc']
    });
    
    table.on( 'responsive-display', function ( e, datatable, row, showHide, update ) {
        bindEvaluationUpdate();        
    });
    
    table.on( 'responsive-resize', function ( e, datatable, columns ) {
        bindEvaluationUpdate();        
    });

    // External table additions
    // ------------------------------
    
    // Launch Uniform styling for checkboxes
    $('.ColVis_Button').addClass('btn btn-primary btn-icon').on('click mouseover', function() {
        $('.ColVis_collection input').uniform();
    });

    // Enable Select2 select for the length option
    $('.dataTables_length select').select2({
        minimumResultsForSearch: Infinity,
        width: 'auto'
    });
    
    // Default initialization
    $(".styled, .multiselect-container input").uniform({
        radioClass: 'choice'
    });
    
    $("#filter-form").on('submit', function(event) {
        event.preventDefault();
        
        var url = new URI();
        
        var startDate = $('.daterange-predefined').data('daterangepicker').startDate.format('DD/MM/YYYY');
        var endDate = $('.daterange-predefined').data('daterangepicker').endDate.format('DD/MM/YYYY');        
        
        url.removeSearch("startDate");
        if (startDate) {
            url.addSearch("startDate", startDate);
        }

        url.removeSearch("endDate");
        if (endDate) {
            url.addSearch("endDate", endDate);
        }
        
        var selectedStatuses = "";
        $('.man-status-filter:checked:enabled').each(function() {
            selectedStatuses += $(this).attr('man-status-value') + "|";
        });

        url.removeSearch("selectedStatuses");
        if (selectedStatuses) {
            url.addSearch("selectedStatuses", selectedStatuses);
        }

        window.location.href = url;
        
        return false;
    });
    
    // Initialize with options
    var startDate = moment($('#startDate').text());
    var endDate = moment($('#endDate').text());
    $('.daterange-predefined').daterangepicker(
        {
            startDate: startDate,
            endDate: endDate,
            minDate: '01/01/2017',
            maxDate: '12/31/2099',
            dateLimit: { days: 366 },
            ranges: {
                'Ultimi 7 giorni': [moment().subtract(6, 'days'), moment()],
                'Ultimi 30 giorni': [moment().subtract(29, 'days'), moment()],
                'Questo mese': [moment().startOf('month'), moment().endOf('month')],
                'Ultimo mese': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')],
                'Ultimo 3 mesi': [moment().subtract(3, 'month').startOf('month'), moment().endOf('month')],
                'Ultimo 6 mesi': [moment().subtract(6, 'month').startOf('month'), moment().endOf('month')]
            },
            locale: {
                format: 'DD/MM/YYYY',
                applyLabel: 'Applica',
                cancelLabel: 'Annulla',
                startLabel: 'Data inizio',
                endLabel: 'Data fine',
                customRangeLabel: 'Personalizzato',
                daysOfWeek: ['Do', 'Lu', 'Ma', 'Me', 'Gi', 'Ve','Sa'],
                monthNames: ['Gennaio', 'Febbraio', 'Marzo', 'Aprile', 'Maggio', 'Giugno', 'Luglio', 'Agosto', 'Settembre', 'Ottobre', 'Novembre', 'Dicembre'],
                firstDay: 1
            },
            opens: 'left',
            applyClass: 'btn-small bg-black filter-date',
            cancelClass: 'btn-small btn-default'
        },
        function(start, end) {
            $('.daterange-predefined span').html(start.format('MMMM D, YYYY') + ' &nbsp; - &nbsp; ' + end.format('MMMM D, YYYY'));
            
            var url = new URI();
            
            var startDate = start.format('DD/MM/YYYY');
            var endDate = end.format('DD/MM/YYYY');

            url.removeSearch("startDate");
            if (startDate) {
                url.addSearch("startDate", startDate);
            }

            url.removeSearch("endDate");
            if (endDate) {
                url.addSearch("endDate", endDate);
            }
            
            var selectedStatuses = "";
            $('.man-status-filter:checked:enabled').each(function() {
                selectedStatuses += $(this).attr('man-status-value') + "|";
            });

            url.removeSearch("selectedStatuses");
            if (selectedStatuses) {
                url.addSearch("selectedStatuses", selectedStatuses);
            }

            window.location.href = url;
           
        }
    );

    // Display date format
    $('.daterange-predefined span').html(startDate.format('MMMM D, YYYY') + ' &nbsp; - &nbsp; ' + endDate.format('MMMM D, YYYY'));
    
});

function bindEvaluationUpdate() {

    $('.evaluation-update').click(function (event) {

        // disable the default form submission
        event.preventDefault();

        var postToUrl = $(this).attr('href');
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-checkmark icon-2x"></i><br/><br/> Aggiornamento stato',
            content: "Confermi?",
            columnClass:"col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
            buttons: {
                annulla: {
                    text: 'Annulla',
                    btnClass: 'btn-default mb-5',
                    action: function () {
                        //close
                    }
                },
                conferma: {
                    text: 'Conferma',
                    btnClass: 'btn-success mb-5',
                    action: function () {
                        $.blockUI();
                        $.ajax({
                            url: postToUrl,
                            type: 'POST',
                            data: null,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success:
                                    function (returndata) {
                                        $.unblockUI();
                                        var url = $("#evaluationsSuccessUri").attr("href");
                                        window.location.href = url;
                                    },
                            error:
                                function (response, status, errorThrown) {
                                    $.unblockUI();
                                    // warn
                                    $.alert({
                                        theme: 'supervan',
                                        escapeKey: true,
                                        animation: 'top',
                                        closeAnimation: 'bottom',
                                        backgroundDismiss: true,
                                        title: 'Oh oh! :(',
                                        content: 'Aggiornamento non riuscito.<br/>'
                                    });
                                }
                        });                    
                    }                    
                }
            }
        });        

        return false;
    });  
}

