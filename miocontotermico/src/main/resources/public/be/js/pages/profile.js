$(function() {

    if ($("#profileType").children("option:selected").val() !== 'azienda') {
        $("#fullnameForm").addClass("hidden");
        $("#vatNumberForm").addClass("hidden");
        $("#sdiNumberForm").addClass("hidden");
        $("#pecForm").addClass("hidden");
    } else {
        $("#nameForm").addClass("hidden");
        $("#lastnameForm").addClass("hidden");
        $("#tinForm").addClass("hidden");
        $("#fullnameForm").removeClass("hidden");
    }
    
    $( "#profileType" ).change(function() {
        if ($("#profileType").children("option:selected").val() !== 'azienda') {
            $("#fullnameForm").addClass("hidden");
            $("#vatNumberForm").addClass("hidden");
            $("#sdiNumberForm").addClass("hidden");
            $("#pecForm").addClass("hidden");
        } else {
            $("#nameForm").addClass("hidden");
            $("#lastnameForm").addClass("hidden");
            $("#tinForm").addClass("hidden");
            $("#fullnameForm").removeClass("hidden");
        }
    });
    
    // Form components
    // ------------------------------

    // Default initialization
    $(".styled, .multiselect-container input").uniform({
        radioClass: 'choice'
    }); 
    
    // Select with search
    $('.select-search').select2();

    // Dropdown
    $('.select').select2({
        minimumResultsForSearch: Infinity
    });
        
    // Initialize validation
    $(".form-horizontal").validate({
        ignore: 'input[type=hidden], .select2-search__field', // ignore hidden fields
        errorClass: 'validation-error-label',
        successClass: 'validation-valid-label',
        highlight: function (element, errorClass) {
            $(element).removeClass(errorClass);
        },
        unhighlight: function (element, errorClass) {
            $(element).removeClass(errorClass);
        },
        // Different components require proper error label placement
        errorPlacement: function (error, element) {

            // Styled checkboxes, radios, bootstrap switch
            if (element.parents('div').hasClass("checker") || element.parents('div').hasClass("choice") || element.parent().hasClass('bootstrap-switch-container')) {
                if (element.parents('label').hasClass('checkbox-inline') || element.parents('label').hasClass('radio-inline')) {
                    error.appendTo(element.parent().parent().parent().parent());
                } else {
                    error.appendTo(element.parent().parent().parent().parent().parent());
                }
            }

            // Unstyled checkboxes, radios
            else if (element.parents('div').hasClass('checkbox') || element.parents('div').hasClass('radio')) {
                error.appendTo(element.parent().parent().parent());
            }

            // Input with icons and Select2
            else if (element.parents('div').hasClass('has-feedback') || element.hasClass('select2-hidden-accessible')) {
                error.appendTo(element.parent());
            }

            // Inline checkboxes, radios
            else if (element.parents('label').hasClass('checkbox-inline') || element.parents('label').hasClass('radio-inline')) {
                error.appendTo(element.parent().parent());
            }

            // Input group, styled file input
            else if (element.parent().hasClass('uploader') || element.parents().hasClass('input-group')) {
                error.appendTo(element.parent().parent());
            } else {
                error.insertAfter(element);
            }
        },
        rules: {
            email: {
                email: true
            }
        }
    });
    
});
