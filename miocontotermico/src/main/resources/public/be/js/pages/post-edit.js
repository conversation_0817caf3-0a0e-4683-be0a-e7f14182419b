$(function() {

    // Lung<PERSON>zza campi
    // -------------------------
    $('.maxlength').maxlength({
        alwaysShow: true,
        placement: 'top-right'
    });

    // Autosize Descrizione
    // -------------------------
    autosize($('.elastic'));

    // Default initialization
    $(".styled, .multiselect-container input").uniform({
        radioClass: 'choice'
    }); 
    
    // Select with search
    $('.select-search').select2();

    // Dropdown
    $('.select').select2({
        minimumResultsForSearch: Infinity
    });

    // Initialize validation
    $(".form-horizontal").validate({
        ignore: 'input[type=hidden], .select2-search__field', // ignore hidden fields
        errorClass: 'validation-error-label',
        successClass: 'validation-valid-label',
        highlight: function (element, errorClass) {
            $(element).removeClass(errorClass);
        },
        unhighlight: function (element, errorClass) {
            $(element).removeClass(errorClass);
        },
        // Different components require proper error label placement
        errorPlacement: function (error, element) {

            // Styled checkboxes, radios, bootstrap switch
            if (element.parents('div').hasClass("checker") || element.parents('div').hasClass("choice") || element.parent().hasClass('bootstrap-switch-container')) {
                if (element.parents('label').hasClass('checkbox-inline') || element.parents('label').hasClass('radio-inline')) {
                    error.appendTo(element.parent().parent().parent().parent());
                } else {
                    error.appendTo(element.parent().parent().parent().parent().parent());
                }
            }

            // Unstyled checkboxes, radios
            else if (element.parents('div').hasClass('checkbox') || element.parents('div').hasClass('radio')) {
                error.appendTo(element.parent().parent().parent());
            }

            // Input with icons and Select2
            else if (element.parents('div').hasClass('has-feedback') || element.hasClass('select2-hidden-accessible')) {
                error.appendTo(element.parent());
            }

            // Inline checkboxes, radios
            else if (element.parents('label').hasClass('checkbox-inline') || element.parents('label').hasClass('radio-inline')) {
                error.appendTo(element.parent().parent());
            }

            // Input group, styled file input
            else if (element.parent().hasClass('uploader') || element.parents().hasClass('input-group')) {
                error.appendTo(element.parent().parent());
            } else {
                error.insertAfter(element);
            }
        },
        rules: {
            email: {
                email: true
            }
        }
    });

    // Default initialization
    $('.wysihtml5-default').wysihtml5({
        parserRules:  wysihtml5ParserRules,
        stylesheets: ["https://siteria.it/tpls/be/1/1.6/assets/css/components.css"]
    });


    // Simple toolbar
    $('.wysihtml5-min').wysihtml5({
        parserRules:  wysihtml5ParserRules,
        stylesheets: ["https://siteria.it/tpls/be/1/1.6/assets/css/components.css"],
        "font-styles": true, // Font styling, e.g. h1, h2, etc. Default true
        "emphasis": true, // Italics, bold, etc. Default true
        "lists": false, // (Un)ordered lists, e.g. Bullets, Numbers. Default true
        "html": false, // Button which allows you to edit the generated HTML. Default false
        "link": false, // Button to insert a link. Default true
        "image": false, // Button to insert an image. Default true,
        "action": false, // Undo / Redo buttons,
        "color": true // Button to change color of font
    });
	
    $('.btn-cancel').click(function () {
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-cancel-square icon-2x"></i><br/><br/> Sei sicuro?',
            content: "Le modifiche andranno perse.",
            buttons: {
                annulla: {
                    btnClass: 'btn-default',
                    action: function () {
                        //
                    }
                },
                conferma: {
                    btnClass: 'bg-danger-700',
                    action: function () {
                        window.location.href = $('#postsUri').attr('href');
                    }
                }
            }
        });
    });	
    
    // bind delete action
    $('#btn-delete').click(function (event) {

        // disable the default form submission
        event.preventDefault();

        var postToUrl = $('#postRemoveUri').attr('href');
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-trash icon-2x"></i><br/><br/> Rimozione post',
            content: "Procedere?",
            columnClass:"col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
            buttons: {
                Annulla: {
                    text: 'Anuulla',
                    btnClass: 'btn-default mb-5',
                    action: function () {
                        //close
                    }
                },
                Conferma: {
                    text: 'Conferma',
                    btnClass: 'bg-danger-600 mb-5',
                    action: function () {
                        $.blockUI()
                        $.ajax({
                            url: postToUrl,
                            type: 'POST',
                            data: null,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success:
                                    function (returndata) {
                                        $.unblockUI();
                                        var url = $("#postsSuccessUri").attr("href");
                                        window.location.href = url;
                                    },
                            error:
                                function (response, status, errorThrown) {
                                    $.unblockUI();
                                    // warn
                                    $.alert({
                                        theme: 'supervan',
                                        escapeKey: true,
                                        animation: 'top',
                                        closeAnimation: 'bottom',
                                        backgroundDismiss: true,
                                        title: 'Oh oh! :(',
                                        content: 'Impossibile rimuovere i dati.<br/>'
                                    });
                                }
                        });                    
                    }                    
                }
            }
        });        

        return false;
    });    

    // ================================================================================
    // ================================================================================
    // ================================================================================
    // PQINA - SLIM MULTIPLE FILE UPLOAD
    // ================================================================================
    // ================================================================================
    // ================================================================================

    // 1. Handling the various events
    // - get references to different elements we need
    // - listen to drag, drop and change events
    // - handle dropped and selected files

    // get a reference to the file drop area and the file input
    var fileDropArea = document.querySelector('.file-drop-area');
    var fileInput = fileDropArea.querySelector('input');
    var fileInputName = fileInput.name;

    // listen to events for dragging and dropping
    fileDropArea.addEventListener('dragover', handleDragOver);
    fileDropArea.addEventListener('drop', handleDrop);
    fileInput.addEventListener('change', handleFileSelect);

    // need to block dragover to allow drop
    function handleDragOver(e) {
      e.preventDefault();
    }

    // deal with dropped items,
    function handleDrop(e) {
      e.preventDefault();
      handleFileItems(e.dataTransfer.items || e.dataTransfer.files);
    }

    // handle manual selection of files using the file input
    function handleFileSelect(e) {
      handleFileItems(e.target.files);
    }

    // 2. Handle the dropped items
    // - test if the item is a File or a DataTransferItem
    // - do some expectation matching

    // loops over a list of items and passes
    // them to the next function for handling
    function handleFileItems(items) {
      var l = items.length;
      for (var i=0; i<l; i++) {
        handleItem(items[i]);
      }
    }

    // handles the dropped item, could be a DataTransferItem
    // so we turn all items into files for easier handling
    function handleItem(item) {

      // get file from item
      var file = item;
      if (item.getAsFile && item.kind =='file') {
        file = item.getAsFile();
      }

      handleFile(file);
    }

    // now we're sure each item is a file
    // the function below can test if the files match
    // our expectations
    function handleFile(file) {

      /*
      // you can check if the file fits all requirements here
      // for example:
      // if file is bigger then 1 MB don't load
      if (file.size > 1000000) {
        return;
      }
      */

      // if it does, create a cropper
      createCropper(file);
    }

    // 3. Create the Image Cropper
    // - create an element for the cropper to bind to
    // - add the element after the drop area
    // - creat the cropper and bind the remove button so it
    //   removes the entire cropper when clicked.

    var progr = 0;

    // create an Image Cropper for each passed file
    function createCropper(file) {

      // create container element for cropper
      var cropper = document.createElement('div');
      cropper.classList.add('col-xs-6');
      cropper.classList.add('col-sm-3');
      cropper.classList.add('control-label');      
      cropper.classList.add('slim-item-multiple');

      // insert this element after as the last element in drop area
      //var sibling = fileDropArea.nextSibling;
      var sibling = null;
      fileDropArea.parentNode.insertBefore(cropper, sibling);

      // let input name be unique on submit
      progr += 1;
      
      // create a Slim Cropper
      Slim.create(cropper, {
        maxFileSize: 4,
        saveInitialImage: false,
        push: false,
        post: "output",
        label: "Carica un'immagine",
        jpegCompression: 70,
        labelLoading: " ",
        buttonEditLabel: "Modifica",
        buttonRemoveLabel: "Elimina",
        buttonDownloadLabel: "Scarica",
        buttonUploadLabel: "Carica",
        buttonRotateLabel: "Ruota",
        buttonCancelLabel: "Cancella",
        buttonConfirmLabel: "Conferma",
        buttonEditTitle: "Modifica",
        buttonRemoveTitle: "Elimina",
        buttonDownloadTitle: "Scarica",
        buttonUploadTitle: "Carica",
        buttonRotateTitle: "Ruota",
        buttonCancelTitle: "Cancella",
        buttonConfirmTitle: "Conferma",
        statusFileSize: "Il file è troppo grande, il massimo consentito è $0 MB",
        statusFileType: "Formato immagine non valido, formati consentiti: $0",
        statusNoSupport: "Il tuo browser non supporta la modifica dell'immagine",
        statusImageTooSmall: "Immagine troppo piccola, risoluzione minima: $0 pixel",
        statusContentLength: "Il server non supporta file così grandi",
        statusUnknownResponse: "Errore sconosciuto, <NAME_EMAIL>",
        statusUploadSuccess: "Immagine salvata",
        ratio: '16:9',
        defaultInputName: fileInputName + '-' + progr,
        didInit: function() {

          // load the file to our slim cropper
          this.load(file);

        },
        didRemove: function() {

          // detach from DOM
          cropper.parentNode.removeChild(cropper);

          // destroy the slim cropper
          this.destroy();

        }
      });

    }

    // 4. Disable the file input element

    // hide file input, we can now upload with JavaScript
    fileInput.style.display = 'none';

    // remove file input name so it's value is
    // not posted to the server
    fileInput.removeAttribute('name');

    // 5. Load pre-exisisting images
    var imageIdsText = $('#imageIds').text();
    if (imageIdsText) {
        var imageIds = imageIdsText.trim().split('|');
        for (var i in imageIds) {
            if (imageIds[i]) {
                var src = $("#imageUri").attr("href") + imageIds[i];
                console.log('sto caricando ' + src);
                createCropper(src);
            }
        }
    }

    // 6. Let area be a sortable zone
    Sortable.create(fileDropArea.parentNode, { /* options */ });

});
