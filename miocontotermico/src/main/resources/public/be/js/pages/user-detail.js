$(function() {

    // Setting datatable defaults
    $.extend( $.fn.dataTable.defaults, {
        autoWidth: false,
        responsive: true,
        dom: '<"datatable-header"fBl><"datatable-scroll-wrap"t><"datatable-footer"ip>',
        language: {
            search: '<span>Filtra:</span> _INPUT_',
            searchPlaceholder: 'Filtra procedure...',
            lengthMenu: '<span>Mostra:</span> _MENU_',
            emptyTable: 'Non ci sono dati disponibili',
            info:       'Visualizzo da _START_ a _END_ di _TOTAL_ elementi',
            infoEmpty:  'Visualizzo da 0 a 0 di 0 elementi',
            infoFiltered: '(filtrati da _MAX_ elementi totali)',            
            paginate: { 'first': 'Prima', 'last': 'Ultima', 'next': '&rarr;', 'previous': '&larr;' }
        },        
        drawCallback: function () {
            $(this).find('tbody tr').slice(-3).find('.dropdown, .btn-group').addClass('dropup');
        },
        preDrawCallback: function() {
            $(this).find('tbody tr').slice(-3).find('.dropdown, .btn-group').removeClass('dropup');
        }
    });      
    
        // State saving
    var table = $('.datatable-procedures').DataTable({
        responsive: {
            details: {
                type: 'column',
                target: -1
            }
        },
        buttons: [
            {
                extend: 'colvis',
                text: '<i class="icon-grid3"></i> <span class="caret"></span>',
                className: 'btn bg-success-600 btn-icon'
            }
        ],
        stateSave: true,        
        columnDefs: [
            {
                className: 'control',
                orderable: false,
                targets: -1
            },
            {
                type: 'sortable-value',
                orderable: true,
                targets: 2
            }
        ],
        order: [2, 'desc']
    });       

    if ($("#profileType").children("option:selected").val() !== 'azienda') {
        $("#fullnameForm").addClass("hidden");
        $("#vatNumberForm").addClass("hidden");
        $("#sdiNumberForm").addClass("hidden");
        $("#pecForm").addClass("hidden");
        $("#sdiForm").addClass("hidden");
    } else {
        $("#fullnameForm").removeClass("hidden");
        $("#vatNumberForm").removeClass("hidden");
        $("#sdiNumberForm").removeClass("hidden");
        $("#pecForm").removeClass("hidden");
        $("#sdiForm").removeClass("hidden");
    }
    
    $( "#profileType" ).change(function() {
        if ($("#profileType").children("option:selected").val() !== 'azienda') {
            $("#fullnameForm").addClass("hidden");
            $("#vatNumberForm").addClass("hidden");
            $("#sdiNumberForm").addClass("hidden");
            $("#pecForm").addClass("hidden");
            $("#sdiForm").addClass("hidden");
        } else {
            $("#fullnameForm").removeClass("hidden");
            $("#vatNumberForm").removeClass("hidden");
            $("#sdiNumberForm").removeClass("hidden");
            $("#pecForm").removeClass("hidden");
            $("#sdiForm").removeClass("hidden");
        }
    });
    
    // Form components
    // ------------------------------

    // Default initialization
    $(".styled, .multiselect-container input").uniform({
        radioClass: 'choice'
    }); 
    
    // Select with search
    $('.select-search').select2();

    // Dropdown
    $('.select').select2({
        minimumResultsForSearch: Infinity
    });
        
    // Initialize validation
    $(".form-horizontal").validate({
        ignore: 'input[type=hidden], .select2-search__field', // ignore hidden fields
        errorClass: 'validation-error-label',
        successClass: 'validation-valid-label',
        highlight: function (element, errorClass) {
            $(element).removeClass(errorClass);
        },
        unhighlight: function (element, errorClass) {
            $(element).removeClass(errorClass);
        },
        // Different components require proper error label placement
        errorPlacement: function (error, element) {

            // Styled checkboxes, radios, bootstrap switch
            if (element.parents('div').hasClass("checker") || element.parents('div').hasClass("choice") || element.parent().hasClass('bootstrap-switch-container')) {
                if (element.parents('label').hasClass('checkbox-inline') || element.parents('label').hasClass('radio-inline')) {
                    error.appendTo(element.parent().parent().parent().parent());
                } else {
                    error.appendTo(element.parent().parent().parent().parent().parent());
                }
            }

            // Unstyled checkboxes, radios
            else if (element.parents('div').hasClass('checkbox') || element.parents('div').hasClass('radio')) {
                error.appendTo(element.parent().parent().parent());
            }

            // Input with icons and Select2
            else if (element.parents('div').hasClass('has-feedback') || element.hasClass('select2-hidden-accessible')) {
                error.appendTo(element.parent());
            }

            // Inline checkboxes, radios
            else if (element.parents('label').hasClass('checkbox-inline') || element.parents('label').hasClass('radio-inline')) {
                error.appendTo(element.parent().parent());
            }

            // Input group, styled file input
            else if (element.parent().hasClass('uploader') || element.parents().hasClass('input-group')) {
                error.appendTo(element.parent().parent());
            } else {
                error.insertAfter(element);
            }
        },
        rules: {
            email: {
                email: true
            }
        }
    });
 
    // bind delete action
    $('#deleteUser').click(function (event) {

        // disable the default form submission
        event.preventDefault();

        var postToUrl = $('#userRemoveUri').attr('href');
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-checkmark icon-2x"></i><br/><br/> Eliminazione utente!',
            content: "Conferma eliminazione?",
            columnClass:"col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
            buttons: {
                annulla: {
                    text: 'Annulla',
                    btnClass: 'btn-default mb-5',
                    action: function () {
                        //close
                    }
                },
                conferma: {
                    text: 'Conferma',
                    btnClass: 'bg-black mb-5',
                    action: function () {
                        $.blockUI()
                        $.ajax({
                            url: postToUrl,
                            type: 'POST',
                            data: null,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success:
                                    function (returndata) {
                                        $.unblockUI();
                                        var url = $("#usersSuccessUri").attr("href");
                                        window.location.href = url;
                                    },
                            error:
                                function (response, status, errorThrown) {
                                    $.unblockUI();
                                    // warn
                                    $.alert({
                                        theme: 'supervan',
                                        escapeKey: true,
                                        animation: 'top',
                                        closeAnimation: 'bottom',
                                        backgroundDismiss: true,
                                        title: 'Oh oh! :(',
                                        content: 'Eliminazione non riuscita.<br/>'
                                    });
                                }
                        });                    
                    }                    
                }
            }
        });        

        return false;
    });
    
    // bind delete action
    $('#editCredit').click(function (event) {

        // disable the default form submission
        event.preventDefault();

        var postToUrl = $('#userUpdateCreditUri').attr('href');
        var credit = $('#credit').val();
        
        if (credit) {
            postToUrl = postToUrl + credit;
            $.confirm({
                theme: 'supervan',
                escapeKey: true,
                animation: 'top',
                closeAnimation: 'bottom',
                backgroundDismiss: true,
                title: '<i class="icon-checkmark icon-2x"></i><br/><br/> Aggiornamento utente!',
                content: "Conferma?",
                columnClass:"col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
                buttons: {
                    annulla: {
                        text: 'Annulla',
                        btnClass: 'btn-default mb-5',
                        action: function () {
                            //close
                        }
                    },
                    conferma: {
                        text: 'Conferma',
                        btnClass: 'bg-black mb-5',
                        action: function () {
                            $.blockUI()
                            $.ajax({
                                url: postToUrl,
                                type: 'POST',
                                data: null,
                                cache: false,
                                contentType: false,
                                processData: false,
                                success:
                                        function (returndata) {
                                            $.unblockUI();
                                            var url = $("#usersSuccessUri").attr("href");
                                            window.location.href = url;
                                        },
                                error:
                                    function (response, status, errorThrown) {
                                        $.unblockUI();
                                        // warn
                                        $.alert({
                                            theme: 'supervan',
                                            escapeKey: true,
                                            animation: 'top',
                                            closeAnimation: 'bottom',
                                            backgroundDismiss: true,
                                            title: 'Oh oh! :(',
                                            content: 'Aggiornamento non riuscita.<br/>'
                                        });
                                    }
                            });                    
                        }                    
                    }
                }
            });        
            
        }

        return false;
    });
    
    bindProcedureUpdate();
    
});

function bindProcedureUpdate() {

    $('.procedure-update').click(function (event) {

        // disable the default form submission
        event.preventDefault();

        var postToUrl = $(this).attr('href');
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-checkmark icon-2x"></i><br/><br/> Aggiornamento stato',
            content: "Confermi?",
            columnClass:"col-md-8 col-md-offset-2 col-sm-6 col-sm-offset-3 col-xs-10 col-xs-offset-1",
            buttons: {
                annulla: {
                    text: 'Annulla',
                    btnClass: 'btn-default mb-5',
                    action: function () {
                        //close
                    }
                },
                conferma: {
                    text: 'Conferma',
                    btnClass: 'btn-success mb-5',
                    action: function () {
                        $.blockUI();
                        $.ajax({
                            url: postToUrl,
                            type: 'POST',
                            data: null,
                            cache: false,
                            contentType: false,
                            processData: false,
                            success:
                                    function (returndata) {
                                        $.unblockUI();
                                        var url = $("#userDetailSuccessUri").attr("href");
                                        window.location.href = url;
                                    },
                            error:
                                function (response, status, errorThrown) {
                                    $.unblockUI();
                                    // warn
                                    $.alert({
                                        theme: 'supervan',
                                        escapeKey: true,
                                        animation: 'top',
                                        closeAnimation: 'bottom',
                                        backgroundDismiss: true,
                                        title: 'Oh oh! :(',
                                        content: 'Aggiornamento non riuscito.<br/>'
                                    });
                                }
                        });                    
                    }                    
                }
            }
        });        

        return false;
    });  
}