$(function() {

    // Lung<PERSON>zza campi
    // -------------------------
    $('.maxlength').maxlength({
        alwaysShow: true,
        placement: 'top-right'
    });

    // Autosize Descrizione
    // -------------------------
    autosize($('.elastic'));

    // Default initialization
    $(".styled, .multiselect-container input").uniform({
        radioClass: 'choice'
    }); 
    
    // Select with search
    $('.select-search').select2();

    // Dropdown
    $('.select').select2({
        minimumResultsForSearch: Infinity
    });

    // Initialize validation
    $(".form-horizontal").validate({
        ignore: 'input[type=hidden], .select2-search__field', // ignore hidden fields
        errorClass: 'validation-error-label',
        successClass: 'validation-valid-label',
        highlight: function (element, errorClass) {
            $(element).removeClass(errorClass);
        },
        unhighlight: function (element, errorClass) {
            $(element).removeClass(errorClass);
        },
        // Different components require proper error label placement
        errorPlacement: function (error, element) {

            // Styled checkboxes, radios, bootstrap switch
            if (element.parents('div').hasClass("checker") || element.parents('div').hasClass("choice") || element.parent().hasClass('bootstrap-switch-container')) {
                if (element.parents('label').hasClass('checkbox-inline') || element.parents('label').hasClass('radio-inline')) {
                    error.appendTo(element.parent().parent().parent().parent());
                } else {
                    error.appendTo(element.parent().parent().parent().parent().parent());
                }
            }

            // Unstyled checkboxes, radios
            else if (element.parents('div').hasClass('checkbox') || element.parents('div').hasClass('radio')) {
                error.appendTo(element.parent().parent().parent());
            }

            // Input with icons and Select2
            else if (element.parents('div').hasClass('has-feedback') || element.hasClass('select2-hidden-accessible')) {
                error.appendTo(element.parent());
            }

            // Inline checkboxes, radios
            else if (element.parents('label').hasClass('checkbox-inline') || element.parents('label').hasClass('radio-inline')) {
                error.appendTo(element.parent().parent());
            }

            // Input group, styled file input
            else if (element.parent().hasClass('uploader') || element.parents().hasClass('input-group')) {
                error.appendTo(element.parent().parent());
            } else {
                error.insertAfter(element);
            }
        },
        rules: {
            email: {
                email: true
            }
        }
    });

    // Default initialization
    $('.wysihtml5-default').wysihtml5({
        parserRules:  wysihtml5ParserRules,
        stylesheets: ["https://siteria.it/tpls/be/1/1.6/assets/css/components.css"]
    });


    // Simple toolbar
    $('.wysihtml5-min').wysihtml5({
        parserRules:  wysihtml5ParserRules,
        stylesheets: ["https://siteria.it/tpls/be/1/1.6/assets/css/components.css"],
        "font-styles": true, // Font styling, e.g. h1, h2, etc. Default true
        "emphasis": true, // Italics, bold, etc. Default true
        "lists": false, // (Un)ordered lists, e.g. Bullets, Numbers. Default true
        "html": false, // Button which allows you to edit the generated HTML. Default false
        "link": false, // Button to insert a link. Default true
        "image": false, // Button to insert an image. Default true,
        "action": false, // Undo / Redo buttons,
        "color": true // Button to change color of font
    });
	
    $('.btn-cancel').click(function () {
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-cancel-square icon-2x"></i><br/><br/> Sei sicuro?',
            content: "Le modifiche andranno perse.",
            buttons: {
                annulla: {
                    btnClass: 'btn-default',
                    action: function () {
                        //
                    }
                },
                conferma: {
                    btnClass: 'bg-danger-700',
                    action: function () {
                        window.location.href = $('#mailsUri').attr('href');
                    }
                }
            }
        });
    });	
    

});
