$(function() {

    // Table setup
    // ------------------------------

    // Setting datatable defaults
    $.extend( $.fn.dataTable.defaults, {
        autoWidth: false,
        responsive: true,
        dom: '<"datatable-header"fBl><"datatable-scroll-wrap"t><"datatable-footer"ip>',        
        language: {
            search: '<span>Filtra:</span> _INPUT_',
            searchPlaceholder: 'Filtra documenti...',
            lengthMenu: '<span>Mostra:</span> _MENU_',
            emptyTable: 'Non ci sono dati disponibili',
            info:       'Visualizzo da _START_ a _END_ di _TOTAL_ elementi',
            infoEmpty:  'Visualizzo da 0 a 0 di 0 elementi',
            infoFiltered: '(filtrati da _MAX_ elementi totali)',            
            paginate: { 'first': 'Prima', 'last': 'Ultima', 'next': '&rarr;', 'previous': '&larr;' }
        },        
        drawCallback: function () {
            $(this).find('tbody tr').slice(-3).find('.dropdown, .btn-group').addClass('dropup');
            var bLazy = new Blazy({ 
                offset: 350
            });
        },
        preDrawCallback: function() {
            $(this).find('tbody tr').slice(-3).find('.dropdown, .btn-group').removeClass('dropup');
        }
    });      

    // State saving
    var table = $('.datatable-paperworks').DataTable({
        responsive: {
            details: {
                type: 'column',
                target: -1
            }
        },
        buttons: [
            {
                extend: 'colvis',
                text: '<i class="icon-grid3"></i> <span class="caret"></span>',
                className: 'btn bg-success btn-icon'
            }
        ],
        stateSave: true,        
        columnDefs: [
            {
                className: 'control',
                orderable: false,
                targets: -1
            },           
            {
                type: "date-eu",
                targets: 1
            }
        ],
        order: [1, 'desc']
    });
    
    // External table additions
    // ------------------------------
    
    // Launch Uniform styling for checkboxes
    $('.ColVis_Button').addClass('btn bg-danger-700 btn-icon').on('click mouseover', function() {
        $('.ColVis_collection input').uniform();
    });

    // Enable Select2 select for the length option
    $('.dataTables_length select').select2({
        minimumResultsForSearch: Infinity,
        width: 'auto'
    });
    
    // Default initialization
    $(".styled, .multiselect-container input").uniform({
        radioClass: 'choice'
    });
    
    // Hide main sidebar in Dual Sidebar
    $(document).on('click', '.sidebar-main-hide', function (e) {
        e.preventDefault();
        $('.datatable-properties').DataTable()
         .columns.adjust()
         .responsive.recalc();
    });
    
});


