<?xml version="1.0" encoding="UTF-8"?>
<web-app version="3.1" xmlns="http://xmlns.jcp.org/xml/ns/javaee" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://xmlns.jcp.org/xml/ns/javaee http://xmlns.jcp.org/xml/ns/javaee/web-app_3_1.xsd">
    <filter>
        <filter-name>EncodingFilter</filter-name>
        <filter-class>org.apache.catalina.filters.SetCharacterEncodingFilter</filter-class>
        <init-param>
            <param-name>encoding</param-name>
            <param-value>UTF-8</param-value>
        </init-param>
    </filter>
    <filter>
        <filter-name>SparkFilter</filter-name>
        <filter-class>com.miocontotermico.core.ServletFilter</filter-class>
        <init-param>
            <param-name>applicationClass</param-name>
            <param-value>com.miocontotermico.core.Application</param-value>
        </init-param>
    </filter>
    <filter-mapping>
        <filter-name>EncodingFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    <filter-mapping>
        <filter-name>SparkFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>
    <context-param>
        <param-name>com.miocontotermico.mongodb.hostname</param-name>
        <param-value>localhost</param-value>
    </context-param>
    <context-param>
        <param-name>com.miocontotermico.mongodb.port</param-name>
        <param-value>27017</param-value>
    </context-param>
    <context-param>
        <param-name>com.miocontotermico.mongodb.database</param-name>
        <param-value>miocontotermico</param-value>
    </context-param>
    <context-param>
        <param-name>com.miocontotermico.redis.hostname</param-name>
        <param-value>localhost</param-value>
    </context-param>
    <context-param>
        <param-name>com.miocontotermico.session.duration</param-name>
        <param-value>28800</param-value>
    </context-param>
    <context-param>
        <param-name>com.miocontotermico.session.remember.duration</param-name>
        <param-value>7776000</param-value>
    </context-param>
    <context-param>
        <param-name>com.tibaldi.smtp.hostname</param-name>
        <param-value>mail.symmathesy.org</param-value>
    </context-param>
    <context-param>
        <param-name>com.tibaldi.smtp.port</param-name>
        <param-value>465</param-value>
    </context-param>
    <context-param>
        <param-name>com.tibaldi.smtp.authentication</param-name>
        <param-value>true</param-value>
    </context-param>
    <context-param>
        <param-name>com.tibaldi.smtp.username</param-name>
        <param-value><EMAIL></param-value>
    </context-param>
    <context-param>
        <param-name>com.tibaldi.smtp.password</param-name>
        <param-value>!midastouchSY</param-value>
    </context-param>
    <context-param>
        <param-name>com.tibaldi.smtp.emailencryption</param-name>
        <param-value>true</param-value>
    </context-param>
    <context-param>
        <param-name>com.miocontotermico.smtp.sender</param-name>
        <param-value><EMAIL></param-value>
    </context-param>
</web-app>
