{% extends "fe/include/base.html" %}

{% block title %}SOGENIT | News | Società Gestione Energetica Italiana{% endblock %}

{% block canonical %}
    <meta name="robots" content="index, follow">
    <meta name="description" content="">
    <link rel="canonical" href="https://www.miocontotermico.it/news">
{% endblock %}

{% block socialcards %}        
    <meta property="og:locale" content="it_IT" />
    <meta property="og:url" content="https://www.miocontotermico.it/news" />
    <meta property="og:type" content="website" />
    <meta property="og:title" content="SOGENIT | News | Società Gestione Energetica Italiana" />
    <meta property="og:description" content="" />
    <meta property="og:image" content="{{ contextPath }}/fe/imgs/sogenit-meta-img.jpg" />
    <meta property="og:image:width" content="1200" />
    <meta property="og:image:height" content="630" />
    <meta property="og:image:alt" content="SOGENIT | Società Gestione Energetica Italiana" />
{% endblock %}

{% block content %}
    <!--Start breadcrumb area-->     
    <section class="breadcrumb-area" style="background-image: url({{ contextPath }}/fe/imgs/breadcrumb-bg.jpg);">
        <div class="container">
            <div class="row">
                <div class="col-xl-12">
                    <div class="inner-content clearfix">
                        <div class="title float-left">
                            <h1>News</h1>
                        </div>
                        <div class="breadcrumb-menu float-right">
                            <ul class="clearfix">
                                <li><a href="{{ paths('HOME') }}">Home</a></li>
                                <li class="active">News</li>
                            </ul>    
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!--End breadcrumb area-->

    <!--Start blog area-->
    <section id="blog-area" class="blog-style2-area">
        <div class="container">
            <div class="row">
                {% if postList is not empty %}
                    {% for post in postList %}
                        <!--Start single blog post-->
                        <div class="col-xl-4 col-lg-4 col-md-12 col-sm-12">
                            <div class="single-blog-post style2 wow fadeInLeft" data-wow-delay="0ms" data-wow-duration="1500ms">
                                <div class="img-holder">
                                    <img src="{{ paths('IMAGE') }}?oid={{ post.imageIds[0] }}" alt="{{ post.title }}">
                                    <div class="overlay-style-two"></div>
                                    <div class="post-date">                                        
                                        <h3><span>{{ post.creation | date("MM") }}</span><br> {{ post.creation | date("dd") }}</h3>
                                    </div>
                                    <div class="overlay-content">
                                        <div class="box">
                                            <div class="read-more">
                                                <a class="btn-two" href="{{ paths('NEWS_DETAIL') }}/{{ post.identifier }}">Scopri di più<span class="icon-null"></span></a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-holder">
                                    <div class="meta-box">
                                        <ul class="meta-info">                                            
                                            <li><a href="#">{{ post.category }}</a></li>
                                        </ul> 
                                        <div class="author-icon">
                                            <span class="icon-user"></span>    
                                        </div>  
                                    </div>
                                    <h3 class="blog-title"><a href="{{ paths('NEWS_DETAIL') }}/{{ post.identifier }}">{{ post.title }}</a></h3>  
                                </div>
                            </div>
                        </div>
                        <!--End single blog post-->    
                    {% endfor %}
                {% endif %}
                    
            </div>
        </div>
    </section>
    
{% endblock %}


