<!DOCTYPE html>
<html lang="it" prefix="og: http://ogp.me/ns#">

    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <!-- SEO -->
        <title>{% block title %}{% endblock %}</title>
      
        <!-- GOOGLE TAG MANAGER -->
        <script>
            (function(w, d, s, l, i) {
              w[l] = w[l] || [];
              w[l].push({
                'gtm.start': new Date().getTime(),
                event: 'gtm.js'
              });
              var f = d.getElementsByTagName(s)[0],
                j = d.createElement(s),
                dl = l != 'dataLayer' ? '&l=' + l : '';
                    j.async = true;
                    j.src =
                            'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
                    f.parentNode.insertBefore(j, f);
                })(window, document, 'script', 'dataLayer', 'GTM-N6K7RNH');
        </script>

        <!-- URL CANONICAL -->
        {% block canonical %}{% endblock %}

        <!-- SOCIAL CARDS -->
        {% block socialcards %}{% endblock %}

        <!-- PRECONNECT -->                
        <link rel="preconnect" href="https://www.google-analytics.com" crossorigin>
        <link rel="preconnect" href="https://www.googletagmanager.com" crossorigin>
        <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>        
        <link rel="preconnect" href="https://fonts.googleapis.com/" crossorigin>        

        <!-- MAIN CSS -->
        <link href="https://siteria.it/tpls/fe/26/css/style.css" rel="stylesheet">

        <!-- PAGE CSS -->
        {% block pagecss %}{% endblock %}

        <!-- OVERRIDE CSS -->
        <link href="{{ contextPath }}/fe/css/custom.css?{{ buildNumber }}" rel="stylesheet" type="text/css" media="all" />

        <!-- FAVICON -->        
        <link rel="apple-touch-icon" sizes="180x180" href="{{ contextPath }}/fe/imgs/favicon/apple-touch-icon.png">
        <link rel="icon" type="image/png" sizes="32x32" href="{{ contextPath }}/fe/imgs/favicon/favicon-32x32.png">
        <link rel="icon" type="image/png" sizes="16x16" href="{{ contextPath }}/fe/imgs/favicon/favicon-16x16.png">
        <link rel="manifest" href="{{ contextPath }}/fe/imgs/favicon/site.webmanifest">
        <link rel="mask-icon" href="{{ contextPath }}/fe/imgs/favicon/safari-pinned-tab.svg" color="#3aa40c">
        <meta name="msapplication-TileColor" content="#3aa40c">
        <meta name="theme-color" content="#ffffff">                
        
        <script type="text/javascript">
        var _iub = _iub || [];
        _iub.csConfiguration = {"lang":"it","siteId":1633329,"floatingPreferencesButtonDisplay":"anchored-bottom-left","consentOnScroll":false,"perPurposeConsent":true,"cookiePolicyId":21639774, "banner":{ "acceptButtonDisplay":true,"customizeButtonDisplay":true,"position":"float-top-center","rejectButtonDisplay":true }};
        </script>
        <script type="text/javascript" src="//cdn.iubenda.com/cs/iubenda_cs.js" charset="UTF-8" async></script>
        <script type="text/javascript">
          var _iub = _iub || {};
          _iub.cons_instructions = _iub.cons_instructions || [];
          _iub.cons_instructions.push(["init", {
            api_key: "nGnJQ3wVH0bHUovqcVurBXCqM3G6r9Fu"
          }]);
        </script>
        <script src="https://cdn.iubenda.com/cons/iubenda_cons.js" async></script>
        <!-- EXTRA HEAD -->
        {% block pagehead %}{% endblock %}
</head>

<body>
    
    <!-- GOOGLE TAG MANAGER -->
    <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-N6K7RNH" height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>    
    
    <!-- HEADER -->
    {% include "fe/include/header.html" %}

    <!-- CONTENT -->
    {% block content %}{% endblock %}

    <!-- FOOTER -->
    {% include "fe/include/footer.html" %}

    <!-- SCRIPTS -->
    <script src="https://siteria.it/tpls/fe/26/js/jquery.js"></script>
    <script src="https://siteria.it/tpls/fe/26/js/wow.js"></script>
    <script src="https://siteria.it/tpls/fe/26/js/bootstrap.bundle.min.js"></script>
    <script src="https://siteria.it/tpls/fe/26/js/jquery.fancybox.js"></script>
    <script src="https://siteria.it/tpls/fe/26/js/jquery.countTo.js"></script>
    <script src="https://siteria.it/tpls/fe/26/js/appear.js"></script>
    <script src="https://siteria.it/tpls/fe/26/js/owl.js"></script>
    <script src="https://siteria.it/tpls/fe/26/js/validation.js"></script>
    <script src="https://siteria.it/tpls/fe/26/js/jquery.mixitup.min.js"></script>
    <script src="https://siteria.it/tpls/fe/26/js/isotope.js"></script>
    <script src="https://siteria.it/tpls/fe/26/js/jquery.paroller.min.js"></script>
    <script src="https://siteria.it/tpls/fe/26/js/jquery.easing.min.js"></script>
    <script src="https://siteria.it/tpls/fe/26/js/jquery.enllax.min.js"></script>
    <script src="https://siteria.it/tpls/fe/26/js/map-helper.js"></script>
    <script src="https://siteria.it/tpls/fe/26/assets/language-switcher/jquery.polyglot.language.switcher.js"></script>
    <script src="https://siteria.it/tpls/fe/26/assets/timepicker/timePicker.js"></script>
    <script src="https://siteria.it/tpls/fe/26/js/bootstrap-select.min.js"></script>
    <script src="https://siteria.it/tpls/fe/26/assets/html5lightbox/html5lightbox.js"></script>    
    <script src="https://siteria.it/tpls/fe/26/plugins/revolution/js/jquery.themepunch.revolution.min.js"></script>
    <script src="https://siteria.it/tpls/fe/26/plugins/revolution/js/jquery.themepunch.tools.min.js"></script>
    <script src="https://siteria.it/tpls/fe/26/plugins/revolution/js/extensions/revolution.extension.actions.min.js"></script>
    <script src="https://siteria.it/tpls/fe/26/plugins/revolution/js/extensions/revolution.extension.carousel.min.js"></script>
    <script src="https://siteria.it/tpls/fe/26/plugins/revolution/js/extensions/revolution.extension.kenburn.min.js"></script>
    <script src="https://siteria.it/tpls/fe/26/plugins/revolution/js/extensions/revolution.extension.layeranimation.min.js"></script>
    <script src="https://siteria.it/tpls/fe/26/plugins/revolution/js/extensions/revolution.extension.migration.min.js"></script>
    <script src="https://siteria.it/tpls/fe/26/plugins/revolution/js/extensions/revolution.extension.navigation.min.js"></script>
    <script src="https://siteria.it/tpls/fe/26/plugins/revolution/js/extensions/revolution.extension.parallax.min.js"></script>
    <script src="https://siteria.it/tpls/fe/26/plugins/revolution/js/extensions/revolution.extension.slideanims.min.js"></script>
    <script src="https://siteria.it/tpls/fe/26/plugins/revolution/js/extensions/revolution.extension.video.min.js"></script>
    <script src="https://siteria.it/tpls/fe/26/js/main-slider-script.js"></script>
    <script src="https://siteria.it/tpls/fe/26/js/custom.js"></script>
    <script src="https://siteria.it/libs/jquery-blockui/2.70.0/jquery.blockUI.min.js"></script>
    <script src="https://siteria.it/libs/jquery-confirm/3.2.3/dist/jquery-confirm.min.js"></script>
    
    {% block pagescripts %}{% endblock %}
    
    <!-- LINKING DATA -->
    <script type="application/ld+json">
        [
           {
              "@context":"http://schema.org",
              "@type":"WebSite",
              "url":"https://www.miocontotermico.it"
           },
           {
              "@context":"http://schema.org",
              "@type":"Organization",
              "name":"Miocontotermico",
              "alternateName":"SOGENIT",
              "url":"https://www.miocontotermico.it",
              "sameAs":[
                 "https://www.facebook.com/miocontotermico/"
              ],
              "logo":"https://www.miocontotermico.it/imgs/logo_dark.png"
           }
           {% block schema %}{% endblock %}
        ]
    </script>
    <script>
        $.blockUI.defaults  = {
            message:  '<img src="{{ contextPath }}/fe/imgs/loader.svg" width="64">',
            css: {
                padding:        0,
                margin:         0,
                width:          '30%',
                top:            '40%',
                left:           '35%',
                textAlign:      'center',
                color:          '#0F1628',
                border:         'none',
                backgroundColor:'transparent',
                cursor:         'wait'
            },
            overlayCSS:  {
                backgroundColor: '#000',
                opacity:         0.3,
                cursor:          'wait'
            },
            baseZ: 1000,
            showOverlay: true
        };
    </script>    
        
</body>

</html>
