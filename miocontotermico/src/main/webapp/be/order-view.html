{% extends "be/include/base.html" %}

{% block extrahead %}
    <title>Dettaglio ordine</title>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/core/libraries/jasny_bootstrap.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/pickers/daterangepicker.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/styling/uniform.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/datatables.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/extensions/responsive.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/selects/select2.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/app.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/ui/ripple.min.js"></script>
    <!-- including old editable form, missing in limitless 2 -->
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/editable/editable.min.js"></script>
    <script src="{{ contextPath }}/be/js/pages/order-view.js?{{ buildNumber }}"></script>
{% endblock %}

{% block sidebarcontrol %}
<ul class="fab-menu fab-menu-top-left hidden-xs" data-fab-toggle="click">
    <li>
        <a class="fab-menu-btn btn btn-default btn-float btn-rounded btn-icon sidebar-control sidebar-main-hide">
            <i class="icon-paragraph-justify3"></i>
        </a>
    </li>
</ul>
{% endblock %}

{% block content %}

<!-- actions -->
<a id="orderSuccessUri" style="display: none" href="{{ paths('ORDER_VIEW') }}/ok?orderId={{ order.order.id }}" rel="nofollow"></a>


<!-- PAGE CONTENT -->
<div class="page-content">

    <!-- LEFT SIDEBAR -->
    <div class="sidebar sidebar-main sidebar-default sidebar-separate">
        <div class=" sidebar-content">

            <div class="content-group">
                <div class="panel-body bg-info border-radius-top text-center" style="background-image: url({{ contextPath }}/be/imgs/bg.png); background-size: contain;">
                    <div class="content-group-sm">
                        <h5 class="text-semibold no-margin-bottom">
                            Ordine n° {{ order.order.protocol }}{{ order.order.productReturn ? ' (RESO)' : ''}}
                        </h5>

                        {% set fullname = '*Nome* *Cognome*' %}
                        {% if order.user.fullname is not empty %}
                            {% set fullname = order.user.fullname %}
                        {% elseif (order.user.name is not empty) and (order.user.lastname is not empty) %}
                            {% set fullname = (order.user.name | default('*Nome*')) + ' ' + order.user.lastname | default('*Cognome*') %}
                        {% endif %}
                        
                        <span class="display-block">cliente <a href="{{ paths('USER_DETAIL') }}?userid={{ order.user.id }}">{{ fullname }}</a></span>
                    </div>

                    <div class="display-inline-block content-group-sm">
                        {% if order.user.imageId is not empty %}
                            <img src="{{ paths('IMAGE') }}?oid={{ order.user.imageId }}" class="img-circle img-responsive img-entity" alt="Ordine">
                        {% else %}
                            {#<!--<img src="{{ contextPath }}/be/imgs/placeholder.jpg" class="img-circle img-responsive img-entity" alt="Ordine">-->#}
                            <img src="https://ui-avatars.com/api/?name={{ order.user.name }}+{{ order.user.lastname }}+{{ order.user.fullname }}&size=256" class="img-circle img-responsive" alt="" style="width: 120px; height: 120px;">
                        {% endif %}
                    </div>
                </div>

                <div class="panel panel-body no-border-top no-border-radius-top">
                    <div class="form-group mt-5">
                        <label class="text-semibold">Pagamento:</label>
                        <span class="pull-right-sm truncate width-half-right">
                            {% if order.order.paymentStatus == 'paid' %}
                                <span class="label label-flat border-success-700 text-success-700">PAGATO</span>
                            {% elseif order.order.paymentStatus == 'installment' %}
                                <span class="label label-flat border-green-400 text-green-400">ACCONTO</span>
                            {% else %}
                                <span class="label label-flat border-danger-600 text-danger-600">ARRETRATO</span>
                            {% endif %}
                        </span>
                    </div>

                    <div class="form-group mt-5">
                        <label class="text-semibold">Data ordine:</label>
                        <span class="pull-right-sm truncate width-half-right">{{ order.order.date | date('dd MMMM yyyy') }}</span>
                    </div>
                    <div class="form-group">
                        <label class="text-semibold">Totale:</label>
                        <span class="pull-right-sm truncate width-half-right">{{ order.order.finalPrice | numberformat("#0.00") }} €</span>
                    </div>

                </div>
            </div>

        </div>
    </div>
    <!-- END LEFT SIDEBAR -->

    <!-- MAIN CONTENT -->
    <div class="content-wrapper">

        {% if success %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-success">
                    <a href="{{ paths('ORDER_VIEW') }}?orderId={{ order.order.id }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Dati memorizzati correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-danger">
                    <a href="{{ paths('ORDER_VIEW') }}?orderId={{ order.order.id }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Control position -->
        <div class="panel panel-white">
            <div class="panel-heading">
                <h5 class="panel-title text-bold">Dettaglio ordine n° {{ order.order.protocol }}{{ order.order.productReturn ? ' (RESO)' : ''}}</h5>
            </div>

            <table class="table datatable-responsive-control-right">
                <thead>
                    <tr>
                        <th>Crediti</th>
                        <th>Totale ordine</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                                {{ order.order.credit }}
                        </td>
                        <td>
                            <h6 class="no-margin text-semibold {{ color }}">€ {{ order.order.finalPrice | numberformat("#0.00") }}</h6>
                        </td>
                        <td></td>
                    </tr>
                </tbody>
            </table>
        </div>
        <!-- /control position -->

        <div class="form-horizontal">
            <div class="row">
                {% if order.order.status != 'annulled' %}
                <div class="col-sm-12 col-md-6 col-lg-3">                    
                    <div class="panel panel-white">
                        <div class="panel-heading">
                            <h5 class="panel-title text-bold">Fatturazione</h5>
                            <div class="heading-elements">
                                <ul class="icons-list">
                                    <li><a data-action="collapse"></a></li>
                                </ul>
                            </div>
                        </div>
                        <div class="panel-body">
                            {% if order.user.profileType == 'privato' %}
                                <div class="form-group mb-5">
                                    <label class="col-lg-3 control-label text-semibold">Cognome/nome:</label>
                                    <div class="col-lg-9">
                                        <div class="form-control-static text-right-lg">{{ order.order.lastname }} {{ order.order.name }}</div>
                                    </div>
                                </div>
                                <div class="form-group mb-5">
                                    <label class="col-lg-3 control-label text-semibold">Cod.Fiscale:</label>
                                    <div class="col-lg-9">
                                        <div class="form-control-static text-right-lg">{{ order.order.tin }}</div>
                                    </div>
                                </div>    

                            {% else %}
                                <div class="form-group mb-5">
                                    <label class="col-lg-3 control-label text-semibold">Rag. Sociale:</label>
                                    <div class="col-lg-9">
                                        <div class="form-control-static text-right-lg">{{ order.order.fullname }}</div>
                                    </div>
                                </div>    
                                <div class="form-group mb-5">
                                    <label class="col-lg-3 control-label text-semibold">SDI:</label>
                                    <div class="col-lg-9">
                                        <div class="form-control-static text-right-lg">{{ order.order.sdiNumber }}</div>
                                    </div>
                                </div>    
                                <div class="form-group mb-5">
                                    <label class="col-lg-3 control-label text-semibold">PEC:</label>
                                    <div class="col-lg-9">
                                        <div class="form-control-static text-right-lg">{{ order.order.pec }}</div>
                                    </div>
                                </div>    
                            {% endif %}
                            <div class="form-group mb-5">
                                <label class="col-lg-3 control-label text-semibold">Indirizzo:</label>
                                <div class="col-lg-9">
                                    <div class="form-control-static text-right-lg">{{ order.order.address }} {{ order.order.city }} ({{ order.order.provinceCode }})</div>
                                </div>
                            </div>    
                            <div class="form-group mb-5">
                                <label class="col-lg-3 control-label text-semibold">Telefono:</label>
                                <div class="col-lg-9">
                                    <div class="form-control-static text-right-lg">{{ order.order.phoneNumber }}</div>
                                </div>
                            </div>    
                        </div>
                    </div>                    
                </div>
                {% endif %}
  
                <!-- pagamento -->
                {% if order.order.status != 'annulled' %}
                    <div class="col-sm-12 col-md-6 col-lg-3">                    
                        <div class="panel panel-white">
                            <div class="panel-heading">
                                <h5 class="panel-title text-bold">Pagamento</h5>
                                <div class="heading-elements">
                                    <ul class="icons-list">
                                        <li><a data-action="collapse"></a></li>
                                    </ul>
                                </div>
                            </div>
                            <div class="panel-body">
                                {% if order.order.couponCode %}
                                    <div class="form-group mb-5">
                                        <label class="col-lg-3 control-label text-semibold">Codice sconto:</label>
                                        <div class="col-lg-9">
                                            <div class="form-control-static text-right-lg">{{ order.order.couponCode }}</div>
                                        </div>   
                                    </div>                                       
                                {% else %}
                                    {# ... #}
                                {% endif %}
                                {% if order.order.externalPaymentKey %}
                                    <div class="form-group mb-5">
                                        <label class="col-lg-3 control-label text-semibold">Modalità:</label>
                                        <div class="col-lg-9">
                                            <div class="form-control-static text-right-lg">PAYPAL</div>
                                        </div>   
                                        <label class="col-lg-3 control-label text-semibold">Id transazione:</label>
                                        <div class="col-lg-9">
                                            <div class="form-control-static text-right-lg">{{ order.order.externalPaymentKey }}</div>
                                        </div>
                                    </div>                                       
                                {% else %}
                                    <div class="form-group mb-5">
                                        <label class="col-lg-3 control-label text-semibold">Modalità:</label>
                                        <div class="col-lg-9">
                                            <div class="form-control-static text-right-lg">BONIFICO</div>
                                        </div>   
                                    </div>                                      
                                {% endif %}
                            </div>
                        </div>                    
                    </div>
                {% endif %}
                <!-- /pagamento -->
                <!-- personalizzazione -->
                {% if order.order.status != 'annulled' %}
                    {% if order.order.customizedNote %}
                        <div class="col-sm-12 col-md-6 col-lg-3">                                            
                            <div class="panel panel-white">
                                <div class="panel-heading">
                                    <h5 class="panel-title text-bold">Note</h5>
                                    <div class="heading-elements">
                                        <ul class="icons-list">
                                            <li><a data-action="collapse"></a></li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="panel-body">
                                    <div class="form-group">
                                        <div class="col-lg-12">
                                            {{ order.order.customizedNote | raw }}
                                        </div>
                                    </div>
                                </div>
                            </div>                        
                        </div>
                    {% endif %}                    
                {% endif %}
                <!-- /personalizzazione -->
            </div>
        </div>               

    </div>
    <!-- END MAIN CONTENT -->

</div>
<!-- END PAGE CONTENT -->

{% endblock %}