{% extends "be/include/base.html" %}

{% block extrahead %}
{% if property.id is not empty %}
    <title>Visualizza pratica</title>
{% else %}
    <title>Nuova pratica</title>
{% endif %}

<!-- Theme Custom CSS files -->
<link href="https://siteria.it/libs/slim/4.2.1/slim/slim.min.css" rel="stylesheet">
<link href="https://siteria.it/libs/dropuploader/1.8.1/css/drop_uploader.min.css" rel="stylesheet">

<!-- Theme JS files -->
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/switchery.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/editable/editable.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jasny_bootstrap.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/uniform.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/autosize.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/formatter.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/typeahead/typeahead.bundle.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/typeahead/handlebars.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/maxlength.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/validation/validate.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/validation/localization/messages_it.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jquery_ui/interactions.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/selects/select2.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/wizards/steps.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/extensions/cookie.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/pickers/daterangepicker.js"></script>
<script src="https://siteria.it/libs/slim/4.2.1/slim/slim.kickstart.min.js"></script>
<script src="https://siteria.it/libs/jquery-mask/1.14.13/dist/jquery.mask.min.js"></script>
<script src="https://siteria.it/libs/sortable/1.7.0/Sortable.min.js"></script>
<script src="https://siteria.it/libs/autocomplete/1.0.4/auto-complete.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/app.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/ui/ripple.min.js"></script>
<script src="https://siteria.it/libs/dropuploader/1.8.1/js/drop_uploader.min.js"></script>
<script src="{{ contextPath }}/be/js/pages/procedures-add.js?{{ buildNumber }}"></script>
<!-- /theme JS files -->
{% endblock %}

{% block content %}

<!-- some hidden stuff -->
<a id="proceduresUri" class="no-display" href="{{ paths('PROCEDURES') }}?selectedStatuses=opened%7Cprocessing%7C" rel="nofollow"></a>
<a id="proceduresAddInfoSaveUri" class="no-display" href="{{ paths('PROCEDURES_ADD_INFO_SAVE') }}" rel="nofollow"></a>
<a id="proceduresAddSaveUri" class="no-display" href="{{ paths('PROCEDURES_ADD_SAVE') }}" rel="nofollow"></a>
<a id="imageUri" class="no-display" href="{{ paths('IMAGE') }}?oid=" rel="nofollow"></a>
<!-- api call for cities autocomplete -->
<a id="dataCitiesUri" style="display: none" href="{{ paths('DATA_CITIES') }}"></a>

<div id="imageIds" style="display: none;">
    {% if property.id is not empty %}
        {% if property.imageIds is not empty %}
            {% for imageId in property.imageIds %}{{ imageId }}|{% endfor %}
        {%endif%}
    {%endif%}
</div>

<!-- Page content -->
<div class="page-content">
    <!-- Main content -->
    <div class="content-wrapper">

        {% if user.profileType != 'admin' and user.active == false %}   
            <div class="row">
                <div class="col-md-12">
                    <div class="alert alert-danger">
                        <span>Attendi la conferma del tuo account prima di poter inserire una pratica</span>
                    </div>
                </div>
            </div>
        {% else %}
        
            {% if success %}
                <div class="row">
                    <div class="col-md-12">
                        <div class="alert alert-success">
                            <a href="{{ paths('PROCEDURES_ADD') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                            <span>Dati memorizzati correttamente</span>
                        </div>
                    </div>
                </div>
            {% elseif error %}
                <div class="row">
                    <div class="col-md-12">
                        <div class="alert alert-danger">
                            <a href="{{ paths('PROCEDURES_ADD') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                            <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Horizontal form options -->
            {% if property.id is not empty %}
                {% set saveUri = paths('PROCEDURES_ADD_SAVE') + '?procedureId=' + procedure.id + '&status=' %}
            {% else %}
                {% set saveUri = paths('PROCEDURES_ADD_SAVE')  + '?status='%}
            {% endif %}
            <div class="row">
                <div class="col-md-8 col-md-offset-2">
                    <!-- Wizard with validation -->
                    <div class="panel panel-white">
                        <div class="panel-heading">
                            <h6 class="panel-title text-bold">Inserisci la tua pratica in semplici e pochi passaggi!</h6>                        
                        </div>

                        <form id="form-procedure" class="steps-validation" action="#" enctype="multipart/form-data">

                            <input type="hidden" id="step" value="{{ step }}"/>
                            
                            <h6>Informazioni personali</h6>
                            <fieldset>
                                <hr>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="text-semibold">Seleziona la tipologia di pratica: <span class="text-danger">*</span></label>
                                            <select class="select" name="service" id="service" required>
                                                <option value="">-</option>
                                                {% if user.credit < 80 %}
                                                    {% set disabled = 'disabled' %}
                                                    {% set textDisabled = 'crediti non sufficienti' %}
                                                {% else %}
                                                    {% set disabled = '' %}
                                                    {% set textDisabled = '' %}
                                                {% endif %}
                                                <option {{ disabled }} value="conto_termico_pompa_di_calore" {{ procedure.service == 'conto_termico_pompa_di_calore' ? 'selected' : '' }}>Pompa di Calore (80 crediti) {{ textDisabled }}</option>
                                                {% if user.credit < 80 %}
                                                    {% set disabled = 'disabled' %}
                                                    {% set textDisabled = 'crediti non sufficienti' %}
                                                {% else %}
                                                    {% set disabled = '' %}
                                                    {% set textDisabled = '' %}
                                                {% endif %}                                                
                                                <option {{ disabled }} value="conto_termico_solare_termico" {{ procedure.service == 'conto_termico_solare_termico' ? 'selected' : '' }}>Solare Termico (80 crediti) {{ textDisabled }}</option>
                                                {% if user.credit < 90 %}
                                                    {% set disabled = 'disabled' %}
                                                    {% set textDisabled = 'crediti non sufficienti' %}
                                                {% else %}
                                                    {% set disabled = '' %}
                                                    {% set textDisabled = '' %}
                                                {% endif %}
                                                <option {{ disabled }} value="conto_termico_solare_stufa" {{ procedure.service == 'conto_termico_solare_stufa' ? 'selected' : '' }}>Stufa, Inserto, Termostufa (90 crediti) {{ textDisabled }}</option>
                                                {% if user.credit < 120 %}
                                                    {% set disabled = 'disabled' %}
                                                    {% set textDisabled = 'crediti non sufficienti' %}
                                                {% else %}
                                                    {% set disabled = '' %}
                                                    {% set textDisabled = '' %}
                                                {% endif %}
                                                <option {{ disabled }} value="conto_termico_caldaia_biomassa" {{ procedure.service == 'conto_termico_caldaia_biomassa' ? 'selected' : '' }}>Caldaia biomassa (120 crediti) {{ textDisabled }}</option>
                                                {% if user.credit < 80 %}
                                                    {% set disabled = 'disabled' %}
                                                    {% set textDisabled = 'crediti non sufficienti' %}
                                                {% else %}
                                                    {% set disabled = '' %}
                                                    {% set textDisabled = '' %}
                                                {% endif %}
                                                <option {{ disabled }} value="conto_termico_sistema_ibrido" {{ procedure.service == 'conto_termico_sistema_ibrido' ? 'selected' : '' }}>Sistema ibrido in pdc (80) {{ textDisabled }}</option>
                                                {% if user.credit < 80 %}
                                                    {% set disabled = 'disabled' %}
                                                    {% set textDisabled = 'crediti non sufficienti' %}
                                                {% else %}
                                                    {% set disabled = '' %}
                                                    {% set textDisabled = '' %}
                                                {% endif %}
                                                <option {{ disabled }} value="conto_termico_scaldacqua_pdc" {{ procedure.service == 'conto_termico_scaldacqua_pdc' ? 'selected' : '' }}>Scaldacqua in pdc (80) {{ textDisabled }}</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="text-semibold">Coupon rivenditore:</label>
                                            <input type="text" name="coupon" class="form-control maxlength" maxlength="50" placeholder="Digita coupon" value="{{ procedure.coupon }}">
                                            <small class="display-block">Se hai ricevuto un coupon rivenditore inseriscilo ora</small>
                                        </div>
                                    </div>
                                    {% if user.profileType == 'admin' or user.profileType == 'azienda' %}   
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="text-semibold">Il Soggetto Responsabile di questa pratica è:</label>
                                            <select class="select" name="profileType" id="profileType">
                                                <option value="privato" {{ procedure.profileType == 'privato' ? 'selected' : '' }}>Privato</option>
                                                <option value="azienda" {{ procedure.profileType == 'azienda' ? 'selected' : '' }}>Azienda</option>
                                            </select>
                                        </div>
                                    </div>
                                    {% else %}
                                        <input type="hidden" id="profileType" value="{{ procedure.profileType }}"/>
                                    {% endif %}

                                    <div id="businessForm">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="text-semibold">Ragione sociale: <span class="text-danger">*</span></label>
                                                {% set fullname = '' %}
                                                {% if procedure.fullname is not empty %}
                                                    {% set fullname = procedure.fullname %}
                                                {% elseif (procedure.name is not empty) and (procedure.lastname is not empty) %}
                                                    {% set fullname = (procedure.name | default('*Nome*')) + ' ' + procedure.lastname | default('*Cognome*') %}
                                                {% endif %}
                                                <input type="text" name="fullname" class="form-control maxlength" maxlength="100" placeholder="Ragione sociale" required value="{{ fullname }}">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="text-semibold">P.IVA: <span class="text-danger">*</span></label>
                                                <input type="text" name="vatNumber" class="form-control maxlength" maxlength="11" placeholder="Partita IVA" required value="{{ procedure.vatNumber }}">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6" id="nameForm">
                                        <div class="form-group">
                                            <label class="text-semibold">Nome: <span class="text-danger">*</span></label>
                                            <input type="text" name="name" class="form-control " placeholder="Nome" required value="{{ procedure.name }}">
                                        </div>
                                    </div>
                                    <div class="col-md-6"  id="lastnameForm">
                                        <div class="form-group">
                                            <label class="text-semibold">Cognome: <span class="text-danger">*</span></label>
                                            <input type="text" name="lastname" class="form-control " placeholder="Cognome" required value="{{ procedure.lastname }}">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6" id="tinForm">
                                        <div class="form-group">
                                            <label class="text-semibold">Codice fiscale: <span class="text-danger">*</span></label>
                                            <input type="text" name="tin" class="form-control maxlength " maxlength="16" placeholder="Codice fiscale" required value="{{ procedure.tin }}">
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="text-semibold">Indirizzo: <span class="text-danger">*</span></label>
                                            <input type="text" name="address" class="form-control maxlength " maxlength="100" placeholder="Indirizzo" required value="{{ procedure.address }}">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="text-semibold">Città: <span class="text-danger">*</span></label>
                                            <input type="text" name="city" id="city" class="form-control maxlength " maxlength="100" placeholder="Città" required value="{{ procedure.city }}">
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label class="text-semibold">Provincia: <span class="text-danger">*</span></label>
                                            <select class="form-control select-search " id="provinceCode" name="provinceCode" required value="{{ procedure.provinceCode }}">
                                                <option value="-">-</option>
                                                {% for item in lookup("province") %}
                                                    <option value="{{ item.code }}" {{ item.code == procedure.provinceCode ? 'selected' : ''}}>{{ item.description }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>

                                    <div class="col-md-2">
                                        <div class="form-group">
                                            <label class="text-semibold">CAP: <span class="text-danger">*</span></label>
                                            <input type="text" name="postalCode" id="postalCode" class="form-control maxlength " maxlength="5" placeholder="CAP" required value="{{ procedure.postalCode }}">
                                        </div>
                                    </div>
                                </div>

                                <!-- QUESTI OBBLIGATORI MA SOLO SE AZIENDA -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="text-semibold">Email: <span class="text-danger">*</span></label>
                                            <input type="email" name="email" class="form-control maxlength" maxlength="100" placeholder="Email" required value="{{ procedure.email }}">
                                        </div>
                                    </div>
                                    <div class="col-md-6" id="pecForm">
                                        <div class="form-group">
                                            <label class="text-semibold">PEC: <span class="text-danger"></span></label>
                                            <input type="email" name="pec" class="form-control maxlength" maxlength="100" placeholder="PEC" value="{{ procedure.pec }}">
                                        </div>
                                    </div>
                                </div>                            
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="text-semibold">Telefono: <span class="text-danger">*</span></label>
                                            <input type="text" name="phoneNumber" class="form-control maxlength" maxlength="20" placeholder="Telefono" required value="{{ procedure.phoneNumber }}">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="text-semibold">IBAN: <span class="text-danger">*</span></label>
                                            <input type="text" name="iban" class="form-control maxlength" maxlength="50" placeholder="IBAN" required value="{{ procedure.iban }}">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="text-semibold">Data ultimo pagamento:</label>
                                            <input type="date" name="lastPaymentDate" class="form-control" value="{{ procedure.lastPaymentDate | date('yyyy-MM-dd') }}">
                                            <small class="display-block">Data dell'ultimo pagamento ricevuto per questa pratica</small>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>

                            <h6>Titolarità immobile</h6>
                            <fieldset>
                                <hr>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="display-block text-semibold">Titolo di possesso dell'immobile</label>
                                            <label class="radio-inline">
                                                <input type="radio" name="ownership" class="styled" {{ ((procedure.ownership is empty) or (procedure.ownership == 'owner')) ? 'checked' : '' }} value="owner">
                                                Proprietario o comproprietario
                                            </label>

                                            <label class="radio-inline">
                                                <input type="radio" name="ownership" class="styled" {{ procedure.ownership == 'holder' ? 'checked' : '' }} value="holder">
                                                Detentore\Utilizzatore
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="text-semibold">Già registrati al sito del GSE? Indica Userid e Password oppure il PIN</label>
                                            <input type="text" name="pin" id="pin" class="form-control maxlength " maxlength="100" placeholder="Scrivi qui il tuo PIN" value="{{ procedure.pin }}">
                                        </div>
                                    </div>
                                </div>
                            </fieldset>

                            <h6>Caricamento documenti</h6>
                            <fieldset>
                                <hr>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="display-block text-semibold">Delega ad operare nel PortalTermico: <span class="text-danger">*</span></label>
                                            {% set fileIds = procedure.thermalPortalDelegationFileIds %}
                                            {% set listName = 'thermalPortalDelegationFileIds' %}
                                            {% if fileIds is not empty %}
                                                {% for fileId in fileIds %}
                                                    <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                {% endfor %}
                                            {% endif %}
                                            <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                            <div class="text-center">
                                                <input type="file" name="thermalPortalDelegationFileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple {{ fileIds is empty ? 'required' : '' }}>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="display-block text-semibold">Documento d'identità: <span class="text-danger">*</span></label>
                                            {% set fileIds = procedure.identityDocumentFileIds %}
                                            {% set listName = 'identityDocumentFileIds' %}
                                            {% if fileIds is not empty %}
                                                {% for fileId in fileIds %}
                                                    <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                {% endfor %}
                                            {% endif %}
                                            <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                            <div class="text-center">
                                                <input type="file" name="identityDocumentFileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple {{ fileIds is empty ? 'required' : '' }}>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="display-block text-semibold">Codice fiscale: <span class="text-danger">*</span></label>
                                            {% set fileIds = procedure.tinFileIds %}
                                            {% set listName = 'tinFileIds' %}
                                            {% if fileIds is not empty %}
                                                {% for fileId in fileIds %}
                                                    <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                {% endfor %}
                                            {% endif %}
                                            <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                            <div class="text-center">
                                                <input type="file" name="tinFileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple {{ fileIds is empty ? 'required' : '' }}>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="display-block text-semibold">Scheda raccolta dati: <span class="text-danger">*</span></label>
                                            {% set fileIds = procedure.dataCollectionFormFileIds %}
                                            {% set listName = 'dataCollectionFormFileIds' %}
                                            {% if fileIds is not empty %}
                                                {% for fileId in fileIds %}
                                                    <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                {% endfor %}
                                            {% endif %}
                                            <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                            <div class="text-center">
                                                <input type="file" name="dataCollectionFormFileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple {{ fileIds is empty ? 'required' : '' }}>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="display-block text-semibold">Modulo di autorizzazione del proprietario dell'immobile:</label>
                                            {% set fileIds = procedure.authorizationFormFileIds %}
                                            {% set listName = 'authorizationFormFileIds' %}
                                            {% if fileIds is not empty %}
                                                {% for fileId in fileIds %}
                                                    <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                {% endfor %}
                                            {% endif %}
                                            <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                            <div class="text-center">
                                                <input type="file" name="authorizationFormFileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="display-block text-semibold">Documento d'identità del proprietario dell'immobile:</label>
                                            {% set fileIds = procedure.identityDocumentOwnerFormFileIds %}
                                            {% set listName = 'identityDocumentOwnerFormFileIds' %}
                                            {% if fileIds is not empty %}
                                                {% for fileId in fileIds %}
                                                    <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                {% endfor %}
                                            {% endif %}
                                            <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                            <div class="text-center">
                                                <input type="file" name="identityDocumentOwnerFormFileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="display-block text-semibold">Fatture e bonifici inerenti all'intervento: <span class="text-danger">*</span></label>
                                            {% set fileIds = procedure.invoiceFileIds %}
                                            {% set listName = 'invoiceFileIds' %}
                                            {% if fileIds is not empty %}
                                                {% for fileId in fileIds %}
                                                    <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                {% endfor %}
                                            {% endif %}
                                            <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                            <div class="text-center">
                                                <input type="file" name="invoiceFileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple {{ fileIds is empty ? 'required' : '' }}>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="display-block text-semibold">Contratto di finanziamento:</label>
                                            {% set fileIds = procedure.contractFileIds %}
                                            {% set listName = 'contractFileIds' %}
                                            {% if fileIds is not empty %}
                                                {% for fileId in fileIds %}
                                                    <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                {% endfor %}
                                            {% endif %}
                                            <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                            <div class="text-center">
                                                <input type="file" name="contractFileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="display-block text-semibold">Scheda tecnica e certificato ambientale:</label>
                                            {% set fileIds = procedure.technicalSheetFileIds %}
                                            {% set listName = 'technicalSheetFileIds' %}
                                            {% if fileIds is not empty %}
                                                {% for fileId in fileIds %}
                                                    <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                {% endfor %}
                                            {% endif %}
                                            <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                            <div class="text-center">
                                                <input type="file" name="technicalSheetFileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="display-block text-semibold">Documento di Smaltimento:</label>
                                            {% set fileIds = procedure.disposalDocumentFileIds %}
                                            {% set listName = 'disposalDocumentFileIds' %}
                                            {% if fileIds is not empty %}
                                                {% for fileId in fileIds %}
                                                    <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                {% endfor %}
                                            {% endif %}
                                            <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                            <div class="text-center">
                                                <input type="file" name="disposalDocumentFileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="display-block text-semibold">Mandato all’incasso:</label>
                                            {% set fileIds = procedure.identityDocumentAgentFileIds %}
                                            {% set listName = 'identityDocumentAgentFileIds' %}
                                            {% if fileIds is not empty %}
                                                {% for fileId in fileIds %}
                                                    <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                {% endfor %}
                                            {% endif %}
                                            <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                            <div class="text-center">
                                                <input type="file" name="identityDocumentAgentFileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="display-block text-semibold">Documento d’identità mandatario:</label>
                                            {% set fileIds = procedure.cashingMandateFileIds %}
                                            {% set listName = 'cashingMandateFileIds' %}
                                            {% if fileIds is not empty %}
                                                {% for fileId in fileIds %}
                                                    <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                {% endfor %}
                                            {% endif %}
                                            <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                            <div class="text-center">
                                                <input type="file" name="cashingMandateFileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <legend class="text-semibold">Applicazione</legend>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <input type="hidden" id="tab" value="{{ tab }}"/>
                                        <div class="tabbable">
                                            <ul class="nav nav-tabs nav-tabs-highlight nav-justified">
                                                {% if not tab %}
                                                    {% set tab = '#highlighted-justified-tab1' %}
                                                {% endif %}
                                                <li class="{{ tab == '#highlighted-justified-tab1' ? 'active' : '' }} text-semibold"><a class="tabApplication" href="#highlighted-justified-tab1" data-toggle="tab">Pompe di calore</a></li>
                                                <li class="{{ tab == '#highlighted-justified-tab2' ? 'active' : '' }}"><a  class="tabApplication" href="#highlighted-justified-tab2" data-toggle="tab">Biomasse</a></li>
                                                {% set activeDropdown = '' %}
                                                {% if tab == '#highlighted-justified-tab3' or tab == '#highlighted-justified-tab4' or tab == '#highlighted-justified-tab5' %}
                                                    {% set activeDropdown = 'active' %}
                                                {% endif %}

                                                <li class="dropdown {{ activeDropdown }}">
                                                    <a href="#" class="dropdown-toggle" data-toggle="dropdown">Altro <span class="caret"></span></a>
                                                    <ul class="dropdown-menu dropdown-menu-right">
                                                        <li><a class="tabApplication" href="#highlighted-justified-tab3" data-toggle="tab">Solare termico</a></li>
                                                        <li><a class="tabApplication" href="#highlighted-justified-tab4" data-toggle="tab">Scaldaacqua a pompa di calore</a></li>
                                                        <li><a class="tabApplication" href="#highlighted-justified-tab5" data-toggle="tab">Sistemi ibridi a pompa di calore</a></li>
                                                    </ul>
                                                </li>
                                            </ul>

                                            <div class="tab-content">
                                                <div class="tab-pane {{ tab == '#highlighted-justified-tab1' ? 'active' : '' }}" id="highlighted-justified-tab1">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label class="display-block text-semibold">Targhe dei generatori sostituiti e installati (di ciascuna delle unità che costituiscono i generatori): <span class="text-danger">*</span></label>
                                                                {% set fileIds = procedure.plate1FileIds %}
                                                                {% set listName = 'plate1FileIds' %}
                                                                {% if fileIds is not empty %}
                                                                    {% for fileId in fileIds %}
                                                                        <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                                    {% endfor %}
                                                                {% endif %}
                                                                <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                                                <div class="text-center">
                                                                    <input type="file" name="plate1FileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple {{ fileIds is empty ? 'required' : '' }}>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label class="display-block text-semibold">Vista di dettaglio da distante e da vicino dei generatori interni sostituiti e installati: <span class="text-danger">*</span></label>
                                                                {% set fileIds = procedure.thermalPlant1FileIds %}
                                                                {% set listName = 'thermalPlant1FileIds' %}
                                                                {% if fileIds is not empty %}
                                                                    {% for fileId in fileIds %}
                                                                        <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                                    {% endfor %}
                                                                {% endif %}
                                                                <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                                                <div class="text-center">
                                                                    <input type="file" name="thermalPlant1FileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple {{ fileIds is empty ? 'required' : '' }}>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label class="display-block text-semibold">Vista di dettaglio da distante e da vicino dei generatori esterni sostituiti e installati: <span class="text-danger">*</span></label>
                                                                {% set fileIds = procedure.generator1FileIds %}
                                                                {% set listName = 'generator1FileIds' %}
                                                                {% if fileIds is not empty %}
                                                                    {% for fileId in fileIds %}
                                                                        <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                                    {% endfor %}
                                                                {% endif %}
                                                                <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                                                <div class="text-center">
                                                                    <input type="file" name="generator1FileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple {{ fileIds is empty ? 'required' : '' }}>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label class="display-block text-semibold">Valvole termostatiche o del sistema di regolazione modulante della portata:</label>
                                                                {% set fileIds = procedure.valves1FileIds %}
                                                                {% set listName = 'valves1FileIds' %}
                                                                {% if fileIds is not empty %}
                                                                    {% for fileId in fileIds %}
                                                                        <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                                    {% endfor %}
                                                                {% endif %}
                                                                <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                                                <div class="text-center">
                                                                    <input type="file" name="valves1FileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="tab-pane {{ tab == '#highlighted-justified-tab2' ? 'active' : '' }}" id="highlighted-justified-tab2">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label class="display-block text-semibold">Targhe dei generatori sostituiti e installati (di ciascuna delle unità che costituiscono i generatori): <span class="text-danger">*</span></label>
                                                                {% set fileIds = procedure.plate2FileIds %}
                                                                {% set listName = 'plate2FileIds' %}
                                                                {% if fileIds is not empty %}
                                                                    {% for fileId in fileIds %}
                                                                        <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                                    {% endfor %}
                                                                {% endif %}
                                                                <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                                                <div class="text-center">
                                                                    <input type="file" name="plate2FileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple {{ fileIds is empty ? 'required' : '' }}>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label class="display-block text-semibold">Vista di dettaglio dei collegamenti (canna fumaria e/o collegamenti idraulici) dei generatori sostituiti e installati: <span class="text-danger">*</span></label>
                                                                {% set fileIds = procedure.thermal2PlantFileIds %}
                                                                {% set listName = 'thermal2PlantFileIds' %}
                                                                {% if fileIds is not empty %}
                                                                    {% for fileId in fileIds %}
                                                                        <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                                    {% endfor %}
                                                                {% endif %}
                                                                <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                                                <div class="text-center">
                                                                    <input type="file" name="thermal2PlantFileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple {{ fileIds is empty ? 'required' : '' }}>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label class="display-block text-semibold">Vista di dettaglio da distante e da vicino dei generatori sostituiti e installati: <span class="text-danger">*</span></label>
                                                                {% set fileIds = procedure.generator2FileIds %}
                                                                {% set listName = 'generator2FileIds' %}
                                                                {% if fileIds is not empty %}
                                                                    {% for fileId in fileIds %}
                                                                        <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                                    {% endfor %}
                                                                {% endif %}
                                                                <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                                                <div class="text-center">
                                                                    <input type="file" name="generator2FileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple {{ fileIds is empty ? 'required' : '' }}>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label class="display-block text-semibold">Valvole termostatiche o del sistema di regolazione modulante della portata:</label>
                                                                {% set fileIds = procedure.valves2FileIds %}
                                                                {% set listName = 'valves2FileIds' %}
                                                                {% if fileIds is not empty %}
                                                                    {% for fileId in fileIds %}
                                                                        <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                                    {% endfor %}
                                                                {% endif %}
                                                                <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                                                <div class="text-center">
                                                                    <input type="file" name="valves2FileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label class="display-block text-semibold">Vista d'insieme del sistema di accumulo termico installato e relativa targa, dove previsto:</label>
                                                                {% set fileIds = procedure.globalStorage2FileIds %}
                                                                {% set listName = 'globalStorage2FileIds' %}
                                                                {% if fileIds is not empty %}
                                                                    {% for fileId in fileIds %}
                                                                        <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                                    {% endfor %}
                                                                {% endif %}
                                                                <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                                                <div class="text-center">
                                                                    <input type="file" name="globalStorage2FileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="tab-pane {{ tab == '#highlighted-justified-tab3' ? 'active' : '' }}" id="highlighted-justified-tab3">
                                                                  <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label class="display-block text-semibold">Vista di dettaglio del pannello solare installato: <span class="text-danger">*</span></label>
                                                                {% set fileIds = procedure.detailPanel3FileIds %}
                                                                {% set listName = 'detailPanel3FileIds' %}
                                                                {% if fileIds is not empty %}
                                                                    {% for fileId in fileIds %}
                                                                        <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                                    {% endfor %}
                                                                {% endif %}
                                                                <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                                                <div class="text-center">
                                                                    <input type="file" name="detailPanel3FileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple {{ fileIds is empty ? 'required' : '' }}>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label class="display-block text-semibold">Vista di dettaglio della targa dei collettori solari installati: <span class="text-danger">*</span></label>
                                                                {% set fileIds = procedure.detailPlate3FileIds %}
                                                                {% set listName = 'detailPlate3FileIds' %}
                                                                {% if fileIds is not empty %}
                                                                    {% for fileId in fileIds %}
                                                                        <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                                    {% endfor %}
                                                                {% endif %}
                                                                <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                                                <div class="text-center">
                                                                    <input type="file" name="detailPlate3FileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple {{ fileIds is empty ? 'required' : '' }}>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label class="display-block text-semibold">Vista di dettaglio del bollitore: <span class="text-danger">*</span></label>
                                                                {% set fileIds = procedure.detailBoiler3FileIds %}
                                                                {% set listName = 'detailBoiler3FileIds' %}
                                                                {% if fileIds is not empty %}
                                                                    {% for fileId in fileIds %}
                                                                        <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                                    {% endfor %}
                                                                {% endif %}
                                                                <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                                                <div class="text-center">
                                                                    <input type="file" name="detailBoiler3FileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple {{ fileIds is empty ? 'required' : '' }}>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label class="display-block text-semibold">Vista d'insieme del campo solare in fase di installazione:</label>
                                                                {% set fileIds = procedure.globalInstalling3FieldFileIds %}
                                                                {% set listName = 'globalInstalling3FieldFileIds' %}
                                                                {% if fileIds is not empty %}
                                                                    {% for fileId in fileIds %}
                                                                        <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                                    {% endfor %}
                                                                {% endif %}
                                                                <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                                                <div class="text-center">
                                                                    <input type="file" name="globalInstalling3FieldFileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label class="display-block text-semibold">Vista d'insieme del campo solare realizzato:</label>
                                                                {% set fileIds = procedure.globalField3FileIds %}
                                                                {% set listName = 'globalField3FileIds' %}
                                                                {% if fileIds is not empty %}
                                                                    {% for fileId in fileIds %}
                                                                        <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                                    {% endfor %}
                                                                {% endif %}
                                                                <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                                                <div class="text-center">
                                                                    <input type="file" name="globalField3FileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label class="display-block text-semibold">Valvole termostatiche o del sistema di regolazione modulante della portata, ove previste:</label>
                                                                {% set fileIds = procedure.valves3FileIds %}
                                                                {% set listName = 'valves3FileIds' %}
                                                                {% if fileIds is not empty %}
                                                                    {% for fileId in fileIds %}
                                                                        <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                                    {% endfor %}
                                                                {% endif %}
                                                                <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                                                <div class="text-center">
                                                                    <input type="file" name="valves3FileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="tab-pane {{ tab == '#highlighted-justified-tab4' ? 'active' : '' }}" id="highlighted-justified-tab4">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label class="display-block text-semibold">Vista di dettaglio dei generatori sostituiti e installati: <span class="text-danger">*</span></label>
                                                                {% set fileIds = procedure.detailGenerator4FileIds %}
                                                                {% set listName = 'detailGenerator4FileIds' %}
                                                                {% if fileIds is not empty %}
                                                                    {% for fileId in fileIds %}
                                                                        <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                                    {% endfor %}
                                                                {% endif %}
                                                                <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                                                <div class="text-center">
                                                                    <input type="file" name="detailGenerator4FileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple {{ fileIds is empty ? 'required' : '' }}>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label class="display-block text-semibold">Vista d'insieme dei generatori sostituiti e installati:</label>
                                                                {% set fileIds = procedure.globalGenerator4FileIds %}
                                                                {% set listName = 'globalGenerator4FileIds' %}
                                                                {% if fileIds is not empty %}
                                                                    {% for fileId in fileIds %}
                                                                        <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                                    {% endfor %}
                                                                {% endif %}
                                                                <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                                                <div class="text-center">
                                                                    <input type="file" name="globalGenerator4FileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label class="display-block text-semibold">Targa dei generatori installati:</label>
                                                                {% set fileIds = procedure.plate4FileIds %}
                                                                {% set listName = 'plate4FileIds' %}
                                                                {% if fileIds is not empty %}
                                                                    {% for fileId in fileIds %}
                                                                        <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                                    {% endfor %}
                                                                {% endif %}
                                                                <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                                                <div class="text-center">
                                                                    <input type="file" name="plate4FileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                <div class="tab-pane {{ tab == '#highlighted-justified-tab5' ? 'active' : '' }}" id="highlighted-justified-tab5">
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label class="display-block text-semibold">Targhe dei generatori sostituiti e installati (di ciascuna delle unità che costituiscono i generatori): <span class="text-danger">*</span></label>
                                                                {% set fileIds = procedure.plate5FileIds %}
                                                                {% set listName = 'plate5FileIds' %}
                                                                {% if fileIds is not empty %}
                                                                    {% for fileId in fileIds %}
                                                                        <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                                    {% endfor %}
                                                                {% endif %}
                                                                <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                                                <div class="text-center">
                                                                    <input type="file" name="plate5FileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple {{ fileIds is empty ? 'required' : '' }}>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label class="display-block text-semibold">Generatori sostituiti e installati:</label>
                                                                {% set fileIds = procedure.generator5FileIds %}
                                                                {% set listName = 'generator5FileIds' %}
                                                                {% if fileIds is not empty %}
                                                                    {% for fileId in fileIds %}
                                                                        <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                                    {% endfor %}
                                                                {% endif %}
                                                                <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                                                <div class="text-center">
                                                                    <input type="file" name="generator5FileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label class="display-block text-semibold">Centrale termica, o il locale di installazione, ante-operam (presente il generatore sostituito) e post-operam (presente il generatore installato):</label>
                                                                {% set fileIds = procedure.thermalPlant5FileIds %}
                                                                {% set listName = 'thermalPlant5FileIds' %}
                                                                {% if fileIds is not empty %}
                                                                    {% for fileId in fileIds %}
                                                                        <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                                    {% endfor %}
                                                                {% endif %}
                                                                <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                                                <div class="text-center">
                                                                    <input type="file" name="thermalPlant5FileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <div class="form-group">
                                                                <label class="display-block text-semibold">Valvole termostatiche o del sistema di regolazione modulante della portata:</label>
                                                                {% set fileIds = procedure.valves5FileIds %}
                                                                {% set listName = 'valves5FileIds' %}
                                                                {% if fileIds is not empty %}
                                                                    {% for fileId in fileIds %}
                                                                        <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('PROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                                    {% endfor %}
                                                                {% endif %}
                                                                <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                                                <div class="text-center">
                                                                    <input type="file" name="valves5FileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>

                            <h6>Conferma</h6>
                            <fieldset>
                                <hr>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="checkbox">
                                                <label>
                                                    <input id="terms" name="terms" type="checkbox" class="styled" required {{ procedure.terms ? 'checked' : '' }}>
                                                    Dichiaro di aver preso visione e di accettare le <a href="{{ contextPath }}/be/doc/condizioni-di-servizio.pdf" target="_blank" rel="noopener">condizioni di fornitura del servizio</a>
                                                </label>
                                            </div>
                                            <div class="checkbox">
                                                <label>
                                                    <input id="privacy" name="privacy" type="checkbox" class="styled" required {{ procedure.privacy ? 'checked' : '' }}>
                                                    Dichiaro di aver preso visione e di accettare l'<a href="https://www.iubenda.com/privacy-policy/21639774" target="_blank" rel="noopener">informativa Privacy</a>
                                                </label>
                                            </div>
                                            <div class="checkbox">
                                                <label>
                                                    <input id="priority" name="priority" type="checkbox" class="styled" {{ procedure.priority ? 'checked' : '' }}>
                                                    Servizio FAST (selezionando questo servizio la pratica verrà lavorata in 2 giorni lavorativi, il costo è di 30 euro (30 crediti) che si aggiunge al normale costo pratica)
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>
                        </form>
                    </div>
                    <!-- /wizard with validation -->
                </div>
            </div>
        {% endif %}

    </div>
    <!-- /main content -->

</div>
<!-- /page content -->

<script>
    $('.btn-cancel').click(function () {
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-cancel-square icon-2x"></i><br/><br/> Sei sicuro?',
            content: "Le modifiche fatte fin'ora verranno perse.",
            buttons: {
                annulla: {
                    btnClass: 'btn-default',
                    action: function () {
                        //
                    }
                },
                conferma: {
                    btnClass: 'bg-success',
                    action: function () {
                        window.location.href = "{{ paths('PROCEDURES') }}?selectedStatuses=opened%7Cprocessing%7C";
                    }
                }
            }
        });
    });
</script>

{% endblock %}
