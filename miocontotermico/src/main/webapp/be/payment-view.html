{% extends "be/include/base.html" %}
{% block extrahead %}
    <title>Pagamento {{ payment.name | upper }}</title>

    <!-- CSS -->
    <link href="https://siteria.it/libs/slim/4.2.1/slim/slim.min.css" rel="stylesheet" type="text/css" media="all">

    <!-- SCRIPTS -->
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/styling/switchery.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/selects/select2.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/inputs/maxlength.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/validate.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/localization/messages_it.js"></script>
    <script src="https://siteria.it/libs/slim/4.2.1/slim/slim.kickstart.min.js"></script>
    <script src="{{ contextPath }}/be/js/pages/payment-view.js?{{ buildNumber }}"></script>
{% endblock %}

{% block content %}

<!-- actions -->
<!-- ... -->

<!-- PAGE CONTENT -->
<div class="page-content">

    <!-- MAIN CONTENT -->
    <div class="content-wrapper">

        {% if success %}
            <div class="row">
                <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                    <div class="alert alert-success">
                        <a href="{{ paths('PAYMENT_VIEW') }}?paymentId={{ payment.id }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                        <span>Dati memorizzati correttamente</span>
                    </div>
                </div>
            </div>
        {% elseif error %}
            <div class="row">
                <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                    <div class="alert alert-danger">
                        <a href="{{ paths('PAYMENT_VIEW') }}?paymentId={{ payment.id }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                        <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                    </div>
                </div>
            </div>
        {% endif %}

        <!-- PAYMENT VIEW -->
        <div class="row">
            <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                <div class="tabbable">
                    <div class="tab-content">
                        <div class="tab-pane fade in active" id="activity">

                            <!-- Profile info -->
                            <form id="form-payment" class="form-horizontal form-validate-jquery" method="post" action="{{ paths('PAYMENT_VIEW_UPDATE') }}?paymentId={{ payment.id }}">
                                <div class="panel panel-white">
                                    <div class="panel-heading has-visible-elements">
                                        <h5 class="panel-title text-bold">Pagamento {{ payment.name | upper }}</h5>
                                        <div class="heading-elements">
                                            <a href="{{ paths('PAYMENTS') }}" class="btn heading-btn btn-default btn-cancel">Torna alla lista</a>
                                        </div>
                                    </div>

                                    <div class="panel-body">
                                        <div class="form-group">
                                            <label class="col-lg-3 control-label">Logo:</label>
                                            <div class="col-lg-9">
                                                <div class="row">
                                                    <div class="col-xs-6 col-xs-offset-3 col-sm-4 col-sm-offset-4 col-md-4 col-md-offset-4">
                                                        <div class="slim"
                                                             data-max-file-size="5"
                                                             data-save-initial-image="{{ payment.imageIds[0] is not empty ? 'true' : 'false'}}"
                                                             data-push="false"
                                                             data-post="output"
                                                             data-label="Carica un'immagine"
                                                             data-label-loading=" "
                                                             data-ratio="free"
                                                             data-button-edit-label="Modifica"
                                                             data-button-remove-label="Elimina"
                                                             data-button-download-label="Scarica"
                                                             data-button-upload-label="Carica"
                                                             data-button-rotate-label="Ruota"
                                                             data-button-cancel-label="Cancella"
                                                             data-button-confirm-label="Conferma"
                                                             data-status-file-size="Il file è troppo grande, il massimo consentito è $0 MB"
                                                             data-status-file-type="Formato immagine non valido, formati consentiti: $0"
                                                             data-status-no-support="Il tuo browser non supporta la modifica dell'immagine"
                                                             data-status-image-too-small="Immagine troppo piccola, risoluzione minima: $0 pixel"
                                                             data-status-content-length="Il server non supporta file così grandi"
                                                             data-status-unknown-response="Errore sconosciuto, <NAME_EMAIL>"
                                                             data-status-upload-success="Immagine salvata"
                                                             data-size="400,400">

                                                            {% if payment.imageIds[0] is not empty %}
                                                                <img src="{{ paths('IMAGE') }}?oid={{ payment.imageIds[0] }}" alt=""/>
                                                            {% endif %}
                                                            <input type="file" id="cropper" name="uploaded-files" data-show-caption="false" data-show-remove="true">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-lg-3 control-label">Codice</label>
                                            <div class="col-lg-9">
                                                <input type="text" name="code" class="form-control maxlength" maxlength="50" value="{{ payment.code }}" readonly>
                                            </div>
                                        </div>
                                        <div class="tabbable">
                                            <div class="form-group no-margin-bottom">
                                                <label class="col-lg-3 control-label">Inserisci pagamento in:</label>
                                                <div class="col-lg-9">
                                                    <ul class="nav nav-tabs language bg-slate-300 nav-tabs-component">
                                                        <li class="active" id="tab-it"><a href="#it" data-toggle="tab">Italiano</a></li>
                                                        <li id="tab-en"><a href="#en" data-toggle="tab">English</a></li>
                                                    </ul>
                                                </div>
                                            </div>
                                            <div class="tab-content">
                                                <div class="tab-pane active" id="it">
                                                    <div class="form-group">
                                                        <label class="col-lg-3 control-label">Nome:</label>
                                                        <div class="col-lg-9">
                                                            <input type="text" name="name" class="form-control maxlength" maxlength="50" value="{{ payment.name }}" readonly>
                                                        </div>
                                                    </div>
                                                    <div class="form-group">
                                                        <label class="col-lg-3 control-label">Descrizione:</label>
                                                        <div class="col-lg-9">
                                                            {{ payment.description | raw }}
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="tab-pane" id="en">
                                                    <div class="form-group">
                                                        <label class="col-lg-3 control-label">Name:</label>
                                                        <div class="col-lg-9">
                                                            <input type="text" name="nameEnglish" class="form-control maxlength" maxlength="50" value="{{ payment.nameEnglish }}" readonly>
                                                        </div>
                                                    </div>
                                                    <div class="form-group">
                                                        <label class="col-lg-3 control-label">Description:</label>
                                                        <div class="col-lg-9">
                                                            {{ payment.descriptionEnglish | raw }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-lg-3 control-label">Tipo pagamento:</label>
                                            <div class="col-lg-9">
                                                <div class="form-group">
                                                    <select class="select" id="paymentType" name="paymentType" disabled>
                                                        <option value="manual" {{ payment.paymentType == 'manual' ? 'selected' : '' }}>-</option>
                                                        <option value="nexi_intesa" {{ payment.paymentType == 'nexi_intesa' ? 'selected' : '' }}>Nexi (Intesa)</option>
                                                        <!--<option value="nexi" {{ payment.paymentType == 'nexi' ? 'selected' : '' }}>Nexi</option>-->
                                                        <!--<option value="nexi_triveneto" {{ payment.paymentType == 'nexi_triveneto' ? 'selected' : '' }}>Nexi (Triveneto)</option>-->
                                                        <option value="paypal" {{ payment.paymentType == 'paypal' ? 'selected' : '' }}>Paypal</option>
                                                    </select>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="panel-footer has-visible-elements">
                                        <div class="heading-elements">
                                            <span class="heading-text text-semibold">Azioni:</span>
                                            <div class="pull-right">
                                                <a href="{{ paths('PAYMENTS') }}" class="btn heading-btn btn-default btn-cancel">Torna alla lista</a>
                                                <!--<button id="payment-remove" type="button" class="btn btn-danger">Elimina</button>-->
                                                <!--<button type="submit" class="btn bg-brown-600">Aggiorna informazioni <i class="icon-arrow-right14"></i></button>-->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- /profile info -->
                            </form>

                        </div>

                    </div>
                </div>
            </div>

        </div>
        <!-- /user profile -->

    </div>
    <!-- END MAIN CONTENT -->

</div>
<!-- END PAGE CONTENT -->
{% endblock %}
