{% extends "be/include/base.html" %}

{% set currentPage = 'PAPERWORKS' %}

{% block extrahead %}
<title>Lista Documenti</title>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jasny_bootstrap.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/uniform.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/datatables.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/responsive.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/buttons.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/selects/select2.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/core/app.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/ui/ripple.min.js"></script>
<script src="https://www.siteria.it/libs/b-lazy/1.8.2/blazy.min.js"></script>
<script src="{{ contextPath }}/be/js/pages/paperworks.js?{{ buildNumber }}"></script>
{% endblock %}

{% block content %}

<!-- Page content -->
<div class="page-content">
    
    <!-- Main content -->
    <div class="content-wrapper">
        {% if success %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-success">
                    <a href="{{ paths('PAPERWORKS') }}" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                    <span>Informazini salvate correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-danger">
                    <a href="{{ paths('PAPERWORKS') }}" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza.</a></span>
                </div>
            </div>
        </div>
        {% endif %}
        

        <!-- State saving -->
        <div class="panel panel-white">
            <div class="panel-heading">
                <h5 class="panel-title text-bold">Lista Documenti</h5>
                <div class="heading-elements">
                    <div class="form-group">
                        <a href="{{ paths('PAPERWORK_EDIT') }}" class="btn bg-success legitRipple"><i class="icon-plus-circle2 position-left"></i>Aggiungi documento</a>                        
                    </div>
                </div>
            </div>

            {# ?????? @mike: gestire table server-side #}
            <table class="table datatable-paperworks">
                <thead>
                    <tr>
                        <th>Titolo</th>
                        <th>Visibile</th>
                        <th>Allegato</th>
                        <th>Creato il</th>
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    {% if paperworkList is not empty %}
                        {% for entry in paperworkList %}
                            <tr>
                                <td>
                                    <a href="{{ paths('PAPERWORK_EDIT') }}?paperworkId={{ entry.id }}" class="text-bold">{{ entry.title }} </a>
                                </td>
                                <td>
                                    {% if entry.visible %}
                                        <span class="status-mark bg-green-400 position-left"></span>
                                        SI
                                    {% else %}
                                        <span class="status-mark bg-danger position-left"></span>
                                        NO
                                    {% endif %}
                                </td>
                                <td>
                                    {% if entry.fileIds is not empty %}
                                        PRESENTE
                                    {% else %}
                                        <i class="icon-warning22 text-orange-400 position-left"></i> NON PRESENTE
                                    {% endif %}
                                </td>
                                <td>
                                    <span sortable-value="{{ entry.creation | date('yyyyMMddHHmm') }}">{{ entry.creation | date('dd MMMM yyyy HH:mm') }}</span>
                                </td>
                                <td></td>
                            </tr>
                        {% endfor %}
                    {% endif %}
                </tbody>
            </table>
        </div>
        <!-- /state saving -->

    </div>
    <!-- /main content -->
</div>
<!-- /page content -->
{% endblock %}