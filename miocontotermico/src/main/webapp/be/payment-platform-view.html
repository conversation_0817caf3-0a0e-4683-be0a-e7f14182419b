{% extends "be/include/base.html" %}

{% block extrahead %}
    <title>Piattaforma di pagamento</title>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/styling/switchery.min.js"></script>
    <script src="https://siteria.it/libs/jquery-mask/1.14.13/dist/jquery.mask.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/inputs/maxlength.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/validate.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/localization/messages_it.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/selects/select2.min.js"></script>
    <script src="https://siteria.it/libs/slim/4.2.1/slim/slim.kickstart.min.js"></script>
    <script src="{{ contextPath }}/be/js/pages/payment-platform-view.js?{{ buildNumber }}"></script>
{% endblock %}

{% block content %}
    <a id="paymentPlatformViewAbortUri" style="display: none" href="{{ paths('PAYMENT_PLATFORMS') }}"></a>
    <a id="paymentPlatformRemoveUri" style="display: none;" href="{{ paths('PAYMENT_PLATFORM_REMOVE') }}?paymentPlatformId={{ paymentPlatform.id }}" rel="nofollow"></a>
    <a id="paymentPlatformsSuccessUri" style="display: none;" href="{{ paths('PAYMENT_PLATFORMS') }}/ok" rel="nofollow"></a>
    
    <!-- Page content -->
    <div class="page-content">

        <!-- Main content -->
        <div class="content-wrapper">
            <div class="row">
                <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                    {% if success %}
                        <div class="row">
                            <div class="col-md-12">
                                <div class="alert alert-success">
                                    <a href="{{ paths('PAYMENT_PLATFORMS') }}" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                                    <span>Informazioni salvate correttamente</span>
                                </div>
                            </div>
                        </div>
                    {% elseif error %}
                        <div class="row">
                            <div class="col-md-12">
                                <div class="alert alert-danger">
                                    <a href="{{ paths('PAYMENT_PLATFORMS') }}" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                                    <span>Errore durante il salvataggio dei dati. Hai bisogno di aiuto? <a href="mailto:<EMAIL>">Contatta il servizio clienti</a></span>
                                </div>
                            </div>
                        </div>
                    {% endif %}

                    <!-- Smtp info -->
                    <form id="form-edit-paymentplatform" class="form-horizontal form-validate-jquery" method="post" action="{{ paths('PAYMENT_PLATFORM_VIEW_UPDATE') + '?paymentPlatformId=' + paymentPlatform.id }}">
                        <div class="panel panel-white">
                            <div class="panel-heading">
                                <h5 class="panel-title text-bold">Piattaforma di pagamento</h5>                                        
                            </div>
                            <div class="panel-body">
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Tipo:</label>
                                    <div class="col-lg-9">
                                        <div class="form-group">
                                            <select class="select" id="paymentType" name="paymentType" required>
                                                <option value="manual" {{ paymentPlatform.paymentType == 'manual' ? 'selected' : '' }}>-</option>
                                                <!--<option value="nexi" {{ paymentPlatform.paymentType == 'nexi' ? 'selected' : '' }}>Nexi</option>-->
                                                <option value="nexi_intesa" {{ paymentPlatform.paymentType == 'nexi_intesa' ? 'selected' : '' }}>Nexi (Intesa)</option>
                                                <!--<option value="nexi_triveneto" {{ paymentPlatform.paymentType == 'nexi_triveneto' ? 'selected' : '' }}>Nexi (Triveneto)</option>-->
                                                <option value="paypal" {{ paymentPlatform.paymentType == 'paypal' ? 'selected' : '' }}>Paypal</option>
                                                <option value="stripe" {{ paymentPlatform.paymentType == 'stripe' ? 'selected' : '' }}>Stripe</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Ambiente:</label>
                                    <div class="col-lg-9">
                                        <div class="form-group">
                                            <select class="select" id="environmentType" name="environmentType">
                                                <option value="sandbox" {{ paymentPlatform.environmentType == 'sandbox' ? 'selected' : '' }}>Prova</option>
                                                <option value="live" {{ paymentPlatform.environmentType == 'live' ? 'selected' : '' }}>Produzione</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Codice cliente:</label>
                                    <div class="col-lg-9">
                                        <input type="text" name="alias" class="form-control" placeholder="Codice identificativo del profilo esercente" value="{{ paymentPlatform.alias }}" required>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Chiave segreta:</label>
                                    <div class="col-lg-9">
                                        <input type="text" name="secretKey" class="form-control" placeholder="Chiave segreta" value="{{ paymentPlatform.secretKey }}" required>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Endpoint:</label>
                                    <div class="col-lg-9">
                                        <input type="text" name="endpoint" class="form-control" placeholder="Endpoint del servizio" value="{{ paymentPlatform.endpoint }}">
                                    </div>
                                </div>
                            </div>
                            <div class="panel-footer has-visible-elements">
                                <div class="heading-elements">
                                    <span class="heading-text text-semibold">Azioni:</span>
                                    <div class="pull-right">                                        
                                        <button type="button" class="btn heading-btn btn-default btn-cancel">Annulla</button>
                                        <button id="btn-delete" type="button" class="btn heading-btn heading-btn btn-danger">Elimina</button>
                                        <button id="btn-save" type="submit" class="btn heading-btn bg-success">Salva<i class="icon-arrow-right14"></i></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

            </div>

        </div>
        <!-- /main content -->

    </div>
    <!-- /page content -->
{% endblock %}
