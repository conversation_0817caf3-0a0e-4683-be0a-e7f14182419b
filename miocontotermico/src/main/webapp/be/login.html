<!DOCTYPE html>
<html lang="it">
    <head>
        <!-- Links -->
        {% include "be/include/snippets/head.html" %}
        {% block extrahead %}
        <title>Login</title>
        <script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/app.js"></script>
        <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/uniform.min.js"></script>
        
        <script src="{{ contextPath }}/be/js/pages/login.js?{{ buildNumber }}"></script>        
        <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/ui/ripple.min.js"></script>
        <!-- /theme JS files -->
        {% endblock %}
    </head>
    <body class="navbar-bottom login-container">
        <!-- Main navbar -->
        <div class="navbar navbar-inverse bg-success has-shadow">
            <div class="navbar-header">
                <a class="navbar-brand" href="{{ paths('PROCEDURES') }}?selectedStatuses=opened%7Cprocessing%7C"><img src="https://www.miocontotermico.it/imgs/logo_light.png" alt=""></a>
                <ul class="nav navbar-nav pull-right visible-xs-block">
                    <li><a data-toggle="collapse" data-target="#navbar-mobile"><i class="icon-grid3"></i></a></li>
                </ul>
            </div>
            <div class="navbar-collapse collapse" id="navbar-mobile">
                <ul class="nav navbar-nav">
                    <li><a href="mailto:<EMAIL>">Assistenza</a></li>
                </ul>
            </div>
        </div>
        <!-- /main navbar -->
        <!-- Page container -->
        <div class="page-container">
            <!-- Page content -->
            <div class="page-content">
                <!-- Main content -->
                <div class="content-wrapper">
                    <!-- Simple login form -->
                    <form method="post" action="{{ paths('LOGIN_DO') }}" class="login-form">
                        <div class="panel panel-body">
                            <div class="text-center">
                                <div class="icon-object border-success text-success"><i class="icon-home2"></i></div>
                                <h5 class="content-group">Login <small class="display-block">Inserisci le credenziali</small></h5>
                            </div>                            
                            {% if success %}                         
                            {% elseif error %}                                
                                <div class="alert bg-danger-400 alert-styled-left">
                                    <button type="button" class="close" data-dismiss="alert"><span>×</span><span class="sr-only">Close</span></button>
                                    Credenziali errate
                                </div>                                
                            {% endif %}
                            <div class="form-group has-feedback has-feedback-left">
                                <input type="email" id="email" name="email" class="form-control" required>
                                <div class="form-control-feedback">
                                    <i class="icon-user text-muted"></i>
                                </div>
                            </div>
                            <div class="form-group has-feedback has-feedback-left">
                                <input type="password" id="password" name="password" class="form-control" required>
                                <div class="form-control-feedback">
                                    <i class="icon-lock2 text-muted"></i>
                                </div>
                            </div>
                            <div class="form-group login-options">
                                <div class="row">
                                    <div class="col-sm-4">
                                        <div class="checkbox">
                                            <label>
                                                <input id="remember" class="styled" name="remember" type="checkbox" checked="checked">
                                                Ricordami
                                            </label>
                                        </div>
                                    </div>

                                    <div class="col-sm-8 text-right">
                                        <a href="{{ paths('FORGOT') }}" class="checkbox">Password dimenticata?</a>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <button type="submit" class="btn bg-success btn-block">ACCEDI <i class="icon-circle-right2 position-right"></i></button>
                            </div>
                        </div>
                    </form>
                    <!-- /simple login form -->
                </div>
                <!-- /main content -->
            </div>
            <!-- /page content -->
        </div>
        <!-- /page container -->
        <!-- Footer -->
        {% include "be/include/snippets/footer.html" %}
        <!-- /footer -->
    </body>
</html>