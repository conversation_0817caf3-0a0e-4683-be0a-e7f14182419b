{% extends "be/include/base.html" %}

{% block extrahead %}
    <title>Pagamento ordine</title>

    <!-- CSS -->
    <link href="https://siteria.it/libs/slim/4.2.1/slim/slim.min.css" rel="stylesheet" type="text/css" media="all">

    <!-- SCRIPTS -->
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jasny_bootstrap.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/uniform.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/switchery.min.js"></script>    
    
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jquery_ui/interactions.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/app.js"></script>
    <script src="https://siteria.it/libs/slim/4.2.1/slim/slim.kickstart.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/ui/ripple.min.js"></script>
{% endblock %}

{% block content %}

<!-- PAGE CONTENT -->
<div class="page-content">

    <!-- MAIN CONTENT -->
    <div class="content-wrapper">

        <!-- User profile -->
        <div class="row">
            {% if confirmed %}
                <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                    <div class="panel panel-white">
                        <div class="panel-heading">
                            <h5 class="panel-title text-bold">Crediti ordinati</h5>                            
                        </div>

                        <div class="alert alert-info alert-styled-left alert-arrow-left alert-component">                            
                            <h6 class="alert-heading text-semibold">Gentile cliente,</h6>
                            Grazie per aver acquistato i nostri servizi!
                            {% if order.paymentCode == 'bonifico' or order.paymentCode == 'BONIFICO' %}
                            <br>
                                Per completare l'acquisto effettua il bonifico di {{ order.finalPrice | numberformat("#0.00") }}€ alle seguenti coordinate:
                                <br>
                                <br>
                                Causale: acquisto crediti per pratiche portale MioContoTermico
                                <br>
                                Beneficiario: SOGENIT srl
                                <br>
                                IBAN: ***************************
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% else %}                    
                <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                    <div class="panel panel-white">
                        <div class="panel-heading">
                            <h5 class="panel-title text-bold">Acquisto crediti NON COMPLETATO</h5>                            
                        </div>
                        <div class="panel-heading">
                            <div class="alert alert-danger alert-styled-left alert-arrow-left alert-component">                            
                                <h6 class="alert-heading text-semibold">Gentile cliente,</h6>
                                Il pagamento dei servizi non è andato a buon fine!
                            </div>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
        <!-- /user profile -->

    </div>
    <!-- END MAIN CONTENT -->

</div>
<!-- END PAGE CONTENT -->
{% endblock %}
