{% extends "be/include/base.html" %}

{% block extrahead %}
    <title>Nuovo utente di sistema</title>    
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/switchery.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jasny_bootstrap.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/uniform.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/autosize.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/formatter.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/typeahead/typeahead.bundle.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/typeahead/handlebars.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/maxlength.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/validation/validate.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/validation/localization/messages_it.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jquery_ui/interactions.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/selects/select2.min.js"></script>    
    <script src="https://siteria.it/libs/jquery-mask/1.14.13/dist/jquery.mask.min.js"></script>
    <script src="https://siteria.it/libs/sortable/1.7.0/Sortable.min.js"></script>
    <script src="https://siteria.it/libs/autocomplete/1.0.4/auto-complete.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/app.js"></script>
    <script src="{{ contextPath }}/be/js/pages/system-user-add.js?{{ buildNumber }}"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/ui/ripple.min.js"></script>
{% endblock %}

{% block content %}
    <!-- Page content -->
    <div class="page-content">

        <!-- Main content -->
        <div class="content-wrapper">

            <!-- User profile -->
            <div class="row">
                
                <div class="col-md-8 col-md-offset-2">                   

                    <!-- Profile info -->
                    <form id="form-add-user" class="form-horizontal form-validate-jquery" method="post" action="{{ paths('SYSTEM_USERS_ADD_SAVE') }}">
                        <div class="panel panel-white">
                            <div class="panel-heading">
                                <h5 class="panel-title text-bold">Nuovo utente di sistema</h5>                                        
                            </div>
                            <div class="panel-body">                                
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Tipo:</label>
                                    <div class="col-lg-9">
                                        <div class="form-group">
                                            <select class="select" name="profileType" id="profileType">
                                                <option value="admin" {{ userRegistered.profileType == 'admin' ? 'selected' : '' }}>Admin</option>
                                                <option value="operatore" {{ userRegistered.profileType == 'operatore' ? 'selected' : '' }}>Operatore</option>
                                            </select>
                                        </div>
                                    </div>
                                </div> 
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Nome:</label>
                                    <div class="col-lg-9">
                                        <input type="text" name="name" class="form-control maxlength" maxlength="50" placeholder="Nome" value="{{ userRegistered.name }}">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Cognome:</label>
                                    <div class="col-lg-9">
                                        <input type="text" name="lastname" class="form-control maxlength" maxlength="50" placeholder="Cognome" value="{{ userRegistered.lastname }}">
                                    </div>
                                </div>                                                                
                                <legend class="text-bold">
                                    <i class="icon-key"></i>
                                    CREDENZIALI ACCESSO
                                    <a class="control-arrow" data-toggle="collapse" data-target="#credential">
                                        <i class="icon-circle-down2"></i>
                                    </a>
                                </legend>
                                <div class="collapse in" id="credential">
                                    <div class="form-group">
                                        <label class="col-lg-3 control-label">Email:</label>
                                        <div class="col-lg-9">
                                            <input type="email" id="username" name="username" class="form-control" value="{{ userRegistered.username }}" required>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-lg-3 control-label">Password:</label>
                                        <div class="col-lg-9">
                                            <input type="password" name="password" class="form-control" placeholder="Password" value="{{ userRegistered.password }}" required>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-lg-3 control-label">Conferma password:</label>
                                        <div class="col-lg-9">
                                            <input type="password" name="password-confirm" class="form-control" placeholder="Conferma password" value="{{ userRegistered.password }}" required data-parsley-equalto="#password">
                                        </div>
                                    </div>                                            
                                </div>                                
                            </div>
                            <div class="panel-footer">
                                    <div class="heading-elements">
                                        <span class="heading-text text-semibold">Azioni:</span>
                                        <div class="heading-btn pull-right">
                                            <button type="button" class="btn btn-default btn-cancel">Annulla</button>
                                            <button type="submit" class="btn bg-success">Inserisci utente <i class="icon-arrow-right14"></i></button>
                                        </div>
                                    </div>
                                </div>
                        </div>
                    </form>
                </div>

            </div>
            <!-- /user profile -->

        </div>
        <!-- /main content -->

    </div>
    <!-- /page content -->
    <script>
        $(function () {
            $('.btn-cancel').click(function () {
                $.confirm({
                    theme: 'supervan',
                    escapeKey: true,
                    animation: 'top',
                    closeAnimation: 'bottom',
                    backgroundDismiss: true,
                    title: '<i class="icon-cancel-square icon-2x"></i><br/><br/> Sei sicuro?',
                    content: "Le modifiche fatte fin'ora verranno perse.",
                    buttons: {
                        annulla: {
                            btnClass: 'btn-default',
                            action: function () {
                                //
                            }
                        },
                        conferma: {
                            btnClass: 'bg-success',
                            action: function () {
                                window.location.href = "{{ paths('SYSTEM_USERS_ADD') }}";
                            }
                        }
                    }
                });
            });
        });
    </script>
{% endblock %}
