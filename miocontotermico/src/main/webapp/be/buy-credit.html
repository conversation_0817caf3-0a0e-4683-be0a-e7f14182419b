{% extends "be/include/base.html" %}

{% block extrahead %}
    <title>Acquista crediti</title>

    <!-- CSS -->
    <link href="https://siteria.it/libs/slim/4.2.1/slim/slim.min.css" rel="stylesheet" type="text/css" media="all">

    <!-- SCRIPTS -->
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jasny_bootstrap.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/uniform.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/switchery.min.js"></script>    
    
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jquery_ui/interactions.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/app.js"></script>
    <script src="https://siteria.it/libs/slim/4.2.1/slim/slim.kickstart.min.js"></script>
    <script src="{{ contextPath }}/be/js/pages/buy-credit.js?{{ buildNumber }}"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/ui/ripple.min.js"></script>
{% endblock %}

{% block content %}

<!-- actions -->
<a id="buyCreditUrl" style="display: none" href="{{ paths('BUY_CREDIT') }}" rel="nofollow"></a>
<a id="buyCreditSendUri" style="display: none" href="{{ paths('BUY_CREDIT_SEND') }}" rel="nofollow"></a>
<a id="paidUri" style="display: none" href="{{ paths('PAID') }}" rel="nofollow"></a>
    
<!-- PAGE CONTENT -->
<div class="page-content">

    <!-- MAIN CONTENT -->
    <div class="content-wrapper">

        {% if success %}
        <div class="row">
            <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                <div class="alert alert-success">
                    <a href="{{ paths('BUY_CREDIT') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Dati memorizzati correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                <div class="alert alert-danger">
                    <a href="{{ paths('BUY_CREDIT') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- User profile -->
        <div class="row">
            <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">

                <!-- Profile info -->
                <form id="buy-credit" class="form-horizontal form-validate-jquery" method="post">
                    <div class="panel panel-white">
                        <div class="panel-heading">
                            <h5 class="panel-title text-bold">Acquista crediti per l'inserimento pratiche</h5>
                        </div>                        
                        
                        <div class="panel-body">
                            
                            {% if insufficient %}
                                <div class="alert alert-danger alert-styled-left alert-arrow-left alert-component">                            
                                    <h6 class="alert-heading text-semibold">Gentile cliente,</h6>
                                    I tuoi <b>crediti non sono sufficienti</b> per poter inserire nuove pratiche. <br>
                                    Puoi procedere al pagamento in modo semplice e veloce direttamente in questa pagina con PayPal o carta di credito.<br> Dubbi o domande? Contattaci a <a href="<EMAIL>"><EMAIL></a><br>
                                    <b> Crediti disponibili: {{ user.credit | default(0) }}</b>
                                </div>
                            {% endif %}
                            
                            <div class="alert alert-warning alert-styled-left alert-arrow-left alert-component">                            
                                <h6 class="alert-heading text-semibold">Attenzione!</h6>
                                Il pagamento va effettuato con <b>Paypal o Carta di Credito Aziendale</b> per permetterci di risalire al pagamento.
                            </div>
                            
                            <legend class="text-bold">
                                <i class="icon-user"></i>
                                RIEPILOGO CLIENTE                                 
                                <a class="control-arrow" data-toggle="collapse" data-target="#customerRecap">
                                    <i class="icon-circle-down2"></i>
                                </a>
                            </legend>
                            <div class="collapse in" id="customerRecap">
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Crediti disponibili:</label>
                                    <div class="col-lg-9">
                                        <div class="form-control-static">{{ user.credit | default(0) }}</div>
                                    </div>
                                </div>
                                {% if user.profileType == 'privato' %}
                                    <div class="form-group">
                                        <label class="col-lg-3 control-label">Nome:</label>
                                        <div class="col-lg-9">
                                            <div class="form-control-static">{{ user.name }}</div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-lg-3 control-label">Cognome:</label>
                                        <div class="col-lg-9">
                                            <div class="form-control-static">{{ user.lastname }}</div>
                                        </div>
                                    </div>
                                {% else %}
                                    <div class="form-group">
                                        <label class="col-lg-3 control-label">Ragione sociale:</label>
                                        <div class="col-lg-9">
                                            <div class="form-control-static">{{ user.fullname }}</div>
                                        </div>
                                    </div>
                                {% endif %}
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Indirizzo fatturazione:</label>
                                    <div class="col-lg-9">
                                        <div class="form-control-static">{{ user.address }}, {{ user.city }} ({{ user.provinceCode }})</div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Telefono:</label>
                                    <div class="col-lg-9">
                                        <div class="form-control-static"><a href="tel:{{ user.phoneNumber }}">{{ user.phoneNumber }}</a></div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Email:</label>
                                    <div class="col-lg-9">
                                        <div class="form-control-static"><a href="mailto:{{ user.email }}">{{ user.email }}</a></div>
                                    </div>
                                </div>
                                {% if user.profileType == 'privato' %}
                                    <div class="form-group">
                                        <label class="col-lg-3 control-label">Cod. Fiscale:</label>
                                        <div class="col-lg-9">
                                            <div class="form-control-static">{{ user.tin }}</div>
                                        </div>
                                    </div>                                
                                {% else %}
                                    <div class="form-group">
                                        <label class="col-lg-3 control-label">P.IVA:</label>
                                        <div class="col-lg-9">
                                            <div class="form-control-static">{{ user.vatNumber }}</div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-lg-3 control-label">Codice univoco SDI:</label>
                                        <div class="col-lg-9">
                                            <div class="form-control-static">{{ user.sdiNumber }}</div>
                                        </div>
                                    </div>
                                {% endif %}
                                <div class="form-group">                                    
                                    <div class="col-lg-12">
                                        <a href="{{ paths('PROFILE') }}?backUrl={{ paths('BUY_CREDIT') }}" class="btn btn-success btn-xlg legitRipple heading-btn ">MODIFICA DATI</a>                                                                            
                                    </div>
                                </div>
                            </div>
                            <legend class="text-bold">
                                <i class="icon-list"></i>
                                CREDITI ACQUISTABILI
                                <a class="control-arrow" data-toggle="collapse" data-target="#orderRecap">
                                    <i class="icon-circle-down2"></i>
                                </a>
                            </legend>
                            <div class="collapse in" id="orderRecap">
                                <div class="alert alert-info alert-styled-left alert-arrow-left alert-component">
                                    <h6 class="alert-heading text-semibold">Crediti necessari per inserimento pratiche (1 credito = 1 euro IVA esclusa):</h6>                                    
                                    <button type="button" class="btn btn-info btn-xlg legitRipple heading-btn " data-toggle="modal" data-target="#calculator-modal"><i class="icon-calculator position-left"></i> APRI CALCOLATORE CREDITI</button>                                                                                                                
                                </div>                                
                            </div>                                                                                    
                            <legend class="text-bold"><i class="icon-podium"></i> SCEGLI TRA I NOSTRI PACCHETTI</legend>
                            
                            {#
                            - PACCHETTO BRONZE | 5.000 crediti = sconto 2%
                            - PACCHETTO SILVER | 10.000 crediti = sconto 5%
                            - PACCHETTO GOLD | 15.000 crediti =  sconto 8%
                            #}

                            <div class="row">
                                <div class="col-md-4">
                                    <div class="panel">
                                        <div class="panel-body">                                                    
                                            <div class="panel-body text-center">
                                                <h4 class="text-semibold no-margin"><a href="" class="text-default truncate">BRONZE</a></h4>
                                                <ul class="list-inline list-inline-separate mb-10">
                                                    <li><div class="label bg-bronze">sconto 2%</div></li>                                                        
                                                </ul>
                                                <h3 class="no-margin text-semibold"><span class="discount-price"> € 5.000,00</span> € 4.900,00</h3>
                                                <div class="text-muted">Crediti: 5.000</div>
                                                <a href="#" class="btn btn-block bg-success legitRipple pack-add mt-10" data-quantity="5000"><i class="icon-cart-add position-left"></i> SELEZIONA</a>                                            
                                            </div>                                                                                                                                                                    
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="panel">
                                        <div class="panel-body">                                                    
                                            <div class="panel-body text-center">
                                                <h4 class="text-semibold no-margin"><a href="" class="text-default truncate">SILVER</a></h4>
                                                <ul class="list-inline list-inline-separate mb-10">
                                                    <li><div class="label bg-silver">sconto 5%</div></li>
                                                </ul>
                                                <h3 class="no-margin text-semibold"><span class="discount-price"> € 10.000,00</span> € 9.500,00</h3>
                                                <div class="text-muted">Crediti: 10.000</div>
                                                <a href="#" class="btn btn-block bg-success legitRipple pack-add mt-10" data-quantity="10000"><i class="icon-cart-add position-left"></i> SELEZIONA</a>                                            
                                            </div>                                                                                                                                                                    
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="panel">
                                        <div class="panel-body">                                                    
                                            <div class="panel-body text-center">
                                                <h4 class="text-semibold no-margin"><a href="" class="text-default truncate">GOLD</a></h4>
                                                <ul class="list-inline list-inline-separate mb-10">
                                                    <li><div class="label bg-gold">sconto 8%</div></li>
                                                </ul>
                                                <h3 class="no-margin text-semibold"><span class="discount-price"> € 15.000,00</span> € 13.800,00</h3>
                                                <div class="text-muted">Crediti: 15.000</div>
                                                <a href="#" class="btn btn-block bg-success legitRipple pack-add mt-10" data-quantity="15000"><i class="icon-cart-add position-left"></i> SELEZIONA</a>
                                            </div>                                                                                                                                                                    
                                        </div>
                                    </div>
                                </div>
                            </div>

                            
                            <legend class="text-bold">oppure inserisci i crediti di cui hai bisogno:</legend>
                            
                            
                            {% set totalPrice = 0 %}

                            {% set totalPrice = totalPrice  %}
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Num. Crediti:</label>
                                <div class="col-lg-9">
                                    <input id="numCredit" type="number" name="numCredit" class="form-control calculate-total" placeholder="Es. 1000" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Totale:</label>
                                <div class="col-lg-9">
                                    <output id="priceVatNotIncluded" type="number" class="form-control"></output>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Totale IVA inclusa:</label>
                                <div class="col-lg-9">
                                    <output id="priceVatIncluded" type="number" class="form-control"></output>
                                </div>
                            </div>

                            <legend class="text-bold">Seleziona metodo di pagamento:</legend>
                            <div class="mb-6 mt-6">
                                {% if paymentList is not empty %}                                    
                                    {% for payment in paymentList %} 
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="payment" id="payment-{{ loop.index }}" value="{{ payment.code }}" {{ loop.first ? 'checked' : ''}}>
                                            <label class="form-check-label fw-bolder" for="payment-{{ loop.index }}">
                                                {{ payment.name }}
                                            </label>
                                        </div>
                                    {% endfor %}                                        
                                {% endif %}
                            </div>
                        </div>
                        <div class="panel-footer has-visible-elements">
                            <div class="heading-elements visible-elements">
                                <span class="heading-text text-semibold">Azioni:</span>
                                <div class="pull-right">
                                    <a href="#" class="btn bg-success btn-xlg heading-btn buy-confirm">Conferma <i class="icon-arrow-right14"></i></a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- /profile info -->
                </form>

            </div>

        </div>
        <!-- /user profile -->

    </div>
    <!-- END MAIN CONTENT -->

    <!-- Large modal -->
    <div id="calculator-modal" class="modal fade">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <button type="button" class="close" data-dismiss="modal">&times;</button>
                    <h5 class="modal-title text-bold">CALCOLATORE CREDITI PRATICHE</h5>
                </div>

                <div class="modal-body form-horizontal">                                        
                    <div class="form-group no-margin-bottom">
                        <label class="col-lg-3 control-label"><b>Enea bonus casa</b><br><small class="text-grey">(45 crediti l'una)</small></label>
                        <div class="col-lg-9">
                            <input type="number" name="enea_bonus_casa" class="form-control maxlength input-calculator" data-credit="45" maxlength="5" placeholder="Inserisci numero">
                        </div>
                    </div>
                    <div class="form-group no-margin-bottom">
                        <label class="col-lg-3 control-label"><b>Enea ecobonus casa</b><br><small class="text-grey">(75 crediti l'una)</small></label>
                        <div class="col-lg-9">
                            <input type="number" name="enea_ecobonus_casa" class="form-control maxlength input-calculator" data-credit="75" maxlength="5" placeholder="Inserisci numero">
                        </div>
                    </div>
                    <div class="form-group no-margin-bottom">
                        <label class="col-lg-3 control-label"><b>Conto termico pompa di calore</b><br><small class="text-grey">(80 crediti l'una)</small></label>
                        <div class="col-lg-9">
                            <input type="number" name="conto_termico_pompa_di_calore" class="form-control maxlength input-calculator" data-credit="80" maxlength="5" placeholder="Inserisci numero">
                        </div>
                    </div>
                    <div class="form-group no-margin-bottom">
                        <label class="col-lg-3 control-label"><b>Conto termico solare termico</b><br><small class="text-grey">(80 crediti l'una)</small></label>
                        <div class="col-lg-9">
                            <input type="number" name="conto_termico_solare_termico" class="form-control maxlength input-calculator" data-credit="80" maxlength="5" placeholder="Inserisci numero">
                        </div>
                    </div>
                    <div class="form-group no-margin-bottom">
                        <label class="col-lg-3 control-label"><b>Conto termico solare stufa</b><br><small class="text-grey">(90 crediti l'una)</small></label>
                        <div class="col-lg-9">
                            <input type="number" name="conto_termico_solare_stufa" class="form-control maxlength input-calculator" data-credit="90" maxlength="5" placeholder="Inserisci numero">
                        </div>
                    </div>
                    <div class="form-group no-margin-bottom">
                        <label class="col-lg-3 control-label"><b>Conto termico caldaia biomassa</b><br><small class="text-grey">(120 crediti l'una)</small></label>
                        <div class="col-lg-9">
                            <input type="number" name="conto_termico_caldaia_biomassa" class="form-control maxlength input-calculator" data-credit="120" maxlength="5" placeholder="Inserisci numero">
                        </div>
                    </div>
                    <div class="form-group no-margin-bottom">
                        <label class="col-lg-3 control-label"><b>Conto termico sistema ibrido</b><br><small class="text-grey">(80 crediti l'una)</small></label>
                        <div class="col-lg-9">
                            <input type="number" name="conto_termico_sistema_ibrido" class="form-control maxlength input-calculator" data-credit="80" maxlength="5" placeholder="Inserisci numero">
                        </div>
                    </div> 
                    <div class="form-group no-margin-bottom">
                        <label class="col-lg-3 control-label"><b>Conto termico scaldacqua pdc</b><br><small class="text-grey">(80 crediti l'una)</small></label>
                        <div class="col-lg-9">
                            <input type="number" name="conto_termico_scaldacqua_pdc" class="form-control maxlength input-calculator" data-credit="80" maxlength="5" placeholder="Inserisci numero">
                        </div>
                    </div> 
                    <div class="form-group no-margin-bottom">
                        <label class="col-lg-3 control-label"><b>Sconto in fattura bonus casa</b><br><small class="text-grey">(125 crediti l'una)</small></label>
                        <div class="col-lg-9">
                            <input type="number" name="sconto_in_fattura_bonus_casa" class="form-control maxlength input-calculator" data-credit="125" maxlength="5" placeholder="Inserisci numero">
                        </div>
                    </div> 
                    <div class="form-group no-margin-bottom">
                        <label class="col-lg-3 control-label"><b>Sconto in fattura ecobonus casa</b><br><small class="text-grey">(155 crediti l'una)</small></label>
                        <div class="col-lg-9">
                            <input type="number" name="sconto_in_fattura_ecobonus_casa" class="form-control maxlength input-calculator" data-credit="155" maxlength="5" placeholder="Inserisci numero">
                        </div>
                    </div> 
                    <div class="form-group no-margin-bottom">
                        <label class="col-lg-3 control-label"><b><i class="icon-rocket"></i> Modalità FAST</b><br><small class="text-grey">(30 crediti l'una)</small></label>
                        <div class="col-lg-9">
                            <input type="number" name="modalita_fast" class="form-control maxlength input-calculator" data-credit="30" maxlength="5" placeholder="Inserisci numero">
                        </div>
                    </div> 
                    <div class="form-group" id="sdiNumberForm">
                        <div class="col-lg-3"><h5>Totale</h5></div>
                        <div class="col-lg-9">
                            <h5 id="calculator-modal-total">0</h5>                            
                        </div>
                    </div> 
                </div>

                <div class="modal-footer text-center">
                    <button type="button" class="btn btn-cancel" data-dismiss="modal">Chiudi</button>                    
                </div>
            </div>
        </div>
    </div>
    <!-- /large modal -->
    
</div>
<!-- END PAGE CONTENT -->
<script>
    window.addEventListener("DOMContentLoaded", function() {
      var campi = document.getElementsByClassName("input-calculator");
      for (var i = 0; i < campi.length; i++) {
        campi[i].addEventListener("input", calcolaSomma);
      }
    });

    function calcolaSomma() {
      var somma = 0;
      var campi = document.getElementsByClassName("input-calculator");
      for (var i = 0; i < campi.length; i++) {
        if (campi[i].value !== "") {
          somma += parseInt(campi[i].value) * parseInt(campi[i].getAttribute('data-credit'));
        }
      }
      document.getElementById("calculator-modal-total").innerHTML = somma;
    }
  </script>
{% endblock %}
