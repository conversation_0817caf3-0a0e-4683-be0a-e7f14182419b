{% extends "be/include/base.html" %}

{% block extrahead %}
    <title>Utenti di sistema</title>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jasny_bootstrap.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/uniform.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/datatables.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/responsive.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/buttons.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/selects/select2.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/app.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/ui/ripple.min.js"></script>
    <script src="https://siteria.it/libs/pdfmake/0.1.34/pdfmake.min.js"></script>
    <script src="https://siteria.it/libs/pdfmake/0.1.34/vfs_fonts.js"></script>
    <script src="https://siteria.it/libs/b-lazy/1.8.2/blazy.min.js"></script>
    <script src="{{ contextPath }}/be/js/pages/system-users.js?{{ buildNumber }}"></script>
{% endblock %}

{% block content %}

<!-- Page content -->
<div class="page-content">

    <a id="usersSuccessUri" class="no-display" href="{{ paths('SYSTEM_USERS') }}/success" rel="nofollow"></a>    

    <!-- Main content -->
    <div class="content-wrapper">
        {% if success %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-success">
                    <a href="{{ paths('SYSTEM_USERS') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Dati memorizzati correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-danger">
                    <a href="{{ paths('SYSTEM_USERS') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- State saving -->
        <div class="panel panel-white">
            <div class="panel-heading">
                <h5 class="panel-title text-bold">Utenti di sistema</h5>
                <div class="heading-elements">
                    <div class="form-group">
                        <a href="{{ paths('SYSTEM_USERS_ADD') }}" class="btn bg-success legitRipple"><i class="icon-plus-circle2 position-left"></i>Aggiungi utente sistema</a>                        
                    </div>
                </div>
            </div>

            <table class="table datatable-users">
                <thead>
                    <tr>
                        <th>Email</th>
                        <th>Tipo</th>
                        <th>Registrato il</th>
                        {% if user.profileType == 'admin' %}    
                            <th class="text-center">Azioni</th>
                        {% endif %}
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    {% if userList is not empty %}
                        {% for userTmp in userList %}
                            {% if userTmp.profileType == 'admin' or userTmp.profileType == 'operatore' %}
                                <tr>                                
                                    <td>
                                        <!--<a href="{{ paths('SYSTEM_USER_EDIT') }}?userId={{ user.id }}" class="text-bold text-uppercase" target="_blank">-->
                                        {% if userTmp.lastname is empty %}
                                            {{ userTmp.email }}
                                        {% else %}
                                            {{ user.name }} {{ userTmp.lastname }}  {{ userTmp.fullname is empty ? '' : ('/ ' + userTmp.fullname) }}<br>
                                            <small>{{ userTmp.username }}</small>
                                        {% endif %}
                                        </a>
                                    </td>
                                    <td>
                                        {% if userTmp.profileType == 'admin' %}        
                                            <span class="label bg-info">{{ userTmp.profileType }}</span>
                                        {% elseif userTmp.profileType == 'operatore' %}        
                                            <span class="label bg-warning">{{ userTmp.profileType }}</span>
                                        {% endif %}     
                                    </td> 
                                    <td><span sortable-value="{{ userTmp.creation | date('yyyyMMddHHmm') }}">{{ userTmp.creation | date('dd/MM/yyyy HH:mm') }}</span></td>
                                    {% if user.profileType == 'admin' %}    
                                        <td class="text-center">
                                            <ul class="icons-list">
                                                <li class="dropdown">
                                                    <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                                        <i class="icon-menu9"></i>
                                                    </a>
                                                    <ul class="dropdown-menu dropdown-menu-right">                                                
                                                        {% if userTmp.profileType != 'admin' %}   
                                                            <li><a href="{{ paths('USER_REMOVE') }}?userId={{ userTmp.id }}" class="user-remove"><span class="status-mark bg-danger-300 position-left"></span> Elimina</a></li>
                                                        {% endif %}                                                
                                                    </ul>
                                                </li>
                                            </ul>
                                        </td>
                                    {% endif %}
                                    <td></td>
                                </tr>
                            {% endif %}
                        {% endfor %}
                    {% endif %}
                </tbody>
            </table>
        </div>
        <!-- /state saving -->

    </div>
    <!-- /main content -->
</div>
<!-- /page content -->
{% endblock %}
