{% extends "be/include/base.html" %}

{% block extrahead %}
{% if property.id is not empty %}
    <title>Visualizza pratica sconto in fattura</title>
{% else %}
    <title>Nuova pratica sconto in fattura</title>
{% endif %}

<!-- Theme Custom CSS files -->
<link href="https://siteria.it/libs/slim/4.2.1/slim/slim.min.css" rel="stylesheet">
<link href="https://siteria.it/libs/dropuploader/1.8.1/css/drop_uploader.min.css" rel="stylesheet">

<!-- Theme JS files -->
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/switchery.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/editable/editable.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jasny_bootstrap.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/uniform.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/autosize.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/formatter.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/typeahead/typeahead.bundle.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/typeahead/handlebars.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/maxlength.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/validation/validate.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/validation/localization/messages_it.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jquery_ui/interactions.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/selects/select2.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/wizards/steps.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/extensions/cookie.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/pickers/daterangepicker.js"></script>
<script src="https://siteria.it/libs/slim/4.2.1/slim/slim.kickstart.min.js"></script>
<script src="https://siteria.it/libs/jquery-mask/1.14.13/dist/jquery.mask.min.js"></script>
<script src="https://siteria.it/libs/sortable/1.7.0/Sortable.min.js"></script>
<script src="https://siteria.it/libs/autocomplete/1.0.4/auto-complete.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/app.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/ui/ripple.min.js"></script>
<script src="https://siteria.it/libs/dropuploader/1.8.1/js/drop_uploader.min.js"></script>
<script src="{{ contextPath }}/be/js/pages/invoicediscount-edit.js?{{ buildNumber }}"></script>
<!-- /theme JS files -->
{% endblock %}

{% block content %}

<a id="invoicediscountsSuccessUri" class="no-display" href="{{ paths('INVOICEDISCOUNTS') }}/success" rel="nofollow"></a>    
    
<!-- some hidden stuff -->
<a id="imageUri" class="no-display" href="{{ paths('IMAGE') }}?oid=" rel="nofollow"></a>

<!-- Page content -->
<div class="page-content">
    <!-- Main content -->
    <div class="content-wrapper">

        {% if success %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-success">
                    <a href="{{ paths('INVOICEDISCOUNT_EDIT') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Dati memorizzati correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-danger">
                    <a href="{{ paths('INVOICEDISCOUNT_EDIT') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Horizontal form options -->
        <div class="row">
            <div class="col-md-8 col-md-offset-2">
                {% set disabled = 'disabled' %}
                {% if user.profileType == 'admin' or user.profileType == 'operatore' %}    
                    {% set disabled = '' %}
                {% endif %}                <!-- Editable inputs -->
                <div class="panel panel-flat">
                    <div class="panel-heading">
                        <h5 class="panel-title">Pratica #{{ invoicediscount.protocol }}</h5>
                    </div>

                    <div class="table-responsive">
                        <form id="form-edit-invoicediscount" class="form-horizontal form-validate-jquery">
                            <table class="table table-lg">
                                <tr>
                                    <th colspan="2" class="active">Pratica assegnata a</th>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Utente</td>                                                                                                                                  
                                    <td>
                                        <select class="select" name="assignedUserId" id="assignedUserId" {{ disabled }}>
                                            <option value=""> - </option>
                                            {% for assignedUser in userList %}
                                                {{ invoicediscount.assignedUserId }}
                                                <option value="{{ assignedUser.id }}" {{ invoicediscount.assignedUserId == assignedUser.id ? 'selected' : '' }}> {{ assignedUser.name}} {{ assignedUser.lastname}}</option>
                                            {% endfor %}    
                                        </select>
                                    </td>                                
                                </tr>
                                <tr>
                                    <th colspan="2" class="active">Stato pratica</th>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Stato</td>   
                                    <td>                                    
                                        <select class="select" name="status" id="status"  {{ disabled }}>
                                            <option value="opened" {{ invoicediscount.status == 'opened' ? 'selected' : '' }}>Ricevuta</option>
                                            <option value="processing" {{ invoicediscount.status == 'processing' ? 'selected' : '' }}>In lavorazione</option>
                                            <option value="approved" {{ invoicediscount.status == 'approved' ? 'selected' : '' }}>Approvata</option>
                                            <option value="annulled" {{ invoicediscount.status == 'annulled' ? 'selected' : '' }}>Annullata</option>
                                        </select>                                    
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Note</td>   
                                    <td>                                    
                                        <textarea type="text" name="note" id="note" class="form-control maxlength" rows="5" placeholder="note...." {{ disabled }}>{{ invoicediscount.note }}</textarea>
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Documenti tecnici</td>                                                                                                                                   
                                    <td>
                                        {% set listName = 'adminFileIds' %}
                                        {% if invoicediscount.adminFileIds is not empty %}
                                            <div class="col-lg-9">
                                                <div class="panel panel-flat">
                                                    <div class="panel-body">                                                                
                                                        <ul class="media-list">
                                                            {% for fileId in invoicediscount.adminFileIds %}
                                                                <li class="media">
                                                                    <div class="media-left">
                                                                        <a href="#" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                                    </div>

                                                                    <div class="media-body">
                                                                        <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                            {{ fileinfo('file', fileId, 'originalFilename') }}
                                                                        </a>
                                                                        <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                                    </div>

                                                                    {% if user.profileType == 'admin' or user.profileType == 'operatore' %}   
                                                                        <div class="media-right media-middle">
                                                                            <ul class="icons-list">
                                                                                <li>
                                                                                    <button type="button" href="{{ paths('INVOICEDISCOUNT_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" listname="{{ listName }}" fileid="{{ fileId }}" invoicediscountId="{{ invoicediscount.id}}">Rimuovi</button>
                                                                                </li>
                                                                            </ul>
                                                                        </div>
                                                                    {% endif %}
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                            {% if user.profileType == 'admin' or user.profileType == 'operatore' %}    
                                                <div id="uploader-text" style="display: none;">Trascina qui il file</div>
                                                <div class="col-lg-9">
                                                    <div class="text-center">
                                                        <input type="file" name="adminFileIds" data-maxfilessize="4194304" attachment="true">
                                                    </div>
                                                </div>
                                            {% endif %}
                                        {% else %}
                                            {% if user.profileType == 'admin' or user.profileType == 'operatore' %}    
                                                <div id="uploader-text" style="display: none;">Trascina qui il file</div>
                                                <div class="col-lg-9">
                                                    <div class="text-center">
                                                        <input type="file" name="adminFileIds" data-maxfilessize="4194304" attachment="true">
                                                    </div>
                                                </div>
                                            {% endif %}
                                        {% endif %}
    <!--                                        <div id="uploader-text" style="display: none;">Trascina qui il file</div>
                                        <div class="text-center">
                                            <input type="file" name="adminFileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple {{ fileIds is empty ? 'required' : '' }}>
                                        </div>-->
                                    </td> 
                                </tr>
                            </table>
                            {% if user.profileType == 'admin' or user.profileType == 'operatore' %}   
                                <div class="panel-footer has-visible-elements">
                                    <div class="heading-elements visible-elements">
                                        <span class="heading-text text-semibold">Azioni:</span>
                                        <div class="pull-right">                                    
                                            <button type="button" class="invoicediscountSave btn bg-success-600" href="{{ paths('INVOICEDISCOUNT_EDIT_SAVE') }}?invoicediscountId={{ invoicediscount.id }}">Salva <i class="icon-arrow-right14"></i></button>
                                        </div>
                                    </div>
                                </div>
                            {% endif %}
                        </form>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-lg">

                            <tr>
                                <th colspan="2" class="active">Informazioni personali</th>
                            </tr>
                            <tr>
                                
                            <td style="width: 33%;">Soggetto</td>
                                {% if invoicediscount.profileType == 'privato' %}
                                    <td><span class="label bg-info">{{ invoicediscount.profileType }}</span></td>
                                {% elseif invoicediscount.profileType == 'azienda'  %}
                                    <td><span class="label bg-warning">{{ invoicediscount.profileType }}</span></td>
                                {% endif %}
                            </tr>
                            {% if invoicediscount.profileType == 'privato' %}  
                                <tr>
                                    <td style="width: 33%;">Nome</td>
                                    <td>{{ invoicediscount.name }}</td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Cognome</td>
                                    <td>{{ invoicediscount.lastname }}</td>
                                </tr>
                            {% endif %}
                            {% if invoicediscount.profileType == 'azienda' %}  
                                <tr>
                                    <td style="width: 33%;">Ragione sociale</td>
                                    {% set fullname = '*Nome* *Cognome*' %}
                                    {% if invoicediscount.fullname is not empty %}
                                        {% set fullname = invoicediscount.fullname %}
                                    {% elseif (invoicediscount.name is not empty) and (invoicediscount.lastname is not empty) %}
                                        {% set fullname = (invoicediscount.name | default('*Nome*')) + ' ' + invoicediscount.lastname | default('*Cognome*') %}
                                    {% endif %}
                                    <td>{{ fullname }}</td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">P.IVA</td>
                                    <td>{{ invoicediscount.vatNumber }}</td>
                                </tr>
                            {% endif %}
       
                            <tr>
                                <th colspan="2" class="active">Documenti caricati <a href="{{ paths('FILEZIP') + '?invoicediscountId=' + invoicediscount.id }}" download>Scarica tutti</a></th>
                            </tr>
                            {% if invoicediscount.identityDocumentFileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Carta d'identità:</td>
                                    <td>
                                        {% for fileId in invoicediscount.identityDocumentFileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if invoicediscount.invoiceFileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Fatture:</td>
                                    <td>
                                        {% for fileId in invoicediscount.invoiceFileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if invoicediscount.bankTransferFileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Bonifici:</td>
                                    <td>
                                        {% for fileId in invoicediscount.bankTransferFileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if invoicediscount.collectionFormFileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Modulo raccolta dati:</td>
                                    <td>
                                        {% for fileId in invoicediscount.collectionFormFileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if invoicediscount.revenueAgencyDelegationFileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Delega Agenzia Entrate:</td>
                                    <td>
                                        {% for fileId in invoicediscount.revenueAgencyDelegationFileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if invoicediscount.revenueAgencyFormFileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Modulo Agenzia Entrate:</td>
                                    <td>
                                        {% for fileId in invoicediscount.revenueAgencyFormFileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if invoicediscount.selfDeclarationFileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Autodichiarazione intervento:</td>
                                    <td>
                                        {% for fileId in invoicediscount.selfDeclarationFileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if invoicediscount.technicalDataFileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Scheda tecnica prodotti installati:</td>
                                    <td>
                                        {% for fileId in invoicediscount.technicalDataFileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            
                            <!-- ecc ecc -->
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /main content -->

</div>
<!-- /page content -->

<script>
    $('.btn-cancel').click(function () {
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-cancel-square icon-2x"></i><br/><br/> Sei sicuro?',
            content: "Le modifiche fatte fin'ora verranno perse.",
            buttons: {
                annulla: {
                    btnClass: 'btn-default',
                    action: function () {
                        //
                    }
                },
                conferma: {
                    btnClass: 'bg-success',
                    action: function () {
                        window.location.href = "{{ paths('INVOICEDISCOUNTS') }}?selectedStatuses=opened%7Cprocessing%7C";
                    }
                }
            }
        });
    });
</script>

{% endblock %}
