{% extends "be/include/base.html" %}

{% set currentPage = 'PAPERWORKS' %}

{% block extrahead %}
<title>Lista Documenti</title>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jasny_bootstrap.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/uniform.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/datatables.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/responsive.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/buttons.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/selects/select2.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/core/app.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/ui/ripple.min.js"></script>
<script src="https://www.siteria.it/libs/b-lazy/1.8.2/blazy.min.js"></script>
<script src="{{ contextPath }}/be/js/pages/paperworks-view.js?{{ buildNumber }}"></script>
{% endblock %}

{% block content %}

<!-- Page content -->
<div class="page-content">
    
    <!-- Main content -->
    <div class="content-wrapper">
        <div class="row">
            <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                {% if success %}
                <div class="row">
                    <div class="col-md-12">
                        <div class="alert alert-success">
                            <a href="{{ paths('PAPERWORKS_VIEW') }}" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                            <span>Informazini salvate correttamente</span>
                        </div>
                    </div>
                </div>
                {% elseif error %}
                <div class="row">
                    <div class="col-md-12">
                        <div class="alert alert-danger">
                            <a href="{{ paths('PAPERWORKS_VIEW') }}" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                            <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza.</a></span>
                        </div>
                    </div>
                </div>
                {% endif %}

                {% if paperworkList is not empty %}
                    {% for entry in paperworkList %}
                        <div class="panel">
                            <div class="panel-body">
                                <div class="col-md-10">
                                    <h3 class="no-margin text-bold">{{ entry.title }}</h3>
                                    <div class="text-muted text-semibold">{{ entry.subtitle }}</div>
                                </div>
                                <div class="col-md-2 mt-5 text-center">
                                    {% if entry.fileIds is not empty %}
                                        {% for fileId in entry.fileIds %}
                                            <a href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" download class="btn btn-primary legitRipple"><i class="icon-attachment"></i>DOWNLOAD</a>
                                        {% endfor %}                                        
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                {% endif %}
                <!-- /state saving -->

            </div>
        </div>
    </div>
    <!-- /main content -->
</div>
<!-- /page content -->
{% endblock %}