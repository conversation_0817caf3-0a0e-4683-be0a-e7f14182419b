{% extends "be/include/base.html" %}

{% set currentPage = 'POSTS' %}

{% block extrahead %}
{% if post.id is not empty %}
    <title>Modifica news</title>
{% else %}
    <title>Nuova news</title>
{% endif %}
<!-- Theme Custom CSS files -->
<link href="https://www.siteria.it/libs/slim/4.2.1/slim/slim.min.css" rel="stylesheet">
<!-- Theme JS files -->
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jasny_bootstrap.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/uniform.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/autosize.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/formatter.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/typeahead/typeahead.bundle.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/typeahead/handlebars.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/maxlength.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/validation/validate.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jquery_ui/interactions.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/selects/select2.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/editors/wysihtml5/wysihtml5.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/editors/wysihtml5/toolbar.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/editors/wysihtml5/parsers.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/editors/wysihtml5/locales/bootstrap-wysihtml5.ua-UA.js"></script>
<script src="https://www.siteria.it/libs/slim/4.2.1/slim/slim.kickstart.min.js"></script>
<script src="https://www.siteria.it/libs/sortable/1.7.0/Sortable.min.js"></script>
<script src="https://www.siteria.it/libs/autocomplete/1.0.4/auto-complete.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/core/app.js"></script>
<script src="{{ contextPath }}/be/js/pages/post-edit.js?{{ buildNumber }}"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/ui/ripple.min.js"></script>
<!-- /theme JS files -->
{% endblock %}

{% block content %}

<a id="imageUri" class="no-display" href="{{ paths('IMAGE') }}?oid=" rel="nofollow"></a>

<a id="postsUri" style="display: none" href="{{ paths('POSTS') }}"></a>
<a id="postsSuccessUri" style="display: none;" href="{{ paths('POSTS') }}/success" rel="nofollow"></a>
<a id="postRemoveUri" style="display: none;" href="{{ paths('POST_REMOVE') }}?postId={{ post.id }}" rel="nofollow"></a>

<div id="imageIds" style="display: none;">
    {% if post.id is not empty %}
        {% if post.imageIds is not empty %}
            {% for imageId in post.imageIds %}{{ imageId }}|{% endfor %}
        {%endif%}
    {%endif%}
</div>

<!-- Page content -->
<div class="page-content">
    <!-- Main content -->
    <div class="content-wrapper">

        {% if success %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-success">
                    <a href="{{ paths('POST_EDIT') }}" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                    <span>Informazini salvate correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-danger">
                    <a href="{{ paths('POST_EDIT') }}" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza.</a></span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Horizontal form options -->
        {% if post.id is not empty %}
            {% set saveUri = paths('POST_EDIT_SAVE') + '?postId=' + post.id %}
        {% else %}
            {% set saveUri = paths('POST_EDIT_SAVE') %}
        {% endif %}
        <div class="row">
            <div class="col-md-10 col-md-offset-1">
                <!-- Basic layout-->
                <form id="post-form" class="form-horizontal" method="post" action="{{ saveUri }}">
                    
                    <input type="hidden" name="gallery" value="{{ gallery }}">
                    
                    <div class="panel panel-white">
                        
                        <div class="panel-heading">
                            {% if post.id is not empty %}
                                <h5 class="panel-title text-bold">Modifica {{ post.title | upper }}</h5>
                            {% else %}
                                <h5 class="panel-title text-bold">Aggiungi news</h5>
                            {% endif %}
                        </div>
                        
                        <div class="panel-body">
                            <legend class="text-semibold">
                                <i class="icon-stack-picture"></i>
                                GALLERIA
                                <a class="control-arrow" data-toggle="collapse" data-target="#panel-gallery">
                                    <i class="icon-circle-down2"></i>
                                </a>
                            </legend>
                            <div class="collapse {{ gallery ? 'in' : '' }}" id="panel-gallery">
                                {% if gallery %}
                                    <div class="form-group no-margin-bottom">
                                        <div class="col-md-12">
                                            <div class="file-drop-area">
                                                <label for="files">Trascina qui le foto</label>
                                                <input name="uploaded-files" id="files" type="file" multiple>
                                            </div>
                                        </div>
                                    </div>                                    
                                    <br>
                                {% else %}
                                    <div class="form-group text-center mb-5">
                                        {% if post.id is not empty %}
                                            {% set postEditWithGalleryUri = paths('POST_EDIT') + '?postId=' + post.id + '&gallery=true' %}
                                        {% endif %}
                                        <a href="{{ postEditWithGalleryUri }}" class="btn bg-success legitRipple"><i class="icon-stack-picture"></i> Modifica galleria</a>
                                    </div>
                                    {% if post.imageIds is not empty %}
                                        <span class="help-block text-center">Questa news contiene delle foto. Clicca sul bottone per modificarle.</span>
                                    {% else %}                                
                                        <span class="help-block text-center">Questa news non contiene foto. Clicca sul bottone per aggiungerle.</span>
                                    {% endif %}                                
                                    <br>
                                {% endif %}                                
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Titolo:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="title" class="form-control maxlength" maxlength="50" placeholder="Titolo post" value="{{ post.title }}" required>
                                </div>
                            </div>                            
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Descrizione:</label>
                                <div class="col-lg-9">
                                    <div class="form-group">
                                        <textarea cols="18" rows="18" name="description" class="wysihtml5 wysihtml5-min form-control" placeholder="Aggiungi del testo...">
                                            {{ post.description }}
                                        </textarea>
                                    </div>                                    
                                </div>
                            </div>                            
                        </div>
                        <div class="panel-footer">
                            <div class="heading-elements">
                                <span class="heading-text text-semibold">Azioni:</span>
                                <div class="heading-btn pull-right">
                                    <button type="button" class="btn btn-default btn-cancel">Annulla</button>
                                    {% if post.id is not empty %}                                    
                                        <button id="btn-delete" type="button" class="btn bg-danger-600">Rimuovi</button>
                                    {% endif %}
                                    <button id="btn-save" type="submit" class="btn bg-success">Pubblica news<i class="icon-arrow-right14"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <!-- /basic layout -->
            </div>
        </div>
    </div>
    <!-- /main content -->

</div>
<!-- /page content -->

{% endblock %}
