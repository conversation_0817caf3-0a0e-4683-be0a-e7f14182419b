{% extends "be/include/base.html" %}

{% block extrahead %}
{% if property.id is not empty %}
    <title>Visualizza valutazione termica</title>
{% else %}
    <title>Nuova valutazione</title>
{% endif %}

<!-- Theme Custom CSS files -->
<link href="https://siteria.it/libs/slim/4.2.1/slim/slim.min.css" rel="stylesheet">
<link href="https://siteria.it/libs/dropuploader/1.8.1/css/drop_uploader.min.css" rel="stylesheet">

<!-- Theme JS files -->
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/switchery.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/editable/editable.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jasny_bootstrap.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/uniform.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/autosize.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/formatter.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/typeahead/typeahead.bundle.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/typeahead/handlebars.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/maxlength.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/validation/validate.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/validation/localization/messages_it.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jquery_ui/interactions.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/selects/select2.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/wizards/steps.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/extensions/cookie.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/pickers/daterangepicker.js"></script>
<script src="https://siteria.it/libs/slim/4.2.1/slim/slim.kickstart.min.js"></script>
<script src="https://siteria.it/libs/jquery-mask/1.14.13/dist/jquery.mask.min.js"></script>
<script src="https://siteria.it/libs/sortable/1.7.0/Sortable.min.js"></script>
<script src="https://siteria.it/libs/autocomplete/1.0.4/auto-complete.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/app.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/ui/ripple.min.js"></script>
<script src="https://siteria.it/libs/dropuploader/1.8.1/js/drop_uploader.min.js"></script>
<script src="{{ contextPath }}/be/js/pages/evaluations-add.js?{{ buildNumber }}"></script>
<!-- /theme JS files -->
{% endblock %}

{% block content %}

<!-- some hidden stuff -->
<a id="evaluationsUri" class="no-display" href="{{ paths('EVALUATIONS') }}?selectedStatuses=opened%7Cprocessing%7C" rel="nofollow"></a>
<a id="evaluationsAddInfoSaveUri" class="no-display" href="{{ paths('EVALUATIONS_ADD_INFO_SAVE') }}" rel="nofollow"></a>
<a id="evaluationsAddSaveUri" class="no-display" href="{{ paths('EVALUATIONS_ADD_SAVE') }}" rel="nofollow"></a>
<a id="imageUri" class="no-display" href="{{ paths('IMAGE') }}?oid=" rel="nofollow"></a>
<!-- api call for cities autocomplete -->
<a id="dataCitiesUri" style="display: none" href="{{ paths('DATA_CITIES') }}"></a>

<div id="imageIds" style="display: none;">
    {% if property.id is not empty %}
        {% if property.imageIds is not empty %}
            {% for imageId in property.imageIds %}{{ imageId }}|{% endfor %}
        {%endif%}
    {%endif%}
</div>

<!-- Page content -->
<div class="page-content">
    <!-- Main content -->
    <div class="content-wrapper">

        {% if user.profileType != 'admin' and user.active == false %}   
            <div class="row">
                <div class="col-md-12">
                    <div class="alert alert-danger">
                        <span>Attendi la conferma del tuo account prima di poter inserire una pratica</span>
                    </div>
                </div>
            </div>
        {% else %}
        
            {% if success %}
                <div class="row">
                    <div class="col-md-12">
                        <div class="alert alert-success">
                            <a href="{{ paths('EVALUATIONS_ADD') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                            <span>Dati memorizzati correttamente</span>
                        </div>
                    </div>
                </div>
            {% elseif error %}
                <div class="row">
                    <div class="col-md-12">
                        <div class="alert alert-danger">
                            <a href="{{ paths('EVALUATIONS_ADD') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                            <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Horizontal form options -->
            {% if property.id is not empty %}
                {% set saveUri = paths('EVALUATIONS_ADD_SAVE') + '?evaluationId=' + evaluation.id + '&status=' %}
            {% else %}
                {% set saveUri = paths('EVALUATIONS_ADD_SAVE')  + '?status='%}
            {% endif %}
            <div class="row">
                <div class="col-md-8 col-md-offset-2">
                    <!-- Wizard with validation -->
                    <div class="panel panel-white">
                        <div class="panel-heading">
                            <h6 class="panel-title text-bold">Inserisci la tua richiesta per la valutazione dello sconto termico!</h6>                        
                        </div>

                        <form id="form-evaluation" class="steps-validation" action="#" enctype="multipart/form-data">

                            <input type="hidden" id="step" value="{{ step }}"/>

                            <h6>Informazioni personali</h6>
                            <fieldset>
                                <hr>
                                <div class="row">
                                    {% if user.profileType == 'admin' or user.profileType == 'azienda' %}   
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="text-semibold">Il Soggetto Responsabile di questa pratica è:</label>
                                            <select class="select" name="profileType" id="profileType">
                                                <option value="privato" {{ evaluation.profileType == 'privato' ? 'selected' : '' }}>Privato</option>
                                                <option value="azienda" {{ evaluation.profileType == 'azienda' ? 'selected' : '' }}>Azienda</option>
                                            </select>
                                        </div>
                                    </div>
                                    {% else %}
                                        <input type="hidden" id="profileType" value="{{ evaluation.profileType }}"/>
                                    {% endif %}

                                    <div id="businessForm">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="text-semibold">Ragione sociale: <span class="text-danger">*</span></label>
                                                {% set fullname = '' %}
                                                {% if evaluation.fullname is not empty %}
                                                    {% set fullname = evaluation.fullname %}
                                                {% elseif (evaluation.name is not empty) and (evaluation.lastname is not empty) %}
                                                    {% set fullname = (evaluation.name | default('*Nome*')) + ' ' + evaluation.lastname | default('*Cognome*') %}
                                                {% endif %}
                                                <input type="text" name="fullname" class="form-control maxlength" maxlength="100" placeholder="Ragione sociale" required value="{{ fullname }}">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="text-semibold">P.IVA: <span class="text-danger">*</span></label>
                                                <input type="text" name="vatNumber" class="form-control maxlength" maxlength="11" placeholder="Partita IVA" required value="{{ evaluation.vatNumber }}">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6" id="nameForm">
                                        <div class="form-group">
                                            <label class="text-semibold">Nome: <span class="text-danger">*</span></label>
                                            <input type="text" name="name" class="form-control " placeholder="Nome" required value="{{ evaluation.name }}">
                                        </div>
                                    </div>
                                    <div class="col-md-6"  id="lastnameForm">
                                        <div class="form-group">
                                            <label class="text-semibold">Cognome: <span class="text-danger">*</span></label>
                                            <input type="text" name="lastname" class="form-control " placeholder="Cognome" required value="{{ evaluation.lastname }}">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6" id="tinForm">
                                        <div class="form-group">
                                            <label class="text-semibold">Paese installazione: <span class="text-danger">*</span></label>
                                            <input type="text" name="city" class="form-control maxlength " maxlength="16" placeholder="Paese installazione" required value="{{ evaluation.city }}">
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="text-semibold">Marca generatore: <span class="text-danger">*</span></label>
                                            <input type="text" name="brand" class="form-control maxlength " maxlength="100" placeholder="Marca generatore" required value="{{ evaluation.brand }}">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6" id="tinForm">
                                        <div class="form-group">
                                            <label class="text-semibold">Modello generatore: <span class="text-danger">*</span></label>
                                            <input type="text" name="model" class="form-control maxlength " maxlength="16" placeholder="Modello generatore" required value="{{ evaluation.model }}">
                                        </div>
                                    </div>

                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="text-semibold">Incasso installatore: <span class="text-danger">*</span></label>
                                            <input type="text" name="recessedInstaller" class="form-control maxlength " maxlength="100" placeholder="Incasso installatore" required value="{{ evaluation.recessedInstaller }}">
                                        </div>
                                    </div>
                                </div>
                            </fieldset>
                            
                            <h6>Caricamento documenti</h6>
                            <fieldset>
                                <hr>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="display-block text-semibold">Foto del locale con ripresa ad ampio raggio: <span class="text-danger">*</span></label>
                                            {% set fileIds = evaluation.localFileIds %}
                                            {% set listName = 'localFileIds' %}
                                            {% if fileIds is not empty %}
                                                {% for fileId in fileIds %}
                                                    <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('EVALUATIONS_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                {% endfor %}
                                            {% endif %}
                                            <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                            <div class="text-center">
                                                <input type="file" name="localFileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple {{ fileIds is empty ? 'required' : '' }}>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="display-block text-semibold">Foto da distante del generatore acceso: <span class="text-danger">*</span></label>
                                            {% set fileIds = evaluation.distanceGeneratorFileIds %}
                                            {% set listName = 'distanceGeneratorFileIds' %}
                                            {% if fileIds is not empty %}
                                                {% for fileId in fileIds %}
                                                    <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('EVALUATIONS_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                {% endfor %}
                                            {% endif %}
                                            <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                            <div class="text-center">
                                                <input type="file" name="distanceGeneratorFileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple {{ fileIds is empty ? 'required' : '' }}>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="display-block text-semibold">Foto da vicino del generatore acceso: <span class="text-danger">*</span></label>
                                            {% set fileIds = evaluation.nearGeneratorFileIds %}
                                            {% set listName = 'nearGeneratorFileIds' %}
                                            {% if fileIds is not empty %}
                                                {% for fileId in fileIds %}
                                                    <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('EVALUATIONS_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                {% endfor %}
                                            {% endif %}
                                            <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                            <div class="text-center">
                                                <input type="file" name="nearGeneratorFileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple {{ fileIds is empty ? 'required' : '' }}>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="display-block text-semibold">Foto di visione di tutta la canna fumaria: <span class="text-danger">*</span></label>
                                            {% set fileIds = evaluation.allFlueIds %}
                                            {% set listName = 'allFlueIds' %}
                                            {% if fileIds is not empty %}
                                                {% for fileId in fileIds %}
                                                    <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('EVALUATIONS_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                {% endfor %}
                                            {% endif %}
                                            <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                            <div class="text-center">
                                                <input type="file" name="allFlueIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple {{ fileIds is empty ? 'required' : '' }}>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="display-block text-semibold">Foto dei collegamenti della canna fumaria nello schienale del generatore: <span class="text-danger">*</span></label>
                                            {% set fileIds = evaluation.retroFlueFileIds %}
                                            {% set listName = 'retroFlueFileIds' %}
                                            {% if fileIds is not empty %}
                                                {% for fileId in fileIds %}
                                                    <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('EVALUATIONS_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                {% endfor %}
                                            {% endif %}
                                            <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                            <div class="text-center">
                                                <input type="file" name="retroFlueFileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple {{ fileIds is empty ? 'required' : '' }}>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="display-block text-semibold">Foto dei collegamenti della canna fumaria al muro: <span class="text-danger">*</span></label>
                                            {% set fileIds = evaluation.wallFlueFileIds %}
                                            {% set listName = 'wallFlueFileIds' %}
                                            {% if fileIds is not empty %}
                                                {% for fileId in fileIds %}
                                                    <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('EVALUATIONS_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                {% endfor %}
                                            {% endif %}
                                            <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                            <div class="text-center">
                                                <input type="file" name="wallFlueFileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple {{ fileIds is empty ? 'required' : '' }}>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="display-block text-semibold">Foto dei collegamenti idraulici:</label>
                                            {% set fileIds = evaluation.connectionsFlueFileIds %}
                                            {% set listName = 'connectionsFlueFileIds' %}
                                            {% if fileIds is not empty %}
                                                {% for fileId in fileIds %}
                                                    <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('EVALUATIONS_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                {% endfor %}
                                            {% endif %}
                                            <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                            <div class="text-center">
                                                <input type="file" name="connectionsFlueFileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="display-block text-semibold">Foto della targa del generatore:</label>
                                            {% set fileIds = evaluation.plateGeneratorFileIds %}
                                            {% set listName = 'plateGeneratorFileIds' %}
                                            {% if fileIds is not empty %}
                                                {% for fileId in fileIds %}
                                                    <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('EVALUATIONS_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                {% endfor %}
                                            {% endif %}
                                            <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                            <div class="text-center">
                                                <input type="file" name="plateGeneratorFileIds" data-maxfilesize="5242880" data-maxfilessize="15728640" attachment="true" multiple>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                            </fieldset>

                            <h6>Conferma</h6>
                            <fieldset>
                                <hr>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="checkbox">
                                                <label>
                                                    <input id="terms" name="terms" type="checkbox" class="styled" required {{ evaluation.terms ? 'checked' : '' }}>
                                                    Dichiaro di aver preso visione e di accettare le <a href="{{ contextPath }}/be/doc/condizioni-di-servizio.pdf" target="_blank" rel="noopener">condizioni di fornitura del servizio</a>
                                                </label>
                                            </div>
                                            <div class="checkbox">
                                                <label>
                                                    <input id="privacy" name="privacy" type="checkbox" class="styled" required {{ evaluation.privacy ? 'checked' : '' }}>
                                                    Dichiaro di aver preso visione e di accettare l'<a href="https://www.iubenda.com/privacy-policy/21639774" target="_blank" rel="noopener">informativa Privacy</a>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>
                        </form>
                    </div>
                    <!-- /wizard with validation -->
                </div>
            </div>
        {% endif %}

    </div>
    <!-- /main content -->

</div>
<!-- /page content -->

<script>
    $('.btn-cancel').click(function () {
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-cancel-square icon-2x"></i><br/><br/> Sei sicuro?',
            content: "Le modifiche fatte fin'ora verranno perse.",
            buttons: {
                annulla: {
                    btnClass: 'btn-default',
                    action: function () {
                        //
                    }
                },
                conferma: {
                    btnClass: 'bg-success',
                    action: function () {
                        window.location.href = "{{ paths('EVALUATIONS') }}?selectedStatuses=opened%7Cprocessing%7C";
                    }
                }
            }
        });
    });
</script>

{% endblock %}
