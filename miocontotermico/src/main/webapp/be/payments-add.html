{% extends "be/include/base.html" %}

{% block extrahead %}
    <title>Nuovo pagamento</title>

    <!-- CSS -->
    <link href="https://siteria.it/libs/slim/4.2.1/slim/slim.min.css" rel="stylesheet" type="text/css" media="all">

    <!-- SCRIPTS -->
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/styling/switchery.min.js"></script>
    <script src="https://siteria.it/libs/jquery-mask/1.14.13/dist/jquery.mask.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/inputs/maxlength.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/validate.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/localization/messages_it.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/selects/select2.min.js"></script>
    <script src="https://siteria.it/libs/slim/4.2.1/slim/slim.kickstart.min.js"></script>
    <script src="{{ contextPath }}/be/js/pages/payments-add.js?{{ buildNumber }}"></script>
{% endblock %}

{% block content %}

<!-- actions -->
<a id="paymentsAddAbortUri" style="display: none" href="{{ paths('PAYMENTS') }}" rel="nofollow"></a>

<!-- PAGE CONTENT -->
<div class="page-content">
    <!-- MAIN CONTENT -->
    <div class="content-wrapper">

        {% if success %}
            <div class="row">
                <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                    <div class="alert alert-success">
                        <a href="{{ paths('PAYMENTS_ADD') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                        <span>Dati memorizzati correttamente</span>
                    </div>
                </div>
            </div>
        {% elseif error %}
            <div class="row">
                <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                    <div class="alert alert-danger">
                        <a href="{{ paths('PAYMENTS_ADD') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                        <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                    </div>
                </div>
            </div>
        {% endif %}

        <!-- PAYMENTS ADD -->
        <div class="row">
            <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                <!-- Basic layout-->
                <form id="payment-form" class="form-horizontal" method="post" action="{{ paths('PAYMENTS_ADD_SAVE') }}">
                    <div class="panel panel-white">
                        <div class="panel-heading">
                            <h5 class="panel-title text-bold">Nuovo pagamento</h5>
                        </div>
                        <div class="panel-body">
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Logo:</label>
                                <div class="col-lg-9">
                                    <div class="row">
                                        <div class="col-xs-6 col-xs-offset-3 col-sm-4 col-sm-offset-4 col-md-4 col-md-offset-4">
                                            <div class="slim"
                                                data-max-file-size="5"
                                                data-save-initial-image="false"
                                                data-push="false"
                                                data-post="output"
                                                data-label="Carica un'immagine"
                                                data-label-loading=" "
                                                data-ratio="free"
                                                data-button-edit-label="Modifica"
                                                data-button-remove-label="Elimina"
                                                data-button-download-label="Scarica"
                                                data-button-upload-label="Carica"
                                                data-button-rotate-label="Ruota"
                                                data-button-cancel-label="Cancella"
                                                data-button-confirm-label="Conferma"
                                                data-status-file-size="Il file è troppo grande, il massimo consentito è $0 MB"
                                                data-status-file-type="Formato immagine non valido, formati consentiti: $0"
                                                data-status-no-support="Il tuo browser non supporta la modifica dell'immagine"
                                                data-status-image-too-small="Immagine troppo piccola, risoluzione minima: $0 pixel"
                                                data-status-content-length="Il server non supporta file così grandi"
                                                data-status-unknown-response="Errore sconosciuto, <NAME_EMAIL>"
                                                data-status-upload-success="Immagine salvata"
                                                data-size="400,400">
                                                <input type="file" name="uploaded-files" multiple="multiple" data-show-caption="false" data-show-remove="true">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Codice</label>
                                <div class="col-lg-9">
                                    <input type="text" name="code" class="form-control maxlength" maxlength="50" placeholder="Codice" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Nome:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="name" class="form-control maxlength" maxlength="50" placeholder="Nome" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Descrizione:</label>
                                <div class="col-lg-9">
                                    <textarea rows="5" name="description" class="form-control" placeholder="Inserisci delle note..."></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Tipo pagamento:</label>
                                <div class="col-lg-9">
                                    <div class="form-group">
                                        <select class="select" id="paymentType" name="paymentType">
                                            <option value="manual">Bonifico</option>
                                            <!--<option value="nexi">Nexi</option>-->
                                            <option value="nexi_intesa">Nexi (Intesa)</option>
                                            <option value="paypal">Paypal</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <input type="hidden" id="saveAndContinue" name="saveAndContinue" value="false">
                        </div>
                        <div class="panel-footer has-visible-elements">
                            <div class="heading-elements">
                                <span class="heading-text text-semibold">Azioni:</span>
                                <div class="pull-right">
                                    <button type="button" class="btn heading-btn btn-default btn-cancel">Annulla</button>
                                    <button type="submit" class="btn heading-btn bg-success" onclick="$('#saveAndContinue').val('false')">Inserisci <i class="icon-arrow-right14"></i></button>
                                    <!-- 2019-12-02 rimosso perché generava confusione tornare in insert dopo il salvataggio cliente trovarsi su una pagina vuota -->
                                    <!--<button type="submit" class="btn heading-btn bg-primary" onclick="$('#saveAndContinue').val('true')">Inserisci e continua <i class="icon-arrow-right14"></i></button>-->
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <!-- /basic layout -->
            </div>
        </div>
    </div>
    <!-- END MAIN CONTENT -->

</div>
<!-- END PAGE CONTENT -->
{% endblock %}
