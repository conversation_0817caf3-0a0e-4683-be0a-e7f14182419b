{% extends "be/include/base.html" %}

{% block extrahead %}
    <title>Pagamenti</title>

    <!-- SCRIPTS -->
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/datatables.min.js"></script>
    <script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/jszip/jszip.min.js"></script>
    <script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/pdfmake/pdfmake.min.js"></script>
    <script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/pdfmake/vfs_fonts.min.js"></script>
    <script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/buttons.min.js"></script>    
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/extensions/responsive.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/selects/select2.min.js"></script>
    <script src="{{ contextPath }}/be/js/pages/payments.js?{{ buildNumber }}"></script>
{% endblock %}

{% block content %}

<!-- PAGE CONTENT -->
<div class="page-content">

    <!-- LEFT SIDEBAR -->
    <div class="sidebar sidebar-main sidebar-default sidebar-separate">
    </div>
    <!-- END LEFT SIDEBAR -->

    <!-- MAIN CONTENT -->
    <div class="content-wrapper">

        {% if success %}
            <div class="row">
                <div class="col-md-12">
                    <div class="alert alert-success">
                        <a href="{{ paths('PAYMENTS') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                        <span>Dati memorizzati correttamente</span>
                    </div>
                </div>
            </div>
        {% elseif error %}
            <div class="row">
                <div class="col-md-12">
                    <div class="alert alert-danger">
                        <a href="{{ paths('PAYMENTS') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                        <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                    </div>
                </div>
            </div>
        {% endif %}

        <!-- PAYMENTS -->
        <div class="panel panel-white">
            <div class="panel-heading has-visible-elements">
                <h5 class="panel-title text-bold">Pagamenti</h5>
                {% if (user.profileType == 'system') or (user.profileType == 'admin') %}
                    <div class="heading-elements visible-elements">                        
                        <a href="{{ paths('PAYMENTS_ADD') }}" class="btn heading bg-success legitRipple"><i class="icon-plus-circle2 position-left"></i>NUOVO PAGAMENTO</a>                        
                    </div>
                {% endif %}
            </div>

            <div id="productsTable">
                <table class="table datatable-responsive-control-right">
                    <thead>
                        <tr>
                            <th>Nome</th>
                            <th>Codice</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody>
                        {% if paymentList is not empty %}
                            {% for entry in paymentList %}
                                <tr>
                                    <td>
                                        <a href="{{ paths('PAYMENT_VIEW') }}?paymentId={{ entry.id }}" class="text-bold">{{ entry.name }}</a>
                                    </td>
                                    <td>
                                        {{ entry.code }}
                                    </td>
                                    <td></td>
                                </tr>
                            {% endfor %}
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>
        <!-- /control position -->

    </div>
    <!-- END MAIN CONTENT -->

</div>
<!-- END PAGE CONTENT -->

{% endblock %}
