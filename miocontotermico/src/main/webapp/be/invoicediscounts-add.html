{% extends "be/include/base.html" %}

{% block extrahead %}
{% if property.id is not empty %}
    <title>Visualizza pratica sconto in fattura</title>
{% else %}
    <title>Nuova pratica sconto in fatturas</title>
{% endif %}

<!-- Theme Custom CSS files -->
<link href="https://siteria.it/libs/slim/4.2.1/slim/slim.min.css" rel="stylesheet">
<link href="https://siteria.it/libs/dropuploader/1.8.1/css/drop_uploader.min.css" rel="stylesheet">

<!-- Theme JS files -->
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/switchery.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/editable/editable.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jasny_bootstrap.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/uniform.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/autosize.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/formatter.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/typeahead/typeahead.bundle.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/typeahead/handlebars.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/maxlength.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/validation/validate.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/validation/localization/messages_it.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jquery_ui/interactions.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/selects/select2.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/wizards/steps.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/extensions/cookie.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/pickers/daterangepicker.js"></script>
<script src="https://siteria.it/libs/slim/4.2.1/slim/slim.kickstart.min.js"></script>
<script src="https://siteria.it/libs/jquery-mask/1.14.13/dist/jquery.mask.min.js"></script>
<script src="https://siteria.it/libs/sortable/1.7.0/Sortable.min.js"></script>
<script src="https://siteria.it/libs/autocomplete/1.0.4/auto-complete.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/app.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/ui/ripple.min.js"></script>
<script src="https://siteria.it/libs/dropuploader/1.8.1/js/drop_uploader.min.js"></script>
<script src="{{ contextPath }}/be/js/pages/invoicediscounts-add.js?{{ buildNumber }}"></script>
<!-- /theme JS files -->
{% endblock %}

{% block content %}

<!-- some hidden stuff -->
<a id="invoicediscountsUri" class="no-display" href="{{ paths('INVOICEDISCOUNTS') }}?selectedStatuses=opened%7Cprocessing%7C" rel="nofollow"></a>
<a id="invoicediscountsAddInfoSaveUri" class="no-display" href="{{ paths('INVOICEDISCOUNTS_ADD_INFO_SAVE') }}" rel="nofollow"></a>
<a id="invoicediscountsAddSaveUri" class="no-display" href="{{ paths('INVOICEDISCOUNTS_ADD_SAVE') }}" rel="nofollow"></a>
<a id="imageUri" class="no-display" href="{{ paths('IMAGE') }}?oid=" rel="nofollow"></a>
<!-- api call for cities autocomplete -->
<a id="dataCitiesUri" style="display: none" href="{{ paths('DATA_CITIES') }}"></a>

<div id="imageIds" style="display: none;">
    {% if property.id is not empty %}
        {% if property.imageIds is not empty %}
            {% for imageId in property.imageIds %}{{ imageId }}|{% endfor %}
        {%endif%}
    {%endif%}
</div>

<!-- Page content -->
<div class="page-content">
    <!-- Main content -->
    <div class="content-wrapper">

        {% if user.profileType != 'admin' and user.active == false %}   
            <div class="row">
                <div class="col-md-12">
                    <div class="alert alert-danger">
                        <span>Attendi la conferma del tuo account prima di poter inserire una pratica</span>
                    </div>
                </div>
            </div>
        {% else %}
        
            {% if success %}
                <div class="row">
                    <div class="col-md-12">
                        <div class="alert alert-success">
                            <a href="{{ paths('INVOICEDISCOUNTS_ADD') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                            <span>Dati memorizzati correttamente</span>
                        </div>
                    </div>
                </div>
            {% elseif error %}
                <div class="row">
                    <div class="col-md-12">
                        <div class="alert alert-danger">
                            <a href="{{ paths('INVOICEDISCOUNTS_ADD') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                            <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Horizontal form options -->
            {% if property.id is not empty %}
                {% set saveUri = paths('INVOICEDISCOUNTS_ADD_SAVE') + '?invoicediscountId=' + invoicediscount.id + '&status=' %}
            {% else %}
                {% set saveUri = paths('INVOICEDISCOUNTS_ADD_SAVE')  + '?status='%}
            {% endif %}
            <div class="row">
                <div class="col-md-8 col-md-offset-2">
                    <!-- Wizard with validation -->
                    <div class="panel panel-white">
                        <div class="panel-heading">
                            <h6 class="panel-title text-bold">Inserisci la tua richiesta per lo SCONTO IN FATTURA!</h6>                        
                        </div>

                        <form id="form-invoicediscount" class="steps-validation" action="#" enctype="multipart/form-data">

                            <input type="hidden" id="step" value="{{ step }}"/>

                            <h6>Informazioni personali</h6>
                            <fieldset>
                                <hr>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="text-semibold">Seleziona la tipologia di pratica: <span class="text-danger">*</span></label>
                                            <select class="select" name="service" id="service" required>
                                                <option value="">-</option>
                                                {% if user.credit < 125 %}
                                                    {% set disabled = 'disabled' %}
                                                    {% set textDisabled = 'crediti non sufficienti' %}
                                                {% else %}
                                                    {% set disabled = '' %}
                                                    {% set textDisabled = '' %}
                                                {% endif %}
                                                <option {{ disabled }} value="sconto_in_fattura_bonus_casa" {{ invoicediscount.service == 'sconto_in_fattura_bonus_casa' ? 'selected' : '' }}>Bonus Casa (125 crediti) {{ textDisabled }}</option>
                                                {% if user.credit < 155 %}
                                                    {% set disabled = 'disabled' %}
                                                    {% set textDisabled = 'crediti non sufficienti' %}
                                                {% else %}
                                                    {% set disabled = '' %}
                                                    {% set textDisabled = '' %}
                                                {% endif %}
                                                <option {{ disabled }} value="sconto_in_fattura_ecobonus_casa" {{ invoicediscount.service == 'sconto_in_fattura_ecobonus_casa' ? 'selected' : '' }}>Ecobonus Casa (155 crediti) {{ textDisabled }}</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="row">
                                    {% if user.profileType == 'admin' or user.profileType == 'azienda' %}   
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="text-semibold">Il Soggetto Responsabile di questa pratica è:</label>
                                            <select class="select" name="profileType" id="profileType">
                                                <option value="privato" {{ invoicediscount.profileType == 'privato' ? 'selected' : '' }}>Privato</option>
                                                <option value="azienda" {{ invoicediscount.profileType == 'azienda' ? 'selected' : '' }}>Azienda</option>
                                            </select>
                                        </div>
                                    </div>
                                    {% else %}
                                        <input type="hidden" id="profileType" value="{{ invoicediscount.profileType }}"/>
                                    {% endif %}

                                    <div id="businessForm">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="text-semibold">Ragione sociale: <span class="text-danger">*</span></label>
                                                {% set fullname = '' %}
                                                {% if invoicediscount.fullname is not empty %}
                                                    {% set fullname = invoicediscount.fullname %}
                                                {% elseif (invoicediscount.name is not empty) and (invoicediscount.lastname is not empty) %}
                                                    {% set fullname = (invoicediscount.name | default('*Nome*')) + ' ' + invoicediscount.lastname | default('*Cognome*') %}
                                                {% endif %}
                                                <input type="text" name="fullname" class="form-control maxlength" maxlength="100" placeholder="Ragione sociale" required value="{{ fullname }}">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label class="text-semibold">P.IVA: <span class="text-danger">*</span></label>
                                                <input type="text" name="vatNumber" class="form-control maxlength" maxlength="11" placeholder="Partita IVA" required value="{{ invoicediscount.vatNumber }}">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-6" id="nameForm">
                                        <div class="form-group">
                                            <label class="text-semibold">Nome: <span class="text-danger">*</span></label>
                                            <input type="text" name="name" class="form-control " placeholder="Nome" required value="{{ invoicediscount.name }}">
                                        </div>
                                    </div>
                                    <div class="col-md-6"  id="lastnameForm">
                                        <div class="form-group">
                                            <label class="text-semibold">Cognome: <span class="text-danger">*</span></label>
                                            <input type="text" name="lastname" class="form-control " placeholder="Cognome" required value="{{ invoicediscount.lastname }}">
                                        </div>
                                    </div>
                                </div>
                            </fieldset>
                            
                            <h6>Caricamento documenti</h6>
                            <fieldset>
                                <hr>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="display-block text-semibold">Carta d'identità: <span class="text-danger">*</span></label>
                                            {% set fileIds = invoicediscount.identityDocumentFileIds %}
                                            {% set listName = 'identityDocumentFileIds' %}
                                            {% if fileIds is not empty %}
                                                {% for fileId in fileIds %}
                                                    <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('ENEAPROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                {% endfor %}
                                            {% endif %}
                                            <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                            <div class="text-center">
                                                <input type="file" name="identityDocumentFileIds" data-maxfilesize="5242880" data-maxfilessize="********" attachment="true" multiple {{ fileIds is empty ? 'required' : '' }}>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="display-block text-semibold">Fatture: <span class="text-danger">*</span></label>
                                            {% set fileIds = invoicediscount.invoiceFileIds %}
                                            {% set listName = 'invoiceFileIds' %}
                                            {% if fileIds is not empty %}
                                                {% for fileId in fileIds %}
                                                    <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('ENEAPROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                {% endfor %}
                                            {% endif %}
                                            <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                            <div class="text-center">
                                                <input type="file" name="invoiceFileIds" data-maxfilesize="5242880" data-maxfilessize="********" attachment="true" multiple {{ fileIds is empty ? 'required' : '' }}>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="display-block text-semibold">Bonifici: <span class="text-danger">*</span></label>
                                            {% set fileIds = invoicediscount.bankTransferFileIds %}
                                            {% set listName = 'bankTransferFileIds' %}
                                            {% if fileIds is not empty %}
                                                {% for fileId in fileIds %}
                                                    <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('ENEAPROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                {% endfor %}
                                            {% endif %}
                                            <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                            <div class="text-center">
                                                <input type="file" name="bankTransferFileIds" data-maxfilesize="5242880" data-maxfilessize="********" attachment="true" multiple {{ fileIds is empty ? 'required' : '' }}>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="display-block text-semibold">Modulo raccolta dati: <span class="text-danger">*</span></label>
                                            {% set fileIds = invoicediscount.collectionFormFileIds %}
                                            {% set listName = 'collectionFormFileIds' %}
                                            {% if fileIds is not empty %}
                                                {% for fileId in fileIds %}
                                                    <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('ENEAPROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                {% endfor %}
                                            {% endif %}
                                            <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                            <div class="text-center">
                                                <input type="file" name="collectionFormFileIds" data-maxfilesize="5242880" data-maxfilessize="********" attachment="true" multiple {{ fileIds is empty ? 'required' : '' }}>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="display-block text-semibold">Delega Agenzia Entrate: <span class="text-danger">*</span></label>
                                            {% set fileIds = invoicediscount.revenueAgencyDelegationFileIds %}
                                            {% set listName = 'revenueAgencyDelegationFileIds' %}
                                            {% if fileIds is not empty %}
                                                {% for fileId in fileIds %}
                                                    <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('ENEAPROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                {% endfor %}
                                            {% endif %}
                                            <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                            <div class="text-center">
                                                <input type="file" name="revenueAgencyDelegationFileIds" data-maxfilesize="5242880" data-maxfilessize="********" attachment="true" multiple {{ fileIds is empty ? 'required' : '' }}>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="display-block text-semibold">Modulo Agenzia Entrate: <span class="text-danger">*</span></label>
                                            {% set fileIds = invoicediscount.revenueAgencyFormFileIds %}
                                            {% set listName = 'revenueAgencyFormFileIds' %}
                                            {% if fileIds is not empty %}
                                                {% for fileId in fileIds %}
                                                    <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('ENEAPROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                {% endfor %}
                                            {% endif %}
                                            <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                            <div class="text-center">
                                                <input type="file" name="revenueAgencyFormFileIds" data-maxfilesize="5242880" data-maxfilessize="********" attachment="true" multiple {{ fileIds is empty ? 'required' : '' }}>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="display-block text-semibold">Autodichiarazione intervento: <span class="text-danger">*</span></label>
                                            {% set fileIds = invoicediscount.selfDeclarationFileIds %}
                                            {% set listName = 'selfDeclarationFileIds' %}
                                            {% if fileIds is not empty %}
                                                {% for fileId in fileIds %}
                                                    <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('ENEAPROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                {% endfor %}
                                            {% endif %}
                                            <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                            <div class="text-center">
                                                <input type="file" name="selfDeclarationFileIds" data-maxfilesize="5242880" data-maxfilessize="********" attachment="true" multiple {{ fileIds is empty ? 'required' : '' }}>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label class="display-block text-semibold">Scheda tecnica prodotti installati:</label>
                                            {% set fileIds = invoicediscount.technicalDataFileIds %}
                                            {% set listName = 'technicalDataFileIds' %}
                                            {% if fileIds is not empty %}
                                                {% for fileId in fileIds %}
                                                    <span>{{ fileinfo('file', fileId, 'originalFilename') }}</span><a href="{{ paths('ENEAPROCEDURES_ADD_FILEID_REMOVE') }}" listname="{{ listName }}" fileid="{{ fileId }}" class="delete-fileid">   elimina</a></br>
                                                {% endfor %}
                                            {% endif %}
                                            <div id="uploader-text" style="display: none;">Trascina qui i files</div>
                                            <div class="text-center">
                                                <input type="file" name="technicalDataFileIds" data-maxfilesize="5242880" data-maxfilessize="********" attachment="true" multiple>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>

                            <h6>Conferma</h6>
                            <fieldset>
                                <hr>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <div class="checkbox">
                                                <label>
                                                    <input id="terms" name="terms" type="checkbox" class="styled" required {{ invoicediscount.terms ? 'checked' : '' }}>
                                                    Dichiaro di aver preso visione e di accettare le <a href="{{ contextPath }}/be/doc/condizioni-di-servizio.pdf" target="_blank" rel="noopener">condizioni di fornitura del servizio</a>
                                                </label>
                                            </div>
                                            <div class="checkbox">
                                                <label>
                                                    <input id="privacy" name="privacy" type="checkbox" class="styled" required {{ invoicediscount.privacy ? 'checked' : '' }}>
                                                    Dichiaro di aver preso visione e di accettare l'<a href="https://www.iubenda.com/privacy-policy/21639774" target="_blank" rel="noopener">informativa Privacy</a>
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </fieldset>
                        </form>
                    </div>
                    <!-- /wizard with validation -->
                </div>
            </div>
        {% endif %}

    </div>
    <!-- /main content -->

</div>
<!-- /page content -->

<script>
    $('.btn-cancel').click(function () {
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-cancel-square icon-2x"></i><br/><br/> Sei sicuro?',
            content: "Le modifiche fatte fin'ora verranno perse.",
            buttons: {
                annulla: {
                    btnClass: 'btn-default',
                    action: function () {
                        //
                    }
                },
                conferma: {
                    btnClass: 'bg-success',
                    action: function () {
                        window.location.href = "{{ paths('INVOICEDISCOUNTS') }}?selectedStatuses=opened%7Cprocessing%7C";
                    }
                }
            }
        });
    });
</script>

{% endblock %}
