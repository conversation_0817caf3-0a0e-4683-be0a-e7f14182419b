{% extends "be/include/base.html" %}

{% block extrahead %}
    <title>Nuova piattaforma di pagamento</title>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/styling/switchery.min.js"></script>
    <script src="https://siteria.it/libs/jquery-mask/1.14.13/dist/jquery.mask.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/inputs/maxlength.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/validate.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/localization/messages_it.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/selects/select2.min.js"></script>
    <script src="https://siteria.it/libs/slim/4.2.1/slim/slim.kickstart.min.js"></script>
    <script src="{{ contextPath }}/be/js/pages/payment-platforms-add.js?{{ buildNumber }}"></script>
{% endblock %}

{% block content %}
    <!-- actions -->
    <a id="paymentPlatformAddAbortUri" style="display: none" href="{{ paths('PAYMENT_PLATFORMS') }}" rel="nofollow"></a>
    <!-- Page content -->
    <div class="page-content">

        <!-- Main content -->
        <div class="content-wrapper">
            <div class="row">
                <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                    {% if success %}
                        <div class="row">
                            <div class="col-md-12">
                                <div class="alert alert-success">
                                    <a href="{{ paths('PAYMENT_PLATFORMS_ADD') }}" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                                    <span>Informazioni salvate correttamente</span>
                                </div>
                            </div>
                        </div>
                    {% elseif error %}
                        <div class="row">
                            <div class="col-md-12">
                                <div class="alert alert-danger">
                                    <a href="{{ paths('PAYMENT_PLATFORMS_ADD') }}" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                                    <span>Errore durante il salvataggio dei dati. Hai bisogno di aiuto? <a href="mailto:<EMAIL>">Contatta il servizio clienti</a></span>
                                </div>
                            </div>
                        </div>
                    {% endif %}

                    <!-- payment platform info -->
                    <form class="form-horizontal form-validate-jquery" method="post" action="{{ paths('PAYMENT_PLATFORMS_ADD_SAVE') }}">
                        <div class="panel panel-white">
                            <div class="panel-heading">
                                <h5 class="panel-title text-bold">Nuova piattaforma di pagamento</h5>                                        
                            </div>
                            <div class="panel-body">
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Tipo:</label>
                                    <div class="col-lg-9">
                                        <div class="form-group">
                                            <select class="select" id="paymentType" name="paymentType" required>
                                                <option value="manual">-</option>
                                                <!--<option value="nexi">Nexi</option>-->
                                                <option value="nexi_intesa">Nexi (Intesa)</option>
                                                <!--<option value="nexi_triveneto">Nexi (Triveneto)</option>-->
                                                <option value="paypal">Paypal</option>
                                                <option value="stripe">Stripe</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Ambiente:</label>
                                    <div class="col-lg-9">
                                        <div class="form-group">
                                            <select class="select" id="environmentType" name="environmentType">
                                                <option value="">-</option>
                                                <option value="sandbox">Prova</option>
                                                <option value="live">Produzione</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Codice cliente:</label>
                                    <div class="col-lg-9">
                                        <input type="text" name="alias" class="form-control" placeholder="Codice identificativo del profilo esercente" required>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Chiave segreta:</label>
                                    <div class="col-lg-9">
                                        <input type="text" name="secretKey" class="form-control" placeholder="Chiave segreta o apikey" required>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Endpoint:</label>
                                    <div class="col-lg-9">
                                        <input type="text" name="endpoint" class="form-control" placeholder="Endpoint del servizio">
                                    </div>
                                </div>
                            </div>
                            <div class="panel-footer has-visible-elements">
                                <div class="heading-elements visible-elements">
                                    <span class="heading-text text-semibold">Azioni:</span>
                                    <div class="pull-right">
                                        <button type="button" class="btn heading-btn btn-default btn-cancel">Annulla</button>
                                        <button type="submit" class="btn heading-btn bg-success" onclick="$('#saveAndContinue').val('false')">Inserisci <i class="icon-arrow-right14"></i></button>
                                        <!-- 2019-12-02 rimosso perché generava confusione tornare in insert dopo il salvataggio cliente trovarsi su una pagina vuota -->
                                        <!--<button type="submit" class="btn heading-btn bg-primary" onclick="$('#saveAndContinue').val('true')">Inserisci e continua <i class="icon-arrow-right14"></i></button>-->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

            </div>

        </div>
        <!-- /main content -->

    </div>
    <!-- /page content -->
{% endblock %}
