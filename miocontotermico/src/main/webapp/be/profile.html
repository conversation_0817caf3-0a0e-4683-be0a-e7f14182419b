{% extends "be/include/base.html" %}

{% block extrahead %}
    <title>Profilo</title>
    <link href="https://siteria.it/libs/slim/4.2.1/slim/slim.min.css" rel="stylesheet">
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/switchery.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jasny_bootstrap.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/uniform.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/autosize.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/formatter.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/typeahead/typeahead.bundle.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/typeahead/handlebars.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/maxlength.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/validation/validate.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/validation/localization/messages_it.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jquery_ui/interactions.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/selects/select2.min.js"></script>
<script src="https://siteria.it/libs/slim/4.2.1/slim/slim.kickstart.min.js"></script>
<script src="https://siteria.it/libs/jquery-mask/1.14.13/dist/jquery.mask.min.js"></script>
<script src="https://siteria.it/libs/sortable/1.7.0/Sortable.min.js"></script>
<script src="https://siteria.it/libs/autocomplete/1.0.4/auto-complete.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/app.js"></script>
<script src="{{ contextPath }}/be/js/pages/profile.js?{{ buildNumber }}"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/ui/ripple.min.js"></script>
{% endblock %}

{% block content %}
    <!-- Page content -->
    <div class="page-content">

        <!-- Main content -->
        <div class="content-wrapper">

            <!-- User profile -->
            <div class="row">
                <div class="col-md-8 col-md-offset-2">
                    {% if success %}
                    <div class="row">
                        <div class="col-md-12">
                            <div class="alert alert-success">
                                <a href="{{ paths('PROFILE') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                                <span>Dati memorizzati correttamente</span>
                            </div>
                        </div>
                    </div>
                    {% elseif error %}
                    <div class="row">
                        <div class="col-md-12">
                            <div class="alert alert-danger">
                                <a href="{{ paths('PROFILE') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                                <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Profile info -->
                    <form id="form-edit-profile" class="form-horizontal form-validate-jquery" method="post" action="{{ paths('PROFILE_SAVE') }}?oid={{ user.id }}&backUrl={{ backUrl }}">
                        <div class="panel panel-white">
                            <div class="panel-heading">
                                <h5 class="panel-title text-bold">Informazioni</h5>                                        
                            </div>

                            <div class="panel-body">
                                <div class="form-group">
                                    <div class="form-group">
                                        <label class="col-lg-3 control-label">Crediti disponibili:</label>
                                        <div class="col-lg-9">
                                            <div class="form-control-static">{{ user.credit }}</div>
                                        </div>
                                    </div>
                                    <label class="col-lg-3 control-label">Foto profilo:</label>
                                    <div class="col-lg-9">
                                        <div class="row">
                                            <div class="col-xs-6 col-xs-offset-3 col-sm-4 col-sm-offset-4 col-md-4 col-md-offset-4">
                                                <div class="slim"
                                                     data-max-file-size="5"
                                                     data-save-initial-image="{{ user.imageId is not empty ? 'true' : 'false'}}"
                                                     data-push="false"
                                                     data-post="output"
                                                     data-label="Carica un'immagine"
                                                     data-label-loading=" "
                                                     data-ratio="1:1"
                                                     data-button-edit-label="Modifica"
                                                     data-button-remove-label="Elimina"
                                                     data-button-download-label="Scarica"
                                                     data-button-upload-label="Carica"
                                                     data-button-rotate-label="Ruota"
                                                     data-button-cancel-label="Cancella"
                                                     data-button-confirm-label="Conferma"
                                                     data-status-file-size="Il file è troppo grande, il massimo consentito è $0 MB"
                                                     data-status-file-type="Formato immagine non valido, formati consentiti: $0"
                                                     data-status-no-support="Il tuo browser non supporta la modifica dell'immagine"
                                                     data-status-image-too-small="Immagine troppo piccola, risoluzione minima: $0 pixel"
                                                     data-status-content-length="Il server non supporta file così grandi"
                                                     data-status-unknown-response="Errore sconosciuto, <NAME_EMAIL>"
                                                     data-status-upload-success="Immagine salvata"
                                                     data-size="400,400">

                                                    {% if user.imageId is not empty %}
                                                    <img src="{{ paths('IMAGE') }}?oid={{ user.imageId }}" alt=""/>
                                                    {% endif %}
                                                    <input type="file" id="cropper" name="uploaded-files" data-show-caption="false" data-show-remove="true">
                                                </div>
                                            </div>
                                        </div>
                                    </div> 
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Soggetto:</label>
                                    <div class="col-lg-9">
                                        <div class="form-group">
                                            {% if user.profileType == 'admin' or user.profileType == 'system' %}
                                                <select class="select" name="profileType" id="profileType" disabled="">
                                                    <option value="system" {{ user.profileType == 'system' ? 'selected' : '' }}>Sistema</option>
                                                    <option value="admin" {{ user.profileType == 'admin' ? 'selected' : '' }}>Amministratore</option>
                                                    <option value="privato" {{ user.profileType == 'privato' ? 'selected' : '' }}>Privato</option>
                                                    <option value="azienda" {{ user.profileType == 'azienda' ? 'selected' : '' }}>Azienda</option>
                                                </select>
                                            {% else %}
                                                <select class="select" name="profileType" id="profileType">
                                                    <option value="privato" {{ user.profileType == 'privato' ? 'selected' : '' }}>Privato</option>
                                                    <option value="azienda" {{ user.profileType == 'azienda' ? 'selected' : '' }}>Azienda</option>
                                                </select>
                                            {% endif %}
                                        </div>
                                    </div>
                                </div> 
                                <div class="form-group" id="nameForm">
                                    <label class="col-lg-3 control-label">Nome:</label>
                                    <div class="col-lg-9">
                                        <input type="text" name="name" class="form-control maxlength" maxlength="50" placeholder="Nome" value="{{ user.name }}">
                                    </div>
                                </div>
                                <div class="form-group" id="lastnameForm">
                                    <label class="col-lg-3 control-label">Cognome:</label>
                                    <div class="col-lg-9">
                                        <input type="text" name="lastname" class="form-control maxlength" maxlength="50" placeholder="Cognome" value="{{ user.lastname }}">
                                    </div>
                                </div>
                                <div class="form-group" id="fullnameForm">
                                    <label class="col-lg-3 control-label">Ragione sociale:</label>
                                    <div class="col-lg-9">
                                        <input type="text" name="fullname" class="form-control maxlength" maxlength="100" placeholder="Ragione sociale" value="{{ user.fullname }}">
                                    </div>
                                </div>
                                <div class="form-group" id="vatNumberForm">
                                    <label class="col-lg-3 control-label">P.IVA:</label>
                                    <div class="col-lg-9">
                                        <input type="text" name="vatNumber" class="form-control maxlength" maxlength="11" placeholder="Partita IVA" value="{{ user.vatNumber }}">
                                    </div>
                                </div>
                                <div class="form-group" id="tinForm">
                                    <label class="col-lg-3 control-label">Codice fiscale:</label>
                                    <div class="col-lg-9">
                                        <input type="text" name="tin" class="form-control maxlength" maxlength="16" placeholder="Codice fiscale" value="{{ user.tin }}">
                                    </div>
                                </div>
                                <div class="form-group" id="sdiNumberForm">
                                    <label class="col-lg-3 control-label">Codice destinatario:</label>
                                    <div class="col-lg-9">
                                        <input type="text" name="sdiNumber" class="form-control maxlength" maxlength="7" placeholder="Codice destinatario" value="{{ user.sdiNumber }}">
                                    </div>
                                </div> 
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Telefono:</label>
                                    <div class="col-lg-9">
                                        <input type="text" name="phoneNumber" class="form-control maxlength" maxlength="20" placeholder="Telefono" value="{{ user.phoneNumber }}">
                                    </div>
                                </div>                                
                                <div class="form-group" id="pecForm">
                                    <label class="col-lg-3 control-label">PEC:</label>
                                    <div class="col-lg-9">
                                        <input type="text" name="pec" class="form-control maxlength" maxlength="100" placeholder="PEC" value="{{ user.pec }}">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Come ci hai conosciuto:</label>
                                    <div class="col-lg-9">
                                        <div class="form-group">
                                            <select class="select" name="howKnowUs">
                                                <option value="-" {{ user.howKnowUs == '-' ? 'selected' : '' }}>-</option>
                                                <option value="fiera" {{ user.howKnowUs == 'fiera' ? 'selected' : '' }}>Ad una fiera</option>
                                                <option value="facebook" {{ user.howKnowUs == 'facebook' ? 'selected' : '' }}>Ricerca Facebook</option>
                                                <option value="google" {{ user.howKnowUs == 'google' ? 'selected' : '' }}>Ricerca Google</option>
                                                <option value="altro" {{ user.howKnowUs == 'altro' ? 'selected' : '' }}>Altro</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>                                                                    
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Coupon:</label>
                                    <div class="col-lg-9">
                                        <input type="text" name="coupon" class="form-control maxlength" maxlength="20" placeholder="Coupon" value="{{ user.coupon }}">
                                    </div>
                                </div>    
                                <legend class="text-semibold titolo-sezione">
                                    <i class="icon-location4"></i>
                                    INDIRIZZO
                                    <a class="control-arrow" data-toggle="collapse" data-target="#address2">
                                        <i class="icon-circle-down2"></i>
                                    </a>
                                </legend>
                                <div class="collapse in" id="address2">
                                    <div class="form-group">
                                        <label class="col-lg-3 control-label">Indirizzo:</label>
                                        <div class="col-lg-9">
                                            <input type="text" name="address" class="form-control maxlength" maxlength="100" placeholder="Indirizzo" value="{{ user.address }}">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-lg-3 control-label">Città:</label>
                                        <div class="col-lg-9">
                                            <input type="text" name="city" id="citta" class="form-control" maxlength="100" placeholder="Città" value="{{ user.city }}">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-lg-3 control-label">Provincia:</label>
                                        <div class="col-lg-9">
                                            <div class="form-group">
                                                <select class="form-control select-search" id="provinceCode" name="provinceCode">
                                                    <option value="-">-</option>
                                                    {% for item in lookup("province") %}
                                                        <option value="{{ item.code }}" {{ item.code == user.provinceCode ? 'selected' : ''}}>{{ item.description }}</option>
                                                    {% endfor %}
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-lg-3 control-label">CAP:</label>
                                        <div class="col-lg-9">
                                            <input type="text" name="postalCode" id="postalCode" class="form-control maxlength" maxlength="5" placeholder="CAP" value="{{ user.postalCode }}">
                                        </div>
                                    </div>
                                </div>                                
                                <legend class="text-bold">
                                    <i class="icon-key"></i>
                                    CREDENZIALI
                                    <a class="control-arrow" data-toggle="collapse" data-target="#credential">
                                        <i class="icon-circle-down2"></i>
                                    </a>
                                </legend>
                                <div class="collapse in" id="credential">
                                    <div class="form-group">
                                        <label class="col-lg-3 control-label">Email:</label>
                                        <div class="col-lg-9">
                                            <input type="email" id="username" name="username" class="form-control" value="{{ user.username }}" required readonly>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-lg-3 control-label">Password:</label>
                                        <div class="col-lg-9">
                                            <input type="password" name="password" class="form-control" placeholder="Password" value="{{ user.password }}" required>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-lg-3 control-label">Conferma password:</label>
                                        <div class="col-lg-9">
                                            <input type="password" name="password-confirm" class="form-control" placeholder="Conferma password" value="{{ user.password }}" required data-parsley-equalto="#password">
                                        </div>
                                    </div>                                            
                                </div>
                            </div>
                            <div class="panel-footer">
                                <div class="heading-elements">
                                    <span class="heading-text text-semibold">Azioni:</span>
                                    <div class="heading-btn pull-right">                                        
                                        <button type="button" class="btn btn-default btn-cancel">Annulla</button>
                                        <button type="submit" class="btn bg-success">Aggiorna informazioni <i class="icon-arrow-right14"></i></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- /profile info -->
                    </form>
                </div>

            </div>
            <!-- /user profile -->

        </div>
        <!-- /main content -->

    </div>
    <!-- /page content -->
    <script>
        $(function () {
            $('.btn-cancel').click(function () {
                $.confirm({
                    theme: 'supervan',
                    escapeKey: true,
                    animation: 'top',
                    closeAnimation: 'bottom',
                    backgroundDismiss: true,
                    title: '<i class="icon-cancel-square icon-2x"></i><br/><br/> Sei sicuro?',
                    content: "Le modifiche fatte fin'ora verranno perse.",
                    buttons: {
                        annulla: {
                            btnClass: 'btn-default',
                            action: function () {
                                //
                            }
                        },
                        conferma: {
                            btnClass: 'bg-success',
                            action: function () {
                                if ("{{ backUrl }}") {
                                    window.location.href = "{{ backUrl }}";
                                } else {
                                    window.location.href = "{{ paths('PROFILE') }}";
                                }
                            }
                        }
                    }
                });
            });
        });
    </script>
{% endblock %}
