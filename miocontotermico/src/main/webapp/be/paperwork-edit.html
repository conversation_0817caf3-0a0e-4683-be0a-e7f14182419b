{% extends "be/include/base.html" %}

{% set currentPage = 'PAPERWORKS' %}

{% block extrahead %}
{% if paperwork.id is not empty %}
    <title>Modifica documento</title>
{% else %}
    <title>Nuovo documento</title>
{% endif %}
<!-- Theme Custom CSS files -->
<link href="https://siteria.it/libs/dropuploader/1.8.1/css/drop_uploader.min.css" rel="stylesheet">

<!-- Theme JS files -->
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jasny_bootstrap.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/uniform.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/autosize.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/formatter.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/typeahead/typeahead.bundle.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/typeahead/handlebars.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/maxlength.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/validation/validate.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jquery_ui/interactions.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/selects/select2.min.js"></script>
<script src="https://www.siteria.it/libs/autocomplete/1.0.4/auto-complete.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/core/app.js"></script>
<script src="{{ contextPath }}/be/js/pages/paperwork-edit.js?{{ buildNumber }}"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/ui/ripple.min.js"></script>
<script src="https://siteria.it/libs/dropuploader/1.8.1/js/drop_uploader.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/switchery.min.js"></script>
<!-- /theme JS files -->
{% endblock %}

{% block content %}

<a id="imageUri" class="no-display" href="{{ paths('IMAGE') }}?oid=" rel="nofollow"></a>

<a id="paperworksUri" style="display: none" href="{{ paths('PAPERWORKS') }}"></a>
<a id="paperworksSuccessUri" style="display: none;" href="{{ paths('PAPERWORKS') }}/success" rel="nofollow"></a>
<a id="paperworkRemoveUri" style="display: none;" href="{{ paths('PAPERWORK_REMOVE') }}?paperworkId={{ paperwork.id }}" rel="nofollow"></a>

<div id="imageIds" style="display: none;">
    {% if paperwork.id is not empty %}
        {% if paperwork.imageIds is not empty %}
            {% for imageId in paperwork.imageIds %}{{ imageId }}|{% endfor %}
        {%endif%}
    {%endif%}
</div>

<!-- Page content -->
<div class="page-content">
    <!-- Main content -->
    <div class="content-wrapper">

        {% if success %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-success">
                    <a href="{{ paths('PAPERWORK_EDIT') }}" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                    <span>Informazini salvate correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-danger">
                    <a href="{{ paths('PAPERWORK_EDIT') }}" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza.</a></span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Horizontal form options -->
        {% if paperwork.id is not empty %}
            {% set saveUri = paths('PAPERWORK_EDIT_SAVE') + '?paperworkId=' + paperwork.id %}
        {% else %}
            {% set saveUri = paths('PAPERWORK_EDIT_SAVE') %}
        {% endif %}
        <div class="row">
            <div class="col-md-10 col-md-offset-1">
                <!-- Basic layout-->
                <form id="paperwork-form" class="form-horizontal" method="post" action="{{ saveUri }}" enctype="multipart/form-data">
                    <div class="panel panel-white">
                        
                        <div class="panel-heading">
                            {% if paperwork.id is not empty %}
                                <h5 class="panel-title text-bold">Modifica {{ paperwork.title | upper }}</h5>
                            {% else %}
                                <h5 class="panel-title text-bold">Aggiungi documento</h5>
                            {% endif %}
                        </div>
                        
                        <div class="panel-body">
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Titolo:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="title" class="form-control maxlength" maxlength="50" placeholder="Titolo documento" value="{{ paperwork.title }}" required>
                                </div>
                            </div>                            
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Sottotitolo:</label>
                                <div class="col-lg-9">
                                    <input type="text" name="subtitle" class="form-control maxlength" maxlength="250" placeholder="Sottotitolo documento" value="{{ paperwork.subtitle }}">
                                </div>
                            </div> 
                            <div class="form-group">
                                <label class="col-lg-3 control-label">File:</label>
                                <div class="col-lg-9">
                                    {% if paperwork.fileIds is empty %}
                                        <div id="uploader-text" style="display: none;">Trascina qui il file</div>

                                        <div class="text-center">
                                            <input type="file" name="fileIds" data-maxfilessize="8194304" attachment="true" >
                                        </div>
                                    {% endif %}
                                    {% if paperwork.fileIds is not empty %}
                                    <div class="panel panel-flat">
                                        <div class="panel-heading">
                                            <h5 class="panel-title">Già caricato<a class="heading-elements-toggle"><i class="icon-more"></i></a></h5>
                                        </div>

                                        <div class="panel-body">
                                            <div class="content-group-xs" id="bullets"></div>

                                            <ul class="media-list">
                                                {% for fileId in paperwork.fileIds %}
                                                    <li class="media">
                                                        <div class="media-left">
                                                            <a href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" class="btn border-primary text-primary btn-flat btn-rounded btn-icon btn-xs legitRipple"><i class="icon-attachment"></i></a>
                                                        </div>

                                                        <div class="media-body">
                                                            <a class="block m-t-xs" href="{{ paths('FILE') }}?oid={{ fileId }}" target="_blank" rel="noopener">
                                                                {{ fileinfo('file', fileId, 'originalFilename') }}
                                                            </a>
                                                            <div class="media-annotation">{{ fileinfo('file', fileId, 'originalFilename') }}</div>
                                                        </div>

                                                        <div class="media-right media-middle">
                                                            <ul class="icons-list">
                                                                <li>
                                                                    <button type="button" href="{{ paths('PAPERWORK_EDIT_FILEID_REMOVE') }}" class="btn btn-link legitRipple delete-fileid" fileid="{{ fileId }}" paperworkId="{{ paperwork.id }}">Rimuovi</button>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </li>
                                                {% endfor %}
                                            </ul>
                                        </div>
                                    </div>
                                    {% endif %}

                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Visibile:</label>
                                <div class="col-lg-9">
                                    <label class="checkbox-inline checkbox-right checkbox-switchery switchery-sm">
                                        <input id="visible" name="visible" type="checkbox" class="switchery" {{ paperwork.visible ? 'checked' : '' }}>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="panel-footer">
                            <div class="heading-elements">
                                <span class="heading-text text-semibold">Azioni:</span>
                                <div class="heading-btn pull-right">
                                    <button type="button" class="btn btn-default btn-cancel">Annulla</button>
                                    {% if paperwork.id is not empty %}                                    
                                        <button id="btn-delete" type="button" class="btn bg-danger-600">Rimuovi</button>
                                    {% endif %}
                                    <button id="btn-save" type="submit" class="btn bg-success">Pubblica documento<i class="icon-arrow-right14"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <!-- /basic layout -->
            </div>
        </div>
    </div>
    <!-- /main content -->

</div>
<!-- /page content -->

{% endblock %}
