{% extends "be/include/base.html" %}

{% block extrahead %}
{% if property.id is not empty %}
    <title>Visualizza valutazione termica</title>
{% else %}
    <title>Nuova valutazione</title>
{% endif %}

<!-- Theme Custom CSS files -->
<link href="https://siteria.it/libs/slim/4.2.1/slim/slim.min.css" rel="stylesheet">
<link href="https://siteria.it/libs/dropuploader/1.8.1/css/drop_uploader.min.css" rel="stylesheet">

<!-- Theme JS files -->
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/switchery.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/editable/editable.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jasny_bootstrap.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/uniform.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/autosize.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/formatter.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/typeahead/typeahead.bundle.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/typeahead/handlebars.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/maxlength.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/validation/validate.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/validation/localization/messages_it.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jquery_ui/interactions.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/selects/select2.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/wizards/steps.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/extensions/cookie.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment.min.js"></script>
<script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/pickers/daterangepicker.js"></script>
<script src="https://siteria.it/libs/slim/4.2.1/slim/slim.kickstart.min.js"></script>
<script src="https://siteria.it/libs/jquery-mask/1.14.13/dist/jquery.mask.min.js"></script>
<script src="https://siteria.it/libs/sortable/1.7.0/Sortable.min.js"></script>
<script src="https://siteria.it/libs/autocomplete/1.0.4/auto-complete.min.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/app.js"></script>
<script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/ui/ripple.min.js"></script>
<script src="https://siteria.it/libs/dropuploader/1.8.1/js/drop_uploader.min.js"></script>
<script src="{{ contextPath }}/be/js/pages/evaluation-edit.js?{{ buildNumber }}"></script>
<!-- /theme JS files -->
{% endblock %}

{% block content %}

<a id="evaluationsSuccessUri" class="no-display" href="{{ paths('EVALUATIONS') }}/success" rel="nofollow"></a>    
    
<!-- some hidden stuff -->
<a id="imageUri" class="no-display" href="{{ paths('IMAGE') }}?oid=" rel="nofollow"></a>

<!-- Page content -->
<div class="page-content">
    <!-- Main content -->
    <div class="content-wrapper">

        {% if success %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-success">
                    <a href="{{ paths('EVALUATION_EDIT') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Dati memorizzati correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-danger">
                    <a href="{{ paths('EVALUATION_EDIT') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Horizontal form options -->
        <div class="row">
            <div class="col-md-8 col-md-offset-2">
                {% set disabled = 'disabled' %}
                {% if user.profileType == 'admin' or user.profileType == 'operatore' %}    
                    {% set disabled = '' %}
                {% endif %}                <!-- Editable inputs -->
                <div class="panel panel-flat">
                    <div class="panel-heading">
                        <h5 class="panel-title">Pratica #{{ evaluation.protocol }}</h5>
                    </div>

                    <div class="table-responsive">
                        <form id="form-edit-evaluation" class="form-horizontal form-validate-jquery">
                            <table class="table table-lg">
                                <tr>
                                    <th colspan="2" class="active">Pratica assegnata a</th>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Utente</td>                                                                                                                                  
                                    <td>
                                        <select class="select" name="assignedUserId" id="assignedUserId" {{ disabled }}>
                                            <option value=""> - </option>
                                            {% for assignedUser in userList %}
                                                {{ evaluation.assignedUserId }}
                                                <option value="{{ assignedUser.id }}" {{ evaluation.assignedUserId == assignedUser.id ? 'selected' : '' }}> {{ assignedUser.name}} {{ assignedUser.lastname}}</option>
                                            {% endfor %}    
                                        </select>
                                    </td>                                
                                </tr>
                                <tr>
                                    <th colspan="2" class="active">Stato pratica</th>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Stato</td>   
                                    <td>                                    
                                        <select class="select" name="status" id="status"  {{ disabled }}>
                                            <option value="opened" {{ evaluation.status == 'opened' ? 'selected' : '' }}>Ricevuta</option>
                                            <option value="processing" {{ evaluation.status == 'processing' ? 'selected' : '' }}>In lavorazione</option>
                                            <option value="approved" {{ evaluation.status == 'approved' ? 'selected' : '' }}>Approvata</option>
                                            <option value="annulled" {{ evaluation.status == 'annulled' ? 'selected' : '' }}>Annullata</option>
                                        </select>                                    
                                    </td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Note</td>   
                                    <td>                                    
                                        <textarea type="text" name="note" id="note" class="form-control maxlength" rows="5" placeholder="note...." {{ disabled }}>{{ evaluation.note }}</textarea>
                                    </td>
                                </tr>
                            </table>
                            {% if user.profileType == 'admin' or user.profileType == 'operatore' %}   
                                <div class="panel-footer has-visible-elements">
                                    <div class="heading-elements visible-elements">
                                        <span class="heading-text text-semibold">Azioni:</span>
                                        <div class="pull-right">                                    
                                            <button type="button" class="evaluationSave btn bg-success-600" href="{{ paths('EVALUATION_EDIT_SAVE') }}?evaluationId={{ evaluation.id }}">Salva <i class="icon-arrow-right14"></i></button>
                                        </div>
                                    </div>
                                </div>
                            {% endif %}
                        </form>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-lg">

                            <tr>
                                <th colspan="2" class="active">Informazioni personali</th>
                            </tr>
                            <tr>
                                
                                <td style="width: 33%;">Soggetto</td>
                                {% if evaluation.profileType == 'privato' %}
                                    <td><span class="label bg-info">{{ evaluation.profileType }}</span></td>
                                {% elseif evaluation.profileType == 'azienda'  %}
                                    <td><span class="label bg-warning">{{ evaluation.profileType }}</span></td>
                                {% endif %}
                            </tr>
                            {% if evaluation.profileType == 'privato' %}  
                                <tr>
                                    <td style="width: 33%;">Nome</td>
                                    <td>{{ evaluation.name }}</td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">Cognome</td>
                                    <td>{{ evaluation.lastname }}</td>
                                </tr>
                            {% endif %}
                            {% if evaluation.profileType == 'azienda' %}  
                                <tr>
                                    <td style="width: 33%;">Ragione sociale</td>
                                    {% set fullname = '*Nome* *Cognome*' %}
                                    {% if evaluation.fullname is not empty %}
                                        {% set fullname = evaluation.fullname %}
                                    {% elseif (evaluation.name is not empty) and (evaluation.lastname is not empty) %}
                                        {% set fullname = (evaluation.name | default('*Nome*')) + ' ' + evaluation.lastname | default('*Cognome*') %}
                                    {% endif %}
                                    <td>{{ fullname }}</td>
                                </tr>
                                <tr>
                                    <td style="width: 33%;">P.IVA</td>
                                    <td>{{ evaluation.vatNumber }}</td>
                                </tr>
                            {% endif %}
                            <tr>
                                <td style="width: 33%;">Paese installazione</td>
                                <td>{{ evaluation.city }}</td>
                            </tr>
                            <tr>
                                <td style="width: 33%;">Marca generatore:</td>
                                <td>{{ evaluation.brand }}</td>
                            </tr>
                            <tr>
                                <td style="width: 33%;">Modello generatore:</td>
                                <td>{{ evaluation.model }}</td>
                            </tr>
                            <tr>
                                <td style="width: 33%;">Incasso installatore:</td>
                                <td>{{ evaluation.recessedInstaller }}</td>
                            </tr>
       
                            <tr>
                                <th colspan="2" class="active">Documenti caricati <a href="{{ paths('FILEZIP') + '?evaluationId=' + evaluation.id }}" download>Scarica tutti</a></th>
                            </tr>
                            {% if evaluation.localFileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Foto del locale con ripresa ad ampio raggio:</td>
                                    <td>
                                        {% for fileId in evaluation.localFileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if evaluation.distanceGeneratorFileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Foto da distante del generatore acceso:</td>
                                    <td>
                                        {% for fileId in evaluation.distanceGeneratorFileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if evaluation.nearGeneratorFileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Foto da vicino del generatore acceso:</td>
                                    <td>
                                        {% for fileId in evaluation.nearGeneratorFileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if evaluation.allFlueIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Foto di visione di tutta la canna fumaria:</td>
                                    <td>
                                        {% for fileId in evaluation.allFlueIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if evaluation.retroFlueFileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Foto dei collegamenti della canna fumaria nello schienale del generatore:</td>
                                    <td>
                                        {% for fileId in evaluation.retroFlueFileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}

                            {% if evaluation.wallFlueFileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Foto dei collegamenti della canna fumaria al muro:</td>
                                    <td>
                                        {% for fileId in evaluation.wallFlueFileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if evaluation.connectionsFlueFileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Foto dei collegamenti idraulici:</td>
                                    <td>
                                        {% for fileId in evaluation.connectionsFlueFileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            {% if evaluation.plateGeneratorFileIds is not empty %}
                                <tr>
                                    <td style="width: 33%;">Foto della targa del generatore:</td>
                                    <td>
                                        {% for fileId in evaluation.plateGeneratorFileIds %}
                                            <a href="{{ paths('FILE') + '?oid=' + fileId }}" download>Allegato #{{ loop.index + 1 }}: {{ fileinfo('file', fileId, 'originalFilename') }}</a>
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endif %}
                            
                            <!-- ecc ecc -->
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- /main content -->

</div>
<!-- /page content -->

<script>
    $('.btn-cancel').click(function () {
        $.confirm({
            theme: 'supervan',
            escapeKey: true,
            animation: 'top',
            closeAnimation: 'bottom',
            backgroundDismiss: true,
            title: '<i class="icon-cancel-square icon-2x"></i><br/><br/> Sei sicuro?',
            content: "Le modifiche fatte fin'ora verranno perse.",
            buttons: {
                annulla: {
                    btnClass: 'btn-default',
                    action: function () {
                        //
                    }
                },
                conferma: {
                    btnClass: 'bg-success',
                    action: function () {
                        window.location.href = "{{ paths('EVALUATIONS') }}?selectedStatuses=opened%7Cprocessing%7C";
                    }
                }
            }
        });
    });
</script>

{% endblock %}
