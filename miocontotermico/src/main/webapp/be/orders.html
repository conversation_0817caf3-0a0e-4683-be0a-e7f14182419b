{% extends "be/include/base.html" %}
{% block extrahead %}
    <title>Ordini</title>

    <!-- SCRIPTS -->    
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment_locales.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/pickers/daterangepicker.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/styling/uniform.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/datatables.min.js"></script>
    <script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/jszip/jszip.min.js"></script>
    <script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/pdfmake/pdfmake.min.js"></script>
    <script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/pdfmake/vfs_fonts.min.js"></script>
    <script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/buttons.min.js"></script>    
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/tables/datatables/extensions/responsive.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/selects/select2.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/app.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/ui/ripple.min.js"></script>
    <script src="{{ contextPath }}/be/js/pages/orders.js?{{ buildNumber }}"></script>
{% endblock %}

{% block sidebarcontrol %}
<ul class="fab-menu fab-menu-top-left hidden-xs" data-fab-toggle="click">
    <li>
        <a class="fab-menu-btn btn btn-default btn-float btn-rounded btn-icon sidebar-control sidebar-main-hide">
            <i class="icon-paragraph-justify3"></i>
        </a>
    </li>
</ul>
{% endblock %}

{% block content %}
    {% set isAdmin = (user.profileType == 'system') or (user.profileType == 'admin') %}

    <div id="isAdmin" style="display: none">{{ isAdmin }}</div>
    <!-- data -->
    <a id="ordersDataUri" style="display: none;" href="{{ paths('ORDERS_DATA') }}" rel="nofollow"></a>

    <!-- actions -->
    <a id="orderViewUri" style="display: none" href="{{ paths('ORDER_VIEW') }}?orderId="></a>
    <a id="userViewUri" style="display: none" href="{{ paths('USER_DETAIL') }}?userId="></a>
    <a id="orderPaymentStatusUpdateUnpaidUri" style="display: none" href="{{ paths('ORDER_PAYMENT_STATUS_UPDATE') }}?paymentStatus=-&orderId="></a>
    <a id="orderStatusUpdateAnnulledUri" style="display: none" href="{{ paths('ORDER_STATUS_UPDATE') }}?status=annulled&orderId="></a>
    <a id="orderPaymentStatusUpdatePaidUri" style="display: none" href="{{ paths('ORDER_PAYMENT_STATUS_UPDATE') }}?paymentStatus=paid&orderId="></a>
    
    <!-- js deposit -->
    <div id="startDate" style="display: none">{{ startDate | date("yyyy-MM-dd") }}</div>
    <div id="endDate" style="display: none">{{ endDate | date("yyyy-MM-dd") }}</div>
    {% set isVendor = (user.profileType != 'system') and (user.profileType != 'admin') %}
    <div id="isVendor" style="display: none">{{ isVendor }}</div>

    <!-- PAGE CONTENT -->
    <div class="page-content">
        <!-- LEFT SIDEBAR -->
        <div class="sidebar sidebar-main sidebar-default sidebar-separate">
            <div class="sidebar-content">

                <!-- Filter -->
                <div class="sidebar-category">
                    <div class="category-title">
                        <span>Filtra ordini</span>
                        <ul class="icons-list">
                            <li><a href="#" data-action="collapse"></a></li>
                        </ul>
                    </div>

                    <div class="category-content">
                        <form>
                            {% if paymentStatusList is not empty %}
                                <div class="statuses form-group">
                                    <legend class="text-size-mini text-muted no-border no-padding">Pagamento</legend>
                                    <div>
                                        {% for paymentStatus in paymentStatusList %}
                                            <div class="checkbox">
                                                <label class="display-block text-capitalize">
                                                    <input type="checkbox" class="styled mct-payment-status-filter" mct-payment-status-value="{{ paymentStatus }}" {{ selectedPaymentStatuses contains paymentStatus ? 'checked' : ''}}>
                                                        {% if paymentStatus == '-' %}
                                                            <span class="label label-flat border-danger-600 text-danger-600">ARRETRATO</span>
                                                        {% elseif paymentStatus == 'paid' %}
                                                            <span class="label label-flat border-success-700 text-success-700">PAGATO</span>
                                                        {% endif %}
                                                </label>
                                            </div>
                                        {% endfor %}
                                    </div>
                                </div>
                            {% endif %}
                            <button id="filter-apply" type="button" class="btn bg-black btn-block"><i class="icon-filter3 position-left"></i>Filtra</button>
                        </form>
                    </div>
                </div>
                <!-- /filter -->

            </div>
        </div>
        <!-- END LEFT SIDEBAR -->

        <!-- MAIN CONTENT -->
        <div class="content-wrapper">

            {% if success %}
                <div class="row">
                    <div class="col-md-12">
                        <div class="alert alert-success">
                            <a href="{{ paths('ORDERS') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                            <span>Dati memorizzati correttamente</span>
                        </div>
                    </div>
                </div>
            {% elseif error %}
                <div class="row">
                    <div class="col-md-12">
                        <div class="alert alert-danger">
                            <a href="{{ paths('ORDERS') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                            <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                        </div>
                    </div>
                </div>
            {% endif %}

            <!-- Control position -->
            <div class="panel panel-white">
                <div class="panel-heading has-visible-elements">
                    <h5 class="panel-title text-bold">Ordini</h5>
                    <div class="heading-elements visible-elements">                                                
                        <div class="btn-group heading-btn">
                            
                            
                        </div>                        
                        <button type="button" class="btn heading-btn btn-default daterange-predefined">
                            <i class="icon-calendar22 position-left"></i>
                            <span></span>
                            <b class="caret"></b>
                        </button>
                    </div>
                </div>

                <div id="ordersTable">
                    <table class="table datatable-responsive-control-right">
                        <thead>
                            <tr>
                                <th>Cliente</th>                                
                                <th>Numero</th>
                                <th>Data e ora</th>
                                <th>Totale</th>
                                <th class="text-center">Azioni</th>
                                <th></th>
                            </tr>
                        </thead>
                    </table>
                </div>
            </div>
            <!-- /control position -->

        </div>
        <!-- END MAIN CONTENT -->

    </div>
    <!-- END PAGE CONTENT -->

{% endblock %}
