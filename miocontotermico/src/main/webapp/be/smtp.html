{% extends "be/include/base.html" %}

{% block extrahead %}
    <title>Configurazione POSTA</title>
    <script src="https://www.siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/styling/switchery.min.js"></script>
    <script src="https://www.siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/inputs/maxlength.min.js"></script>
    <script src="https://www.siteria.it/tpls/be/1/2.1/global_assets/js/plugins/forms/validation/validate.min.js"></script>
    <script src="{{ contextPath }}/be/js/pages/smtp.js?{{ buildNumber }}"></script>
{% endblock %}

{% block content %}
    <a id="profileUri" style="display: none" href="{{ paths('SMTP') }}"></a>
    <!-- Page content -->
    <div class="page-content">

        <!-- Main content -->
        <div class="content-wrapper">
            <div class="row">
                <div class="col-md-10 col-md-offset-1 col-lg-8 col-lg-offset-2">
                    {% if success %}
                    <div class="row">
                        <div class="col-md-12">
                            <div class="alert alert-success">
                                <a href="{{ paths('SMTP') }}" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                                <span>Dati memorizzati correttamente</span>
                            </div>
                        </div>
                    </div>
                    {% elseif error %}
                    <div class="row">
                        <div class="col-md-12">
                            <div class="alert alert-danger">
                                <a href="{{ paths('SMTP') }}" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                                <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Horizontal form options -->
                    {% if smtp.id is not empty %}
                        {% set saveUri = paths('SMTP_SAVE') + '?smtpId=' + smtp.id %}
                    {% else %}
                        {% set saveUri = paths('SMTP_SAVE') %}
                    {% endif %}
                    <!-- Smtp info -->
                    <form id="form-edit-smtp" class="form-horizontal form-validate-jquery" method="post" action="{{ saveUri }}">
                        <div class="panel panel-white">
                            <div class="panel-heading">
                                <h5 class="panel-title text-bold">Configurazione POSTA</h5>
                            </div>
                            <div class="panel-body">
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Hostname (SMTP):</label>
                                    <div class="col-lg-9">
                                        <input type="text" name="hostname" class="form-control" placeholder="Hostname" value="{{ smtp.hostname }}" required>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Porta (SMTP):</label>
                                    <div class="col-lg-9">
                                        <input type="number" name="port" class="form-control" placeholder="Port" value="{{ smtp.port }}" required>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Autenticazione:</label>
                                    <div class="col-lg-9">
                                        <div class="checkbox checkbox-switchery switchery-sm">
                                            <label>
                                                <input type="checkbox" class="switchery" name="authentication"  {{ smtp.authentication ? 'checked' : '' }} name="authentication">
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Username:</label>
                                    <div class="col-lg-9">
                                        <input type="text" name="username" class="form-control" placeholder="Username" value="{{ smtp.username }}" required>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Password:</label>
                                    <div class="col-lg-9">
                                        <input type="password" name="password" class="form-control" placeholder="Password" value="{{ smtp.password }}" required>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Crittografia:</label>
                                    <div class="col-lg-9">
                                        <div class="checkbox checkbox-switchery switchery-sm">
                                            <label>
                                                <input type="checkbox" class="switchery" {{ smtp.encryption ? 'checked' : '' }} name="encryption">
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-lg-3 control-label">Email mittente:</label>
                                    <div class="col-lg-9">
                                        <input type="text" name="sender" class="form-control" placeholder="Sender name" value="{{ smtp.sender }}" required>
                                    </div>
                                </div>
                            </div>
                            <div class="panel-footer has-visible-elements">
                                <div class="heading-elements visible-elements">
                                    <span class="heading-text text-semibold">Azioni:</span>
                                    <div class="pull-right">
                                        <button type="button" class="btn heading-btn btn-default btn-cancel">Annulla</button>
                                        <button id="btn-save" type="submit" class="btn heading-btn bg-black">Salva<i class="icon-arrow-right14"></i></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

            </div>

        </div>
        <!-- /main content -->

    </div>
    <!-- /page content -->
{% endblock %}
