{% extends "be/include/base.html" %}

{% block extrahead %}
<title>Modifica template</title>
<!-- Theme Custom CSS files -->
<link href="https://www.siteria.it/libs/slim/4.2.1/slim/slim.min.css" rel="stylesheet">
<!-- Theme JS files -->
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jasny_bootstrap.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/uniform.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/autosize.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/formatter.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/typeahead/typeahead.bundle.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/typeahead/handlebars.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/maxlength.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/validation/validate.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jquery_ui/interactions.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/selects/select2.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/editors/wysihtml5/wysihtml5.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/editors/wysihtml5/toolbar.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/editors/wysihtml5/parsers.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/editors/wysihtml5/locales/bootstrap-wysihtml5.ua-UA.js"></script>
<script src="https://www.siteria.it/libs/slim/4.2.1/slim/slim.kickstart.min.js"></script>
<script src="https://www.siteria.it/libs/sortable/1.7.0/Sortable.min.js"></script>
<script src="https://www.siteria.it/libs/autocomplete/1.0.4/auto-complete.min.js"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/core/app.js"></script>
<script src="{{ contextPath }}/be/js/pages/mail-detail.js?{{ buildNumber }}"></script>
<script src="https://www.siteria.it/tpls/be/1/1.6/assets/js/plugins/ui/ripple.min.js"></script>
<!-- /theme JS files -->
{% endblock %}

{% block content %}

<a id="mailsUri" style="display: none" href="{{ paths('MAILS') }}"></a>
<a id="mailsSuccessUri" style="display: none;" href="{{ paths('MAILS') }}/success" rel="nofollow"></a>

<!-- Page content -->
<div class="page-content">
    <!-- Main content -->
    <div class="content-wrapper">

        {% if success %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-success">
                    <a href="{{ paths('MAILS') }}" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                    <span>Informazini salvate correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-danger">
                    <a href="{{ paths('MAILS') }}" class="close" aria-label="Close"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza.</a></span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Horizontal form options -->
        {% set saveUri = paths('MAIL_DETAIL_SAVE') + '?mailId=' + mail.id %}
        <div class="row">
            <div class="col-md-10 col-md-offset-1">
                <!-- Basic layout-->
                <form id="mail-form" class="form-horizontal" method="post" action="{{ saveUri }}">
                    
                    <div class="panel panel-white">
                        
                        <div class="panel-heading">
                            <h5 class="panel-title text-bold">Modifica {{ mail.template | upper }}</h5>
                        </div>
                        
                        <div class="panel-body">
                            <div class="form-group">
                                <label class="col-lg-3 control-label">Descrizione:</label>
                                <div class="col-lg-9">
                                    <div class="form-group">
                                        <textarea cols="18" rows="18" name="description" class="wysihtml5 wysihtml5-min form-control" placeholder="Aggiungi del testo...">
                                            {{ mail.description }}
                                        </textarea>
                                    </div>                                    
                                </div>
                            </div>                            
                        </div>
                        <div class="panel-footer">
                            <div class="heading-elements">
                                <span class="heading-text text-semibold">Azioni:</span>
                                <div class="heading-btn pull-right">
                                    <button type="button" class="btn btn-default btn-cancel">Annulla</button>
                                    <button id="btn-save" type="submit" class="btn bg-success">Salva<i class="icon-arrow-right14"></i></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
                <!-- /basic layout -->
            </div>
        </div>
    </div>
    <!-- /main content -->

</div>
<!-- /page content -->

{% endblock %}
