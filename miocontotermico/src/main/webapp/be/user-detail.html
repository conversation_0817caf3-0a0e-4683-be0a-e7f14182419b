{% extends "be/include/base.html" %}

{% block extrahead %}
    <title>Utente</title>    
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jasny_bootstrap.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/uniform.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/datatables.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/responsive.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/buttons.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/selects/select2.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/switchery.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jasny_bootstrap.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/uniform.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/autosize.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/formatter.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/typeahead/typeahead.bundle.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/typeahead/handlebars.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/inputs/maxlength.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/validation/validate.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/validation/localization/messages_it.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jquery_ui/interactions.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/selects/select2.min.js"></script>    
    <script src="https://siteria.it/libs/jquery-mask/1.14.13/dist/jquery.mask.min.js"></script>
    <script src="https://siteria.it/libs/sortable/1.7.0/Sortable.min.js"></script>
    <script src="https://siteria.it/libs/autocomplete/1.0.4/auto-complete.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/app.js"></script>
    <script src="{{ contextPath }}/be/js/pages/user-detail.js?{{ buildNumber }}"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/ui/ripple.min.js"></script>
{% endblock %}

{% block content %}
    <a id="usersSuccessUri" style="display: none" href="{{ paths('USERS') }}/success" rel="nofollow"></a>
    <a id="userDetailSuccessUri" style="display: none" href="{{ paths('USER_DETAIL') }}/success?userId={{ userRegistered.id }}" rel="nofollow"></a>
    <a id="userRemoveUri" style="display: none" href="{{ paths('USER_REMOVE') }}?userId={{ userRegistered.id }}" rel="nofollow"></a>
    <a id="userUpdateCreditUri" style="display: none" href="{{ paths('USER_UPDATE_CREDIT') }}?userId={{ userRegistered.id }}&credit=" rel="nofollow"></a>

    <!-- Page content -->
    <div class="page-content">

        <!-- Main content -->
        <div class="content-wrapper">

            {% if success %}
                <div class="row">
                    <div class="col-md-12">
                        <div class="alert alert-success">
                            <a href="{{ paths('USER_DETAIL') }}?userId={{ userRegistered.id }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                            <span>Dati memorizzati correttamente</span>
                        </div>
                    </div>
                </div>
            {% elseif error %}
                <div class="row">
                    <div class="col-md-12">
                        <div class="alert alert-danger">
                            <a href="{{ paths('USER_DETAIL') }}?userId={{ userRegistered.id }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                            <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                        </div>
                    </div>
                </div>
            {% endif %}

            <div class="row">
                <div class="col-md-8 col-md-offset-2">                   
                    <!-- Toolbar -->
                    <div class="navbar navbar-default navbar-component navbar-xs">
                        <ul class="nav navbar-nav visible-xs-block">
                            <li class="full-width text-center"><a data-toggle="collapse" data-target="#navbar-filter"><i class="icon-menu7"></i></a></li>
                        </ul>

                        <div class="navbar-collapse collapse" id="navbar-filter">
                            <ul class="nav navbar-nav">
                                <li class="active"><a href="#info" data-toggle="tab"><i class="icon-info22 position-left"></i> Informazioni</a></li>                                
                                <li><a href="#procedures" data-toggle="tab"><i class="icon-file-text2 position-left"></i> Procedure</a></li>                                                                                    
                            </ul>
                        </div>
                    </div>
                    <!-- /toolbar -->
                </div>
            </div>
            
            <!-- User profile -->
            <div class="row">
                <div class="tabbable">
                    <div class="tab-content">
                        <div class="tab-pane fade in active" id="info">
                            <div class="col-md-8 col-md-offset-2">                   
                                <!-- Profile info -->
                                <form id="form-edit-profile" class="form-horizontal form-validate-jquery" method="post" action="{{ paths('PROFILE_SAVE') }}?oid={{ userRegistered.id }}">
                                    <div class="panel panel-white">
                                        <div class="panel-heading">
                                            <h5 class="panel-title text-bold">Informazioni</h5>                                        
                                        </div>
                                        <div class="panel-body">
                                            <div class="form-group">
                                                <label class="col-lg-3 control-label">Crediti:</label>
                                                <div class="col-lg-9">
                                                    {% if user.profileType == 'admin' %}    
                                                        <input type="number" id="credit" name="credit" class="form-control maxlength" maxlength="50" placeholder="Crediti" value="{{ userRegistered.credit }}">
                                                    {% else %}
                                                        <input type="number" name="credit" class="form-control maxlength" maxlength="50" placeholder="Crediti" value="{{ userRegistered.credit }}" disabled>
                                                    {% endif %}
                                                </div>
                                            </div>                                            
                                            <div class="form-group">
                                                <label class="col-lg-3 control-label">Foto utente:</label>
                                                <div class="col-lg-9">
                                                    <div class="row">
                                                        <div class="col-xs-6 col-xs-offset-3 col-sm-4 col-sm-offset-4 col-md-4 col-md-offset-4">                                                
                                                            {% if userRegistered.imageId is not empty %}
                                                            <img src="{{ paths('IMAGE') }}?oid={{ userRegistered.imageId }}" alt=""/>
                                                            {% else %}
                                                            <p>Nessuna immagine</p>
                                                            {% endif %}                                                                                                    
                                                        </div>
                                                    </div>
                                                </div> 
                                            </div>
                                            <div class="form-group">
                                                <label class="col-lg-3 control-label">Soggetto:</label>
                                                <div class="col-lg-9">
                                                    <div class="form-group">
                                                        <select class="select" name="profileType" id="profileType" disabled>
                                                            <option value="privato" {{ userRegistered.profileType == 'privato' ? 'selected' : '' }}>Privato</option>
                                                            <option value="azienda" {{ userRegistered.profileType == 'azienda' ? 'selected' : '' }}>Azienda</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div> 
                                            <div class="form-group">
                                                <label class="col-lg-3 control-label">Nome:</label>
                                                <div class="col-lg-9">
                                                    <input type="text" name="name" class="form-control maxlength" maxlength="50" placeholder="Nome" value="{{ userRegistered.name }}" disabled>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-lg-3 control-label">Cognome:</label>
                                                <div class="col-lg-9">
                                                    <input type="text" name="lastname" class="form-control maxlength" maxlength="50" placeholder="Cognome" value="{{ userRegistered.lastname }}" disabled>
                                                </div>
                                            </div>
                                            <div class="form-group" id="fullnameForm">
                                                <label class="col-lg-3 control-label">Ragione sociale:</label>
                                                <div class="col-lg-9">
                                                    <input type="text" name="fullname" class="form-control maxlength" maxlength="100" placeholder="Ragione sociale" value="{{ userRegistered.fullname }}" disabled>
                                                </div>
                                            </div>
                                            <div class="form-group" id="vatNumberForm">
                                                <label class="col-lg-3 control-label">P.IVA:</label>
                                                <div class="col-lg-9">
                                                    <input type="text" name="vatNumber" class="form-control maxlength" maxlength="11" placeholder="Partita IVA" value="{{ userRegistered.vatNumber }}" disabled>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-lg-3 control-label">Codice fiscale:</label>
                                                <div class="col-lg-9">
                                                    <input type="text" name="tin" class="form-control maxlength" maxlength="16" placeholder="Codice fiscale" value="{{ userRegistered.tin }}" disabled>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-lg-3 control-label">Telefono:</label>
                                                <div class="col-lg-9">
                                                    <input type="text" name="phoneNumber" class="form-control maxlength" maxlength="20" placeholder="Telefono" value="{{ userRegistered.phoneNumber }}" disabled>
                                                </div>
                                            </div>                                
                                            <div class="form-group" id="pecForm">
                                                <label class="col-lg-3 control-label">PEC:</label>
                                                <div class="col-lg-9">
                                                    <input type="text" name="pec" class="form-control maxlength" maxlength="100" placeholder="PEC" value="{{ userRegistered.pec }}" disabled>
                                                </div>
                                            </div>
                                            <div class="form-group" id="sdiForm">
                                                <label class="col-lg-3 control-label">Codice univoco:</label>
                                                <div class="col-lg-9">
                                                    <input type="text" name="sdiNumber" class="form-control maxlength" maxlength="100" placeholder="codice univoco" value="{{ userRegistered.sdiNumber }}" disabled>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-lg-3 control-label">Come ci hai conosciuto:</label>
                                                <div class="col-lg-9">
                                                    <div class="form-group">
                                                        <select class="select" name="howKnowUs" disabled>
                                                            <option value="-" {{ userRegistered.howKnowUs == '-' ? 'selected' : '' }}>-</option>
                                                            <option value="fiera" {{ userRegistered.howKnowUs == 'fiera' ? 'selected' : '' }}>Ad una fiera</option>
                                                            <option value="facebook" {{ userRegistered.howKnowUs == 'facebook' ? 'selected' : '' }}>Ricerca Facebook</option>
                                                            <option value="google" {{ userRegistered.howKnowUs == 'google' ? 'selected' : '' }}>Ricerca Google</option>
                                                            <option value="altro" {{ userRegistered.howKnowUs == 'altro' ? 'selected' : '' }}>Altro</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>                                                                    
                                            <div class="form-group">
                                                <label class="col-lg-3 control-label">Coupon:</label>
                                                <div class="col-lg-9">
                                                    <input type="text" name="coupon" class="form-control maxlength" maxlength="20" placeholder="Coupon" value="{{ userRegistered.coupon }}" disabled>
                                                </div>
                                            </div>    
                                            <legend class="text-semibold titolo-sezione">
                                                <i class="icon-location4"></i>
                                                INDIRIZZO
                                                <a class="control-arrow" data-toggle="collapse" data-target="#address2">
                                                    <i class="icon-circle-down2"></i>
                                                </a>
                                            </legend>
                                            <div class="collapse in" id="address2">
                                                <div class="form-group">
                                                    <label class="col-lg-3 control-label">Indirizzo:</label>
                                                    <div class="col-lg-9">
                                                        <input type="text" name="address" class="form-control maxlength" maxlength="100" placeholder="Indirizzo" value="{{ userRegistered.address }}" disabled>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label class="col-lg-3 control-label">Città:</label>
                                                    <div class="col-lg-9">
                                                        <input type="text" name="city" id="citta" class="form-control" maxlength="100" placeholder="Città" value="{{ userRegistered.city }}" disabled>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label class="col-lg-3 control-label">Provincia:</label>
                                                    <div class="col-lg-9">
                                                        <div class="form-group">
                                                            <select class="form-control select-search" id="provinceCode" name="provinceCode" disabled>
                                                                <option value="-">-</option>
                                                                {% for item in lookup("province") %}
                                                                    <option value="{{ item.code }}" {{ item.code == userRegistered.provinceCode ? 'selected' : ''}}>{{ item.description }}</option>
                                                                {% endfor %}
                                                            </select>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="form-group">
                                                    <label class="col-lg-3 control-label">CAP:</label>
                                                    <div class="col-lg-9">
                                                        <input type="text" name="postalCode" id="postalCode" class="form-control maxlength" maxlength="5" placeholder="CAP" value="{{ userRegistered.postalCode }}" disabled>
                                                    </div>
                                                </div>
                                            </div>   
                                        </div>
                                        <div class="panel-footer has-visible-elements">
                                            <div class="heading-elements visible-elements">
                                                <span class="heading-text text-semibold">Azioni:</span>
                                                <div class="pull-right">
                                                    {% if user.profileType == 'admin' %}    
                                                        <button id="editCredit" type="button" class="btn heading-btn btn-success">Aggiorna crediti</button>
                                                    {% endif%}
                                                    <button id="deleteUser" type="button" class="btn heading-btn btn-danger">Elimina utente + sue pratiche</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>                        
                                </form>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="procedures">
                            <div class="col-md-8 col-md-offset-2">                   
                                <!-- State saving -->
                                <div class="panel panel-white">
                                    <div class="panel-heading">
                                        <h5 class="panel-title text-bold">Pratiche</h5>
                                    </div>

                                    <table class="table datatable-procedures">
                                        <thead>
                                            <tr>
                                                <th>Riferimento</th>
                                                {% if user.profileType == 'admin' %}    
                                                    <th>Caricato da/Email registrazione</th>
                                                {% endif %}
                                                {% if user.profileType == 'admin' or user.profileType == 'azienda' %}                                                            
                                                    <th>Tipo</th>                                                        
                                                    <th>Soggetto pratica</th>                                                        
                                                    <th>Titolarità</th>                            
                                                {% endif %}
                                                <th>Data</th>
                                                <th>Stato</th>                        
                                                {% if user.profileType == 'admin' %}    
                                                    <th class="text-center">Azioni</th>
                                                {% endif %}
                                                <th></th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% if procedureList is not empty %}
                                                {% for procedure in procedureList %}
                                                    <tr>                                
                                                        <td><a href="{{ paths('PROCEDURE_EDIT') }}?procedureId={{ procedure.procedure.id }}" class="text-bold text-uppercase" target="_blank"><span sortable-value="{{ procedure.procedure.protocol }}">Pratica #{{ procedure.procedure.protocol }}</span></a></td>
                                                        {% if user.profileType == 'admin' or user.profileType == 'azienda' %}                                        
                                                            {% if user.profileType == 'admin' %}        
                                                                <td>
                                                                    {% if procedure.procedure.profileType == 'privato' %}        
                                                                        {{ procedure.user.fullname }} {{ procedure.user.name }} {{ procedure.user.lastname }}<br>
                                                                        <small>{{ procedure.user.email }}</small>
                                                                    {% elseif procedure.procedure.profileType == 'azienda' %}        
                                                                        {{ procedure.user.fullname }}<br>
                                                                        <small>{{ procedure.user.email }}</small>
                                                                    {% endif %}                                                                                                                                                                                            
                                                                </td>      
                                                            {% endif %}
                                                            <td>
                                                                {% if procedure.procedure.profileType == 'privato' %}        
                                                                    <span class="label bg-info">{{ procedure.procedure.profileType }}</span>
                                                                {% elseif procedure.procedure.profileType == 'azienda' %}        
                                                                    <span class="label bg-warning">{{ procedure.procedure.profileType }}</span>
                                                                {% endif %}                                                                                                    
                                                            <td>
                                                                {% if procedure.procedure.profileType == 'privato' %}
                                                                    {{ procedure.procedure.name }} {{ procedure.procedure.lastname }}<br>
                                                                {% elseif procedure.procedure.profileType == 'azienda' %}
                                                                    {{ procedure.procedure.fullname }}<br>
                                                                {% endif %}
                                                                <small>{{ procedure.procedure.email }}</small>
                                                            </td>                                    
                                                            {% if procedure.procedure.ownership == 'owner' %}
                                                                <td>Proprietario o comproprietario</td>
                                                            {% else %}
                                                                <td>Detentore\Utilizzatore</td>
                                                            {% endif %}                             
                                                        {% endif %}
                                                        <td><span sortable-value="{{ procedure.procedure.creation | date('yyyyMMddHHmm') }}">{{ procedure.procedure.creation | date('dd/MM/yyyy HH:mm') }}</span></td>
                                                        <td>                                    
                                                            <div class="text-muted text-size-small">
                                                               {% if procedure.procedure.status == 'opened' %}
                                                                    <div class="text-muted text-size-small"><span class="status-mark bg-info position-left"></span> Ricevuta</div>
                                                               {% elseif procedure.procedure.status == 'processing' %}
                                                                    <div class="text-muted text-size-small"><span class="status-mark bg-orange-300 position-left"></span> In lavorazione</div>
                                                                {% elseif procedure.procedure.status == 'approved' %}
                                                                    <div class="text-muted text-size-small"><span class="status-mark bg-success position-left"></span> Approvata</div>                                       
                                                                {% elseif procedure.procedure.status == 'annulled' %}
                                                                    <div class="text-muted text-size-small"><span class="status-mark bg-danger position-left"></span> Annullata</div>                                       
                                                               {% endif %}
                                                            </div>
                                                        </td>
                                                        {% if user.profileType == 'admin' %}    
                                                            <td class="text-center">
                                                                {% if procedure.procedure.status != 'annulled' %}
                                                                    <ul class="icons-list">
                                                                        <li class="dropdown">
                                                                            <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                                                                <i class="icon-menu9"></i>
                                                                            </a>
                                                                            <ul class="dropdown-menu dropdown-menu-right">                                                
                                                                                {% if procedure.procedure.status == 'opened' %}
                                                                                    <li><a href="{{ paths('PROCEDURE_STATUS_UPDATE') }}?procedureId={{ procedure.procedure.id }}&status=processing" class="procedure-update"><span class="status-mark bg-orange-300 position-left"></span> In lavorazione</a></li>
                                                                                    <li><a href="{{ paths('PROCEDURE_STATUS_UPDATE') }}?procedureId={{ procedure.procedure.id }}&status=approved" class="procedure-update"><span class="status-mark bg-success position-left"></span> Approvata</a></li>
                                                                                {% endif %}
                                                                                {% if procedure.procedure.status == 'processing' %}
                                                                                    <li><a href="{{ paths('PROCEDURE_STATUS_UPDATE') }}?procedureId={{ procedure.procedure.id }}&status=approved" class="procedure-update"><span class="status-mark bg-success position-left"></span> Approvata</a></li>
                                                                                    <li><a href="{{ paths('PROCEDURE_STATUS_UPDATE') }}?procedureId={{ procedure.procedure.id }}&status=annulled" class="procedure-update"><span class="status-mark bg-danger position-left"></span> Annulla</a></li>
                                                                                {% endif %}                                                
                                                                            </ul>
                                                                        </li>
                                                                    </ul>
                                                                {% endif %}
                                                            </td>
                                                        {% endif %}
                                                        <td></td>
                                                    </tr>
                                                {% endfor %}
                                            {% endif %}
                                        </tbody>
                                    </table>
                                </div>
                                <!-- /state saving -->
                            </div>
                        </div>
                    </div>
                </div>                

            </div>
            <!-- /user profile -->

        </div>
        <!-- /main content -->

    </div>
    <!-- /page content -->
    <script>
        $(function () {
            $('.btn-cancel').click(function () {
                $.confirm({
                    theme: 'supervan',
                    escapeKey: true,
                    animation: 'top',
                    closeAnimation: 'bottom',
                    backgroundDismiss: true,
                    title: '<i class="icon-cancel-square icon-2x"></i><br/><br/> Sei sicuro?',
                    content: "Le modifiche fatte fin'ora verranno perse.",
                    buttons: {
                        annulla: {
                            btnClass: 'btn-default',
                            action: function () {
                                //
                            }
                        },
                        conferma: {
                            btnClass: 'bg-success',
                            action: function () {
                                window.location.href = "{{ paths('PROFILE') }}";
                            }
                        }
                    }
                });
            });
        });
    </script>
{% endblock %}
