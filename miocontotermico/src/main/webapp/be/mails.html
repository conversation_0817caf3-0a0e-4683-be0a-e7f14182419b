{% extends "be/include/base.html" %}

{% block extrahead %}
    <title>Template mail</title>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jasny_bootstrap.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/uniform.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/datatables.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/responsive.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/buttons.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/selects/select2.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/app.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/ui/ripple.min.js"></script>
    <script src="https://siteria.it/libs/pdfmake/0.1.34/pdfmake.min.js"></script>
    <script src="https://siteria.it/libs/pdfmake/0.1.34/vfs_fonts.js"></script>
    <script src="https://siteria.it/libs/b-lazy/1.8.2/blazy.min.js"></script>
    <script src="{{ contextPath }}/be/js/pages/mails.js?{{ buildNumber }}"></script>
{% endblock %}

{% block content %}

<!-- Page content -->
<div class="page-content">

    <a id="usersSuccessUri" class="no-display" href="{{ paths('MAILS') }}/success" rel="nofollow"></a>

    <!-- Main content -->
    <div class="content-wrapper">
        {% if success %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-success">
                    <a href="{{ paths('MAILS') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Dati memorizzati correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-danger">
                    <a href="{{ paths('MAILS') }}" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- State saving -->
        <div class="panel panel-white">
            <div class="panel-heading">
                <h5 class="panel-title text-bold">Mail</h5>
            </div>

            <table class="table datatable-mails">
                <thead>
                    <tr>
                        <th>Template</th>
                    </tr>
                </thead>
                <tbody>
                    {% if mailList is not empty %}
                        {% for mailTmp in mailList %}
                                <tr>                                
                                    <td>
                                        <a href="{{ paths('MAIL_DETAIL') }}?mailId={{ mailTmp.id }}" class="text-bold text-uppercase" target="_blank">
                                            {{ mailTmp.template }}
                                        </a>
                                    </td>
                                    <td></td>
                                </tr>
                        {% endfor %}
                    {% endif %}
                </tbody>
            </table>
        </div>
        <!-- /state saving -->

    </div>
    <!-- /main content -->
</div>
<!-- /page content -->
{% endblock %}
