{% extends "be/include/base.html" %}

{% block extrahead %}
    <title><PERSON><PERSON><PERSON> sconto in fattura</title>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/ui/moment/moment_locales.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/libraries/jasny_bootstrap.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/styling/uniform.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/2.1/global_assets/js/plugins/pickers/daterangepicker.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/datatables.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/responsive.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/tables/datatables/extensions/buttons.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/forms/selects/select2.min.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/core/app.js"></script>
    <script src="https://siteria.it/tpls/be/1/1.6/assets/js/plugins/ui/ripple.min.js"></script>
    <script src="https://siteria.it/libs/pdfmake/0.1.34/pdfmake.min.js"></script>
    <script src="https://siteria.it/libs/pdfmake/0.1.34/vfs_fonts.js"></script>
    <script src="https://siteria.it/libs/b-lazy/1.8.2/blazy.min.js"></script>
    <script src="{{ contextPath }}/be/js/pages/invoicediscounts.js?{{ buildNumber }}"></script>
{% endblock %}

{% block sidebarcontrol %}
<ul class="fab-menu fab-menu-top-left hidden-xs" data-fab-toggle="click">
    <li>
        <a class="fab-menu-btn btn btn-default btn-float btn-rounded btn-icon sidebar-control sidebar-main-hide">
            <i class="icon-paragraph-justify3"></i>
        </a>
    </li>
</ul>
{% endblock %}

{% block content %}
<!-- js deposit -->
<div id="startDate" style="display: none">{{ startDate | date("yyyy-MM-dd") }}</div>
<div id="endDate" style="display: none">{{ endDate | date("yyyy-MM-dd") }}</div>

<!-- Page content -->
<div class="page-content">

    <a id="invoicediscountsSuccessUri" class="no-display" href="{{ paths('INVOICEDISCOUNTS') }}/success?selectedStatuses=opened%7Cprocessing%7C" rel="nofollow"></a>    

<!-- Main sidebar -->
    <div class="sidebar sidebar-main sidebar-default sidebar-separate">
        <div class="sidebar-content">
            <!-- Filter -->
            <div class="sidebar-category">
                <div class="category-title">
                    <span>Filtra valutazioni</span>
                    <ul class="icons-list">
                        <li><a href="#" data-action="collapse"></a></li>
                    </ul>
                </div>
                <div class="category-content">
                    <form id="filter-form" method="GET" action="{{ paths('INVOICEDISCOUNTS') }}">
                        {% if statusList is not empty %}
                            <div class="form-group">
                                <legend class="text-size-mini text-muted no-border no-padding">Stato pratica</legend>
                                <div>
                                    {% for status in statusList %}
                                        <div class="checkbox">
                                            <label class="display-block text-capitalize">
                                                <input type="checkbox" class="styled man-status-filter" man-status-value="{{ status.status }}" {{ selectedStatuses contains status.status ? 'checked' : ''}}>
                                                {% if status.status == 'opened' %}
                                                    <span class="status-mark bg-info position-left"></span>
                                                    Ricevuta
                                                {% endif %}
                                                {% if status.status == 'processing' %}
                                                    <span class="status-mark bg-orange-300 position-left"></span>
                                                    In lavorazione
                                                {% endif %}
                                                {% if status.status == 'approved' %}
                                                    <span class="status-mark bg-success position-left"></span>
                                                    Approvata
                                                {% endif %}
                                                {% if status.status == 'annulled' %}
                                                    <span class="status-mark bg-danger position-left"></span>
                                                    Annullata
                                                {% endif %}
                                                
                                            </label>
                                        </div>
                                    {% endfor %}
                                </div>
                            </div>
                        {% endif %}
                        <button type="submit" class="btn bg-success btn-block">Filtra</button>
                    </form>
                </div>
            </div>
            <!-- /filter -->
        </div>
    </div>
    <!-- /main sidebar -->
    
    <!-- Main content -->
    <div class="content-wrapper">
        {% if success %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-success">
                    <a href="{{ paths('INVOICEDISCOUNTS') }}?selectedStatuses=opened%7Cprocessing%7C" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Dati memorizzati correttamente</span>
                </div>
            </div>
        </div>
        {% elseif error %}
        <div class="row">
            <div class="col-md-12">
                <div class="alert alert-danger">
                    <a href="{{ paths('INVOICEDISCOUNTS') }}?selectedStatuses=opened%7Cprocessing%7C" class="close" aria-label="Chiudi"><span aria-hidden="true">&times;</span></a>
                    <span>Errore durante il salvataggio dei dati. Serve aiuto? <a href="mailto:<EMAIL>">Contatta l'assistenza</a></span>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- State saving -->
        <div class="panel panel-white">
            <div class="panel-heading has-visible-elements">
                <h5 class="panel-title text-bold">Pratiche sconto in fattura</h5>
                <div class="heading-elements visible-elements">                                                
                    <div class="btn-group heading-btn">

                    </div>                        
                    <button type="button" class="btn heading-btn btn-default daterange-predefined">
                        <i class="icon-calendar22 position-left"></i>
                        <span></span>
                        <b class="caret"></b>
                    </button>
                </div>
            </div>

            <table class="table datatable-invoicediscounts">
                <thead>
                    <tr>
                        <th>Riferimento</th>
                        {% if user.profileType == 'admin' or user.profileType == 'operatore' %}    
                            <th>Caricato da/Email registrazione</th>
                        {% endif %}
                        {% if user.profileType == 'admin' or user.profileType == 'azienda' or user.profileType == 'operatore' %}                                                            
                            <th>Tipo</th>                                                        
                            <th>Soggetto pratica</th>                                                        
                        {% else %}
                            <th>Soggetto pratica</th>
                        {% endif %}
                        <th>Data</th>
                        <th>Stato</th>                        
                        {% if user.profileType == 'admin' or user.profileType == 'operatore' %}    
                            <th class="text-center">Azioni</th>
                        {% endif %}
                        <th></th>
                    </tr>
                </thead>
                <tbody>
                    {% if invoicediscountList is not empty %}
                        {% for invoicediscount in invoicediscountList %}
                            <tr>                                
                                <td>
                                    <a href="{{ paths('INVOICEDISCOUNT_EDIT') }}?invoicediscountId={{ invoicediscount.invoicediscount.id }}" class="text-bold text-uppercase" target="_blank"><span sortable-value="{{ invoicediscount.invoicediscount.protocol }}">Pratica #{{ invoicediscount.invoicediscount.protocol }}</span></a><br>
                                    <small>assegnata a {{ decode('user', invoicediscount.invoicediscount.assignedUserId ) }}</small>
                                </td>
                                {% if user.profileType == 'admin' or user.profileType == 'azienda' or user.profileType == 'operatore' %}                                        
                                    {% if user.profileType == 'admin' or user.profileType == 'operatore' %}        
                                        <td>
                                            {% if invoicediscount.invoicediscount.profileType == 'privato' %}        
                                                {{ invoicediscount.user.fullname }} {{ invoicediscount.user.name }} {{ invoicediscount.user.lastname }}<br>
                                                <small>{{ invoicediscount.user.email }}</small>
                                            {% elseif invoicediscount.invoicediscount.profileType == 'azienda' %}        
                                                {{ invoicediscount.user.fullname }}<br>
                                                <small>{{ invoicediscount.user.email }}</small>
                                            {% endif %}                                                                                                                                                                                            
                                        </td>      
                                    {% endif %}
                                    <td>
                                        {% if invoicediscount.invoicediscount.profileType == 'privato' %}        
                                            <span class="label bg-info">{{ invoicediscount.invoicediscount.profileType }}</span>
                                        {% elseif invoicediscount.invoicediscount.profileType == 'azienda' %}        
                                            <span class="label bg-warning">{{ invoicediscount.invoicediscount.profileType }}</span>
                                        {% endif %}                                                                                                    
                                    <td>
                                        {% if invoicediscount.invoicediscount.profileType == 'privato' %}
                                            {{ invoicediscount.invoicediscount.name }} {{ invoicediscount.invoicediscount.lastname }}<br>
                                        {% elseif invoicediscount.invoicediscount.profileType == 'azienda' %}
                                            {{ invoicediscount.invoicediscount.fullname }}<br>
                                        {% endif %}
                                        <small>{{ invoicediscount.invoicediscount.email }}</small>
                                    </td>                                    
                                {% else %}
                                    <td>
                                        {% if invoicediscount.invoicediscount.profileType == 'privato' %}
                                            {{ invoicediscount.invoicediscount.name }} {{ invoicediscount.invoicediscount.lastname }}
                                        {% elseif invoicediscount.invoicediscount.profileType == 'azienda' %}
                                            {{ invoicediscount.invoicediscount.fullname }}
                                        {% endif %}
                                    </td>
                                {% endif %}
                                <td><span sortable-value="{{ invoicediscount.invoicediscount.date | date('yyyyMMddHHmm') }}">{{ invoicediscount.invoicediscount.date | date('dd/MM/yyyy HH:mm') }}</span></td>
                                <td>                                    
                                    <div class="text-muted text-size-small">
                                       {% if invoicediscount.invoicediscount.status == 'opened' %}
                                            <div class="text-muted text-size-small"><span class="status-mark bg-info position-left"></span> Ricevuta</div>
                                       {% elseif invoicediscount.invoicediscount.status == 'processing' %}
                                            <div class="text-muted text-size-small"><span class="status-mark bg-orange-300 position-left"></span> In lavorazione</div>
                                        {% elseif invoicediscount.invoicediscount.status == 'approved' %}
                                            <div class="text-muted text-size-small"><span class="status-mark bg-success position-left"></span> Approvata</div>                                       
                                        {% elseif invoicediscount.invoicediscount.status == 'annulled' %}
                                            <div class="text-muted text-size-small"><span class="status-mark bg-danger position-left"></span> Annullata</div>                                       
                                       {% endif %}
                                    </div>
                                </td>
                                {% if user.profileType == 'admin' or user.profileType == 'operatore' %}    
                                    <td class="text-center">
                                        {% if invoicediscount.invoicediscount.status != 'annulled' %}
                                            <ul class="icons-list">
                                                <li class="dropdown">
                                                    <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                                                        <i class="icon-menu9"></i>
                                                    </a>
                                                    <ul class="dropdown-menu dropdown-menu-right">                                                
                                                        {% if invoicediscount.invoicediscount.status == 'opened' %}
                                                            <li><a href="{{ paths('INVOICEDISCOUNT_STATUS_UPDATE') }}?invoicediscountId={{ invoicediscount.invoicediscount.id }}&status=processing" class="invoicediscount-update"><span class="status-mark bg-orange-300 position-left"></span> In lavorazione</a></li>
                                                            <li><a href="{{ paths('INVOICEDISCOUNT_STATUS_UPDATE') }}?invoicediscountId={{ invoicediscount.invoicediscount.id }}&status=approved" class="invoicediscount-update"><span class="status-mark bg-success position-left"></span> Approvata</a></li>
                                                        {% endif %}
                                                        {% if invoicediscount.invoicediscount.status == 'processing' %}
                                                            <li><a href="{{ paths('INVOICEDISCOUNT_STATUS_UPDATE') }}?invoicediscountId={{ invoicediscount.invoicediscount.id }}&status=approved" class="invoicediscount-update"><span class="status-mark bg-success position-left"></span> Approvata</a></li>
                                                            <li><a href="{{ paths('INVOICEDISCOUNT_STATUS_UPDATE') }}?invoicediscountId={{ invoicediscount.invoicediscount.id }}&status=annulled" class="invoicediscount-update"><span class="status-mark bg-danger position-left"></span> Annulla</a></li>
                                                        {% endif %}                                                
                                                    </ul>
                                                </li>
                                            </ul>
                                        {% endif %}
                                    </td>
                                {% endif %}
                                <td></td>
                            </tr>
                        {% endfor %}
                    {% endif %}
                </tbody>
            </table>
        </div>
        <!-- /state saving -->

    </div>
    <!-- /main content -->
</div>
<!-- /page content -->
{% endblock %}
