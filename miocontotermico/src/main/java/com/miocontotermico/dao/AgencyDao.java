package com.miocontotermico.dao;

import com.miocontotermico.core.Manager;
import com.miocontotermico.pojo.Agency;
import com.miocontotermico.pojo.types.ImageType;
import com.miocontotermico.support.image.slim.SlimImage;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.eq;
import java.security.InvalidParameterException;
import java.util.Date;
import org.bson.Document;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class AgencyDao {
    
    public static Agency loadAgency(ObjectId agencyId) throws Exception {
        if (agencyId == null) {
            throw new InvalidParameterException("empty agencyId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("agency");
        Document doc = collection.find(eq("_id", agencyId)).first();
        return Manager.fromDocument(doc, Agency.class);
    }
    
    public static Agency loadAgencyFirst() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("agency");
        Document doc = collection.find().first();
        return Manager.fromDocument(doc, Agency.class);
    }

    public static ObjectId insertAgency(String orari) {
        
        // defaults
        Date now = new Date();
        
        Agency agency = new Agency();
        agency.setOrari(orari);
        
        // internals
        agency.setCreation(now);
        agency.setLastUpdate(now);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("agency");
        Document doc = Manager.toDocument(agency);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }
    
    public static void updateAgency(Agency agency) throws Exception {
        if (agency == null) {
            throw new InvalidParameterException("empty agency");
        }

        // defaults
        Date now = new Date();
        
        // internals
        agency.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("agency");
        collection.replaceOne(
                new Document("_id", agency.getId()),
                Manager.toDocument(agency)
        );

    }
    
    public static void updateAgencyImage(ObjectId agencyId, SlimImage image) throws Exception {
        if (agencyId == null) {
            throw new InvalidParameterException("empty agencyId");
        }
        if (image == null) {
            throw new InvalidParameterException("empty image");
        }
        if (image.isEmpty()) {
            throw new InvalidParameterException("empty image bytes");
        }
        
        // defaults
        Date now = new Date();
        String filename = ImageDao.composeFilename(ImageType.agency, "miocontotermico", image.getExtension());
        String type = image.getType();
        
        // save image
        ObjectId imageId = ImageDao.insertImage(filename, 
                type, 
                image.getBytes()
        );
        
        // update imageId
        Agency agency = loadAgency(agencyId);
        agency.setImageId(imageId);
        
        // internals
        agency.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("agency");
        collection.replaceOne(
                new Document("_id", agency.getId()),
                Manager.toDocument(agency)
        );
        
    }

    public static void removeAgencyImage(ObjectId agencyId) throws Exception {
        if (agencyId == null) {
            throw new InvalidParameterException("empty agencyId");
        }
        
        // defaults
        Date now = new Date();
        
        // update
        Agency agency = loadAgency(agencyId);
        agency.setImageId(null);
        
        // internals
        agency.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("agency");
        collection.replaceOne(
                new Document("_id", agency.getId()),
                Manager.toDocument(agency)
        );
        
    }    
        
}
