package com.miocontotermico.dao;

import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.regex;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.orderBy;
import com.miocontotermico.core.Manager;
import com.miocontotermico.pojo.City;
import java.util.List;
import java.util.regex.Pattern;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;

/**
 *
 * <AUTHOR>
 */
public class CityDao {
    
    public static List<City> loadCityList(String description) throws Exception {
        description = StringUtils.isNotEmpty(description) ? description : "";
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("city");
        FindIterable<Document> list = collection
                .find(regex("city", Pattern.compile("^"+ description, Pattern.CASE_INSENSITIVE + Pattern.UNICODE_CASE)))
                .sort(orderBy(ascending("city")))
                .limit(20);
        return Manager.fromDocumentList(list, City.class);
    }    
    
}
