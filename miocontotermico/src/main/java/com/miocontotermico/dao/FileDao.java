package com.miocontotermico.dao;

import com.miocontotermico.core.Manager;
import com.miocontotermico.pojo.types.FileType;
import com.miocontotermico.support.file.Filex;
import com.miocontotermico.util.TimeUtils;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.gridfs.GridFSBucket;
import com.mongodb.client.gridfs.GridFSBuckets;
import com.mongodb.client.gridfs.GridFSDownloadStream;
import com.mongodb.client.gridfs.model.GridFSUploadOptions;
import static com.mongodb.client.model.Filters.eq;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.security.InvalidParameterException;
import java.util.Date;
import java.util.UUID;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
    public class FileDao {
 
    public static String composeFilename(FileType type, String extension) throws Exception {
        if (type == null) {
            throw new InvalidParameterException("empty type");
        }
        // extension is optional
        
        // defaults
        Date now = new Date();
        if (StringUtils.isNotBlank(extension)) {
            extension = "." + extension;
        }
        
        return type + "-" + TimeUtils.toString(now, "yyyyMMdd") + "-" + UUID.randomUUID().toString() + (StringUtils.isNotBlank(extension) ? extension : "");
    }

    public static Filex loadFile(ObjectId oid) throws Exception {
        if (oid == null) {
            throw new InvalidParameterException("empty oid");
        }
        
        String filename;
        String originalFilename;
        String contentType;
        int length;
        byte[] bytes;
        
        GridFSBucket bucket = GridFSBuckets.create(Manager.mongoDatabase, "file");
        try (GridFSDownloadStream stream = bucket.openDownloadStream(oid)) {
            filename = stream.getGridFSFile().getFilename();
            contentType = stream.getGridFSFile().getMetadata().getString("contentType");
            originalFilename = stream.getGridFSFile().getMetadata().getString("originalFilename");
            length = (int) stream.getGridFSFile().getLength();
        }
        try (ByteArrayOutputStream out = new ByteArrayOutputStream(length)) {
            bucket.downloadToStream(oid, out);
            bytes = out.toByteArray();
        }
        
        return new Filex(filename, originalFilename, contentType, bytes);
    }
    
    public static Filex loadFileInfo(ObjectId oid) throws Exception {
        if (oid == null) {
            throw new InvalidParameterException("empty oid");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("file.files");
        Document doc = collection.find(eq("_id", oid)).first();

        String filename = doc.getString("filename");
        String originalFilename = null;
        String contentType = null;
        Document metadata = (Document) doc.get("metadata");
        if (metadata != null) {
            originalFilename = metadata.getString("originalFilename");
            contentType = metadata.getString("contentType");
        }
        
        return new Filex(filename, originalFilename, contentType, null);
    }
    
    public static ObjectId insertFile(String filename, String originalFilename, String type, byte[] bytes) throws Exception {
        if (StringUtils.isBlank(filename)) {
            throw new InvalidParameterException("empty filename");
        }
        if (StringUtils.isBlank(originalFilename)) {
            throw new InvalidParameterException("empty originalFilename");
        }
        if (StringUtils.isBlank(type)) {
            throw new InvalidParameterException("empty type");
        }
        if (bytes == null) {
            throw new InvalidParameterException("empty bytes");
        }
        
        GridFSBucket bucket = GridFSBuckets.create(Manager.mongoDatabase, "file");
        
        Document metadata = new Document();
        metadata.put("contentType", type);
        metadata.put("originalFilename", originalFilename);
        
        GridFSUploadOptions options = new GridFSUploadOptions()
                .metadata(metadata)
                ;
        ByteArrayInputStream data = new ByteArrayInputStream(bytes);
        ObjectId oid = bucket.uploadFromStream(filename, data, options);
        IOUtils.closeQuietly(data);
        
        return oid;
    }    
        
}
