package com.miocontotermico.dao;

import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.orderBy;
import com.miocontotermico.core.Manager;
import com.miocontotermico.pojo.Post;
import com.miocontotermico.pojo.types.ImageType;
import com.miocontotermico.support.image.slim.SlimImage;
import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class PostDao {
    
    public static Post loadPost(ObjectId postId) throws Exception {
        if (postId == null) {
            throw new InvalidParameterException("empty postId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("post");
        Document doc = collection.find(eq("_id", postId)).first();
        return Manager.fromDocument(doc, Post.class);
    }    
    
    public static List<Post> loadPostList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("post");
        FindIterable<Document> list = collection
                .find(ne("cancelled", true))
                .sort(orderBy(ascending("creation")));
        return Manager.fromDocumentList(list, Post.class);
    }
    
    public static Post loadPostByIdentifier(String identifier) throws Exception {
        if (identifier == null) {
            throw new InvalidParameterException("empty identifier");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("post");
        Document doc = collection.find(eq("identifier", identifier)).first();
        return Manager.fromDocument(doc, Post.class);
    }     
    public static ObjectId insertPost(Post post) throws Exception {
        if (post == null) {
            throw new InvalidParameterException("empty post");
        }
        
        // defaults
        Date now = new Date();
        
        // internals
        post.setCreation(now);
        post.setLastUpdate(now);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("post");
        Document doc = Manager.toDocument(post);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }    
    
    public static void updatePost(Post post) throws Exception {
        if (post == null) {
            throw new InvalidParameterException("empty post");
        }

        // defaults
        Date now = new Date();
        
        // internals
        post.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("post");
        collection.replaceOne(
                new Document("_id", post.getId()),
                Manager.toDocument(post)
        );
        
    }    
    
    public static void updatePostCancelled(ObjectId postId, boolean cancelled) throws Exception {
        if (postId == null) {
            throw new InvalidParameterException("empty postId");
        }
        
        // defaults
        Date now = new Date();
        
        // update
        Post post = loadPost(postId);
        post.setCancelled(cancelled);
        
        // internals
        post.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("post");
        collection.replaceOne(
                new Document("_id", post.getId()),
                Manager.toDocument(post)
        );
        
    }
    
    public static void updatePostImages(String username, ObjectId postId, List<SlimImage> images) throws Exception {
        if (StringUtils.isBlank(username)) {
            throw new InvalidParameterException("empty username");
        }
        if (postId == null) {
            throw new InvalidParameterException("empty postId");
        }
        if (images == null) {
            throw new InvalidParameterException("empty images");
        }
        if (images.isEmpty()) {
            throw new InvalidParameterException("empty image");
        }
        for (SlimImage image : images) {
            if (image.isEmpty()) {
                throw new InvalidParameterException("empty image bytes");
            }
        }
        
        // defaults
        Date now = new Date();
        
        List<ObjectId> imageIds = new ArrayList<>();
        for (SlimImage image : images) {
            String filename = ImageDao.composeFilename(ImageType.post, username, image.getExtension());
            String type = image.getType();

            // save image
            ObjectId imageId = ImageDao.insertImage(filename, 
                    type, 
                    image.getBytes()
            );
            
            if (imageId != null) {
                imageIds.add(imageId);
            }
        }
        if (imageIds.isEmpty()) {
            imageIds = null;
        }
        
        // update imageId
        Post post = loadPost(postId);
        post.setImageIds(imageIds);
        
        // internals
        post.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("post");
        collection.replaceOne(
                new Document("_id", post.getId()),
                Manager.toDocument(post)
        );
        
    }
    
    public static void removePostImages(ObjectId postId) throws Exception {
        if (postId == null) {
            throw new InvalidParameterException("empty postId");
        }
        
        // defaults
        Date now = new Date();
        
        // update
        Post post = loadPost(postId);
        post.setImageIds(null);
        
        // internals
        post.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("post");
        collection.replaceOne(
                new Document("_id", post.getId()),
                Manager.toDocument(post)
        );
        
    }        
}
