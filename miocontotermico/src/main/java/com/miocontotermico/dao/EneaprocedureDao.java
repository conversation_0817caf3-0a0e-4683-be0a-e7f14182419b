package com.miocontotermico.dao;

import com.miocontotermico.core.Manager;
import com.miocontotermico.pojo.Eneaprocedure;
import com.miocontotermico.pojo.types.StatusType;
import com.miocontotermico.property.StatusEntry;
import com.miocontotermico.util.MongoUtils;
import com.miocontotermico.util.TimeUtils;
import com.mongodb.client.AggregateIterable;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Accumulators.min;
import static com.mongodb.client.model.Accumulators.sum;
import static com.mongodb.client.model.Aggregates.match;
import static com.mongodb.client.model.Aggregates.project;
import static com.mongodb.client.model.Aggregates.sort;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.gte;
import static com.mongodb.client.model.Filters.lte;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Projections.excludeId;
import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import static com.mongodb.client.model.Aggregates.group;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.in;
import static com.mongodb.client.model.Projections.fields;
import static com.mongodb.client.model.Projections.include;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.orderBy;

/**
 *
 * <AUTHOR>
 */
public class EneaprocedureDao {

    public static Eneaprocedure loadEneaprocedure(ObjectId eneaprocedureId) throws Exception {
        if (eneaprocedureId == null) {
            throw new InvalidParameterException("empty eneaprocedureId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("eneaprocedure");
        Document doc = collection.find(eq("_id", eneaprocedureId)).first();
        return Manager.fromDocument(doc, Eneaprocedure.class);
    }

    public static Eneaprocedure loadDraftEneaprocedure(ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("eneaprocedure");
        Document doc = collection.find(
                and(
                        ne("cancelled", true),
                        eq("status", StatusType.draft.toString()),
                        eq("userId", userId)))
                .first();
        return Manager.fromDocument(doc, Eneaprocedure.class);
    }

    public static List<Eneaprocedure> loadEneaprocedureListByUserId(ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }

        List<Bson> filters = new ArrayList<>();
        
        filters.add(ne("cancelled", true));

        filters.add(eq("userId", userId));
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("eneaprocedure");
        FindIterable<Document> list = collection
                .find(and(filters));
        
        return Manager.fromDocumentList(list, Eneaprocedure.class);
    }
    
    public static List<Eneaprocedure> loadEneaprocedureListByDateRangeAndStatus(Date from, Date to, String[] statuses, ObjectId userId, ObjectId assignedUserId) throws Exception {
        
        List<Bson> filters = new ArrayList<>();
        
        filters.add(ne("cancelled", true));
        filters.add(ne("status", StatusType.draft.toString()));
     
        if (from != null) {
            from = MongoUtils.toComparableDate(TimeUtils.beginOfDay(from));
            filters.add(gte("date", from));
        }
        
        if (to != null) {
            to = MongoUtils.toComparableDate(TimeUtils.endOfDay(to));
            filters.add(lte("date", to));
        }
        
        if (statuses != null) {
            filters.add(in("status", statuses));
        }
        
        if (userId != null) {
            filters.add(in("userId", userId));
        }
        if (assignedUserId != null) {
            filters.add(in("assignedUserId", assignedUserId));
        }
        
        Bson sort = orderBy(ascending("date"), ascending("_id"));
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("eneaprocedure");
        FindIterable<Document> list = collection
                .find(and(filters))
                .limit(10000)
                .sort(sort);
        
        return Manager.fromDocumentList(list, Eneaprocedure.class);
    }
    
    public static ObjectId insertEneaprocedure(Eneaprocedure eneaprocedure) throws Exception {
        if (eneaprocedure == null) {
            throw new InvalidParameterException("empty eneaprocedure");
        }
        
        // defaults
        Date now = new Date();
        
        // internals
        eneaprocedure.setCreation(now);
        eneaprocedure.setLastUpdate(now);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("eneaprocedure");
        Document doc = Manager.toDocument(eneaprocedure);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }

    public static void updateEneaprocedure(Eneaprocedure eneaprocedure) throws Exception {
        if (eneaprocedure == null) {
            throw new InvalidParameterException("empty eneaprocedure");
        }

        // defaults
        Date now = new Date();
        
        // internals
        eneaprocedure.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("eneaprocedure");
        collection.replaceOne(
                new Document("_id", eneaprocedure.getId()),
                Manager.toDocument(eneaprocedure)
        );
        
    }

    public static void updateEneaprocedureCancelled(ObjectId eneaprocedureId, boolean cancelled) throws Exception {
        if (eneaprocedureId == null) {
            throw new InvalidParameterException("empty eneaprocedureId");
        }
        
        // defaults
        Date now = new Date();
        
        // update
        Eneaprocedure eneaprocedure = loadEneaprocedure(eneaprocedureId);
        eneaprocedure.setCancelled(cancelled);
        
        // internals
        eneaprocedure.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("eneaprocedure");
        collection.replaceOne(
                new Document("_id", eneaprocedure.getId()),
                Manager.toDocument(eneaprocedure)
        );
        
    }

    public static void removeDraftEneaprocedure(ObjectId eneaprocedureId) throws Exception {
        if (eneaprocedureId == null) {
            throw new InvalidParameterException("empty eneaprocedureId");
        }
        
        // defaults
        Date now = new Date();
        
        // update
        Eneaprocedure eneaprocedure = loadEneaprocedure(eneaprocedureId);
        eneaprocedure.setCancelled(true);
        
        // internals
        eneaprocedure.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("eneaprocedure");
        collection.replaceOne(
                new Document("_id", eneaprocedure.getId()),
                Manager.toDocument(eneaprocedure)
        );
    }
    
    public static List<StatusEntry> loadEneaprocedureStatusList() throws Exception {
        /*
            db.getCollection('eneaprocedure').aggregate(
                [
                    {
                        $match: {
                            status: {$ne: '', $ne: null}
                        }
                    },
                    {
                        $group: {
                            _id : '$status',
                            status: { $min: '$status'},
                            count: { $sum: 1 }
                        }
                    },
                    { $project: { _id: 0, status: 1}},
                    { $sort : { stato : 1 } }
                ]
            )
        */
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("eneaprocedure");
        AggregateIterable<Document> list = collection
                .aggregate(Arrays.asList(
                    match(and(
                            ne("status", ""),
                            ne("status", "draft"),
                            ne("status", null)
                    )),
                    group("$status", min("status", "$status"), sum("count", "1")),
                    project(fields(excludeId(), include("status"))),
                    sort(orderBy(ascending("status")))
                ));
        return Manager.fromAggregateList(list, StatusEntry.class);
    }
}
