package com.miocontotermico.dao;

import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.eq;
import com.mongodb.client.model.FindOneAndUpdateOptions;
import com.mongodb.client.model.ReturnDocument;
import static com.mongodb.client.model.Updates.inc;
import com.miocontotermico.core.Manager;
import java.security.InvalidParameterException;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;

/**
 *
 * <AUTHOR>
 */
public class CounterDao {

    public static int next(String code) throws Exception {
        if (StringUtils.isBlank(code)) {
            throw new InvalidParameterException("empty code");
        }
        
        FindOneAndUpdateOptions options = new FindOneAndUpdateOptions()
                .upsert(true)
                .returnDocument(ReturnDocument.AFTER)
                ;

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("counter");
        Document doc = collection.findOneAndUpdate(eq("code", code), inc("seq", 1), options);
        
        // https://docs.mongodb.com/v3.0/tutorial/create-an-auto-incrementing-field/
        // https://stackoverflow.com/questions/36368136/find-and-upsert-using-the-java-driver
        Integer seq = doc.get("seq", Integer.class);
        
        if ((seq == null) || (seq < 1)) {
            throw new Exception("invalid sequence");
        }
        
        return seq;
    }    
    
}
