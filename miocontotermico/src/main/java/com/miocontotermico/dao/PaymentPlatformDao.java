package com.miocontotermico.dao;

import com.mongodb.client.MongoCollection;
import com.miocontotermico.core.Manager;
import com.miocontotermico.pojo.PaymentPlatform;
import com.mongodb.client.FindIterable;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.orderBy;
import java.security.InvalidParameterException;
import java.util.Date;
import java.util.List;
import org.bson.Document;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class PaymentPlatformDao {
    
    public static List<PaymentPlatform> loadPaymentPlatformList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("payment_platform");
        FindIterable<Document> list = collection
                .find(ne("cancelled", true))
                .sort(orderBy(ascending("paymentType")));
        return Manager.fromDocumentList(list, PaymentPlatform.class);
    }
    
    public static PaymentPlatform loadPaymentPlatformByPaymentType(String paymentType) throws Exception {
        if (paymentType == null) {
            throw new InvalidParameterException("empty paymentType");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("payment_platform");
        Document doc = collection.find(eq("paymentType", paymentType)).first();
        return Manager.fromDocument(doc, PaymentPlatform.class);
    }
    
    public static PaymentPlatform loadPaymentPlatform(ObjectId paymentPlatformId) throws Exception {
        if (paymentPlatformId == null) {
            throw new InvalidParameterException("empty paymentPlatformId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("payment_platform");
        Document doc = collection.find(eq("_id", paymentPlatformId)).first();
        return Manager.fromDocument(doc, PaymentPlatform.class);
        
    }
    
    public static ObjectId insertPaymentPlatform(PaymentPlatform paymentPlatform) throws Exception {
        if (paymentPlatform == null) {
            throw new InvalidParameterException("empty paymentPlatform");
        }
        
        // defaults
        Date now = new Date();
        
        // internals
        paymentPlatform.setCreation(now);
        paymentPlatform.setLastUpdate(now);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("payment_platform");
        Document doc = Manager.toDocument(paymentPlatform);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }        

    public static void updatePaymentPlatform(PaymentPlatform paymentPlatform) throws Exception {
        if (paymentPlatform == null) {
            throw new InvalidParameterException("empty paymentPlatform");
        }

        // defaults
        Date now = new Date();
        
        // internals
        paymentPlatform.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("payment_platform");
        collection.replaceOne(
                new Document("_id", paymentPlatform.getId()),
                Manager.toDocument(paymentPlatform)
        );

    }

    public static void updatePaymentPlatformCancelled(ObjectId paymentPlatformId, boolean cancelled) throws Exception {
        if (paymentPlatformId == null) {
            throw new InvalidParameterException("empty paymentPlatformId");
        }
        
        // defaults
        Date now = new Date();
        
        // update
        PaymentPlatform paymentPlatform = loadPaymentPlatform(paymentPlatformId);
        paymentPlatform.setCancelled(cancelled);
        
        // internals
        paymentPlatform.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("payment_platform");
        collection.replaceOne(
                new Document("_id", paymentPlatform.getId()),
                Manager.toDocument(paymentPlatform)
        );
        
    }
    
}
