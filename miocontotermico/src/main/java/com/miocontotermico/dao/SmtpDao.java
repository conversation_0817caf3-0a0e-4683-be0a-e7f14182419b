package com.miocontotermico.dao;

import com.mongodb.client.MongoCollection;
import com.miocontotermico.core.Manager;
import com.miocontotermico.pojo.Smtp;
import java.security.InvalidParameterException;
import java.util.Date;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class SmtpDao {
    
    public static Smtp loadSmtp() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("smtp");
        Document doc = collection.find().first();
        
        Smtp smtp = null;
        if (doc != null) {
            smtp = Manager.fromDocument(doc, Smtp.class);
        }
        return smtp;
    }
    
    public static ObjectId insertSmtp(Smtp smtp) throws Exception {
        if (smtp == null) {
            throw new InvalidParameterException("empty smtp");
        }
        
        // defaults
        Date now = new Date();
        
        // internals
        smtp.setCreation(now);
        smtp.setLastUpdate(now);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("smtp");
        Document doc = Manager.toDocument(smtp);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }        

    public static void updateSmtp(Smtp smtp) throws Exception {
        if (smtp == null) {
            throw new InvalidParameterException("empty smtp");
        }

        // defaults
        Date now = new Date();
        
        // internals
        smtp.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("smtp");
        collection.replaceOne(
                new Document("_id", smtp.getId()),
                Manager.toDocument(smtp)
        );

    }
    
    public static void updateSmtpPassword(String password) throws Exception {
        if (StringUtils.isBlank(password)) {
            throw new InvalidParameterException("empty password");
        }

        // defaults
        Date now = new Date();
        
        // update
        Smtp smtp = loadSmtp();
        smtp.setPassword(password);
        
        // internals
        smtp.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("smtp");
        collection.replaceOne(
                new Document("_id", smtp.getId()),
                Manager.toDocument(smtp)
        );
        
    }    
}
