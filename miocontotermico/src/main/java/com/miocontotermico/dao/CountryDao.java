package com.miocontotermico.dao;

import com.miocontotermico.core.Manager;
import com.miocontotermico.pojo.Country;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.regex;
import java.security.InvalidParameterException;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.orderBy;
import java.util.regex.Pattern;

/**
 *
 * <AUTHOR>
 */
public class CountryDao {

    public static List<Country> loadCountryList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("country");
        FindIterable<Document> list = collection
                .find()
                .sort(orderBy(ascending("code")));
        return Manager.fromDocumentList(list, Country.class);
    }
    
    public static Country loadCountry(String code) throws Exception {
        if (StringUtils.isBlank(code)) {
            throw new InvalidParameterException("empty code");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("country");
        Document doc = collection.find(regex("code", Pattern.compile(code, Pattern.CASE_INSENSITIVE + Pattern.UNICODE_CASE))).first();
        return Manager.fromDocument(doc, Country.class);
    }

}
