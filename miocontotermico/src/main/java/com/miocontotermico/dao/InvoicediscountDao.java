package com.miocontotermico.dao;

import com.miocontotermico.core.Manager;
import com.miocontotermico.pojo.Invoicediscount;
import com.miocontotermico.pojo.types.StatusType;
import com.miocontotermico.property.StatusEntry;
import com.miocontotermico.util.MongoUtils;
import com.miocontotermico.util.TimeUtils;
import com.mongodb.client.AggregateIterable;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Accumulators.min;
import static com.mongodb.client.model.Accumulators.sum;
import static com.mongodb.client.model.Aggregates.match;
import static com.mongodb.client.model.Aggregates.project;
import static com.mongodb.client.model.Aggregates.sort;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.gte;
import static com.mongodb.client.model.Filters.lte;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Projections.excludeId;
import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import static com.mongodb.client.model.Aggregates.group;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.in;
import static com.mongodb.client.model.Projections.fields;
import static com.mongodb.client.model.Projections.include;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.orderBy;

/**
 *
 * <AUTHOR>
 */
public class InvoicediscountDao {

    public static Invoicediscount loadInvoicediscount(ObjectId invoicediscountId) throws Exception {
        if (invoicediscountId == null) {
            throw new InvalidParameterException("empty invoicediscountId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("invoicediscount");
        Document doc = collection.find(eq("_id", invoicediscountId)).first();
        return Manager.fromDocument(doc, Invoicediscount.class);
    }

    public static Invoicediscount loadDraftInvoicediscount(ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("invoicediscount");
        Document doc = collection.find(
                and(
                        ne("cancelled", true),
                        eq("status", StatusType.draft.toString()),
                        eq("userId", userId)))
                .first();
        return Manager.fromDocument(doc, Invoicediscount.class);
    }

    public static List<Invoicediscount> loadInvoicediscountListByUserId(ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }

        List<Bson> filters = new ArrayList<>();
        
        filters.add(ne("cancelled", true));

        filters.add(eq("userId", userId));
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("invoicediscount");
        FindIterable<Document> list = collection
                .find(and(filters));
        
        return Manager.fromDocumentList(list, Invoicediscount.class);
    }
    
    public static List<Invoicediscount> loadInvoicediscountListByDateRangeAndStatus(Date from, Date to, String[] statuses, ObjectId userId, ObjectId assignedUserId) throws Exception {
        
        List<Bson> filters = new ArrayList<>();
        
        filters.add(ne("cancelled", true));
        filters.add(ne("status", StatusType.draft.toString()));
     
        if (from != null) {
            from = MongoUtils.toComparableDate(TimeUtils.beginOfDay(from));
            filters.add(gte("date", from));
        }
        
        if (to != null) {
            to = MongoUtils.toComparableDate(TimeUtils.endOfDay(to));
            filters.add(lte("date", to));
        }
        
        if (statuses != null) {
            filters.add(in("status", statuses));
        }
        
        if (userId != null) {
            filters.add(in("userId", userId));
        }
        if (assignedUserId != null) {
            filters.add(in("assignedUserId", assignedUserId));
        }
        
        Bson sort = orderBy(ascending("date"), ascending("_id"));
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("invoicediscount");
        FindIterable<Document> list = collection
                .find(and(filters))
                .limit(10000)
                .sort(sort);
        
        return Manager.fromDocumentList(list, Invoicediscount.class);
    }
    
    public static ObjectId insertInvoicediscount(Invoicediscount invoicediscount) throws Exception {
        if (invoicediscount == null) {
            throw new InvalidParameterException("empty invoicediscount");
        }
        
        // defaults
        Date now = new Date();
        
        // internals
        invoicediscount.setCreation(now);
        invoicediscount.setLastUpdate(now);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("invoicediscount");
        Document doc = Manager.toDocument(invoicediscount);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }

    public static void updateInvoicediscount(Invoicediscount invoicediscount) throws Exception {
        if (invoicediscount == null) {
            throw new InvalidParameterException("empty invoicediscount");
        }

        // defaults
        Date now = new Date();
        
        // internals
        invoicediscount.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("invoicediscount");
        collection.replaceOne(
                new Document("_id", invoicediscount.getId()),
                Manager.toDocument(invoicediscount)
        );
        
    }

    public static void updateInvoicediscountCancelled(ObjectId invoicediscountId, boolean cancelled) throws Exception {
        if (invoicediscountId == null) {
            throw new InvalidParameterException("empty invoicediscountId");
        }
        
        // defaults
        Date now = new Date();
        
        // update
        Invoicediscount invoicediscount = loadInvoicediscount(invoicediscountId);
        invoicediscount.setCancelled(cancelled);
        
        // internals
        invoicediscount.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("invoicediscount");
        collection.replaceOne(
                new Document("_id", invoicediscount.getId()),
                Manager.toDocument(invoicediscount)
        );
        
    }

    public static void removeDraftInvoicediscount(ObjectId invoicediscountId) throws Exception {
        if (invoicediscountId == null) {
            throw new InvalidParameterException("empty invoicediscountId");
        }
        
        // defaults
        Date now = new Date();
        
        // update
        Invoicediscount invoicediscount = loadInvoicediscount(invoicediscountId);
        invoicediscount.setCancelled(true);
        
        // internals
        invoicediscount.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("invoicediscount");
        collection.replaceOne(
                new Document("_id", invoicediscount.getId()),
                Manager.toDocument(invoicediscount)
        );
    }
    
    public static List<StatusEntry> loadInvoicediscountStatusList() throws Exception {
        /*
            db.getCollection('invoicediscount').aggregate(
                [
                    {
                        $match: {
                            status: {$ne: '', $ne: null}
                        }
                    },
                    {
                        $group: {
                            _id : '$status',
                            status: { $min: '$status'},
                            count: { $sum: 1 }
                        }
                    },
                    { $project: { _id: 0, status: 1}},
                    { $sort : { stato : 1 } }
                ]
            )
        */
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("invoicediscount");
        AggregateIterable<Document> list = collection
                .aggregate(Arrays.asList(
                    match(and(
                            ne("status", ""),
                            ne("status", "draft"),
                            ne("status", null)
                    )),
                    group("$status", min("status", "$status"), sum("count", "1")),
                    project(fields(excludeId(), include("status"))),
                    sort(orderBy(ascending("status")))
                ));
        return Manager.fromAggregateList(list, StatusEntry.class);
    }
}
