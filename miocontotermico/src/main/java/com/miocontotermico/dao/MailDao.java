package com.miocontotermico.dao;

import com.mongodb.client.MongoCollection;
import com.miocontotermico.core.Manager;
import com.miocontotermico.pojo.Mail;
import com.miocontotermico.pojo.User;
import com.mongodb.client.FindIterable;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.orderBy;
import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class MailDao {
    
    public static Mail loadMail(ObjectId mailId) throws Exception {
        if (mailId == null) {
            throw new InvalidParameterException("empty mailId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("mail");
        Document doc = collection.find(eq("_id", mailId)).first();
        return Manager.fromDocument(doc, Mail.class);
    }
    
    public static List<Mail> loadMailList() throws Exception {
        
        Bson sort = orderBy(ascending("_id"));
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("mail");
        FindIterable<Document> list = collection
                .find()
                .sort(sort);
        
        return Manager.fromDocumentList(list, Mail.class);
    }
    
    public static Mail loadMailByTemplate(String template) throws Exception {
        if (template == null) {
            throw new InvalidParameterException("empty template");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("mail");
        Document doc = collection.find(eq("template", template)).first();
        return Manager.fromDocument(doc, Mail.class);
    }
    
    public static ObjectId insertMail(Mail mail) throws Exception {
        if (mail == null) {
            throw new InvalidParameterException("empty mail");
        }
        
        // defaults
        Date now = new Date();
        
        // internals
        mail.setCreation(now);
        mail.setLastUpdate(now);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("mail");
        Document doc = Manager.toDocument(mail);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }        

    public static void updateMail(Mail mail) throws Exception {
        if (mail == null) {
            throw new InvalidParameterException("empty mail");
        }

        // defaults
        Date now = new Date();
        
        // internals
        mail.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("mail");
        collection.replaceOne(
                new Document("_id", mail.getId()),
                Manager.toDocument(mail)
        );

    }
    
}
