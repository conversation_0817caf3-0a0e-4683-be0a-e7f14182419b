package com.miocontotermico.dao;

import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.eq;
import com.miocontotermico.core.Manager;
import com.miocontotermico.pojo.User;
import com.miocontotermico.pojo.types.ImageType;
import com.miocontotermico.support.image.slim.SlimImage;
import com.mongodb.client.FindIterable;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Filters.or;
import java.security.InvalidParameterException;
import java.util.Date;
import java.util.UUID;
import java.util.regex.Pattern;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import static com.mongodb.client.model.Filters.regex;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.orderBy;
import java.util.ArrayList;
import java.util.List;
import org.bson.conversions.Bson;

/**
 *
 * <AUTHOR>
 */
public class UserDao {

    public static User loadUser(ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("user");
        Document doc = collection.find(eq("_id", userId)).first();
        
        User user = null;
        if (doc != null) {
            user = Manager.fromDocument(doc, User.class);
        }
        return user;
    }
    
    public static User loadUserByUsername(String username) throws Exception {
        if (StringUtils.isBlank(username)) {
            throw new InvalidParameterException("empty username");
        }
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("user");
        Document doc = collection.find(regex("username", Pattern.compile("(\\s|^)"+ username +"(\\s|$)", Pattern.CASE_INSENSITIVE))).first();
        
        User user = null;
        if (doc != null) {
            user = Manager.fromDocument(doc, User.class);
        }
        return user;
    }

    public static User loadUserByEmail(String email) throws Exception {
        if (StringUtils.isBlank(email)) {
            throw new InvalidParameterException("empty email");
        }
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("user");
        Document doc = collection
                .find(regex("email", Pattern.compile("(\\s|^)"+ email +"(\\s|$)", Pattern.CASE_INSENSITIVE)))
                .first();
        
        User user = null;
        if (doc != null) {
            user = Manager.fromDocument(doc, User.class);
        }
        return user;
    }

    public static List<User> loadUserList() throws Exception {
        
        List<Bson> filters = new ArrayList<>();
        
        filters.add(ne("cancelled", true));
        filters.add(ne("profileType", "system"));
        
        Bson sort = orderBy(ascending("_id"));
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("user");
        FindIterable<Document> list = collection
                .find(and(filters))
                .sort(sort);
        
        return Manager.fromDocumentList(list, User.class);
    }
    
    public static List<User> loadSystemUserList() throws Exception {
        
        List<Bson> filters = new ArrayList<>();
        
        filters.add(ne("cancelled", true));
        filters.add(or(eq("profileType", "admin"),eq("profileType", "operatore")));
        
        Bson sort = orderBy(ascending("_id"));
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("user");
        FindIterable<Document> list = collection
                .find(and(filters))
                .sort(sort);
        
        return Manager.fromDocumentList(list, User.class);
    }
    
    public static ObjectId insertUser(User user) {
        
        if (user == null) {
            throw new InvalidParameterException("empty user");
        }
        if (StringUtils.isBlank(user.getEmail())) {
            throw new InvalidParameterException("empty email");
        }
        if (StringUtils.isBlank(user.getPassword())) {
            throw new InvalidParameterException("empty password");
        }
        if (StringUtils.isBlank(user.getProfileType())) {
            throw new InvalidParameterException("empty profileType");
        }
        
        // defaults
        Date now = new Date();
        
        // internals
        user.setCreation(now);
        user.setLastUpdate(now);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("user");
        Document doc = Manager.toDocument(user);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }

    public static void updateUser(User user) throws Exception {
        if (user == null) {
            throw new InvalidParameterException("empty user");
        }

        // defaults
        Date now = new Date();
        
        // internals
        user.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("user");
        collection.replaceOne(
                new Document("_id", user.getId()),
                Manager.toDocument(user)
        );

    }
    
    public static void updateUserRecovery(ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }
        
        // defaults
        Date now = new Date();
        
        // update
        User user = loadUser(userId);
        user.setRecoverySendDate(now);
        user.setRecoveryDate(null);
        user.setRecoveryToken(UUID.randomUUID().toString());
        user.setRecovered(null);
        user.setPassword(UUID.randomUUID().toString());
        
        // internals
        user.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("user");
        collection.replaceOne(
                new Document("_id", user.getId()),
                Manager.toDocument(user)
        );
        
    }

    public static void removeUserRecovery(ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }
        
        // defaults
        Date now = new Date();
        
        // update
        User user = loadUser(userId);
        user.setRecoveryDate(now);
        user.setRecovered(true);
        
        // internals
        user.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("user");
        collection.replaceOne(
                new Document("_id", user.getId()),
                Manager.toDocument(user)
        );
        
    }

    public static void updateUserPassword(ObjectId userId, String password) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }
        if (StringUtils.isBlank(password)) {
            throw new InvalidParameterException("empty password");
        }

        // defaults
        Date now = new Date();
        
        // update
        User user = loadUser(userId);
        user.setPassword(password);
        
        // internals
        user.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("user");
        collection.replaceOne(
                new Document("_id", user.getId()),
                Manager.toDocument(user)
        );
        
    }

    public static void updateUserImage(String username, ObjectId userId, SlimImage image) throws Exception {
        if (StringUtils.isBlank(username)) {
            throw new InvalidParameterException("empty username");
        }
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }
        if (image == null) {
            throw new InvalidParameterException("empty image");
        }
        if (image.isEmpty()) {
            throw new InvalidParameterException("empty image bytes");
        }
        
        // defaults
        Date now = new Date();
        String filename = ImageDao.composeFilename(ImageType.user, username, image.getExtension());
        String type = image.getType();
        
        // save image
        ObjectId imageId = ImageDao.insertImage(filename, 
                type, 
                image.getBytes()
        );
        
        // update imageId
        User user = loadUser(userId);
        user.setImageId(imageId);
        
        // internals
        user.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("user");
        collection.replaceOne(
                new Document("_id", user.getId()),
                Manager.toDocument(user)
        );
        
    }

    public static void removeUserImage(ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }
        
        // defaults
        Date now = new Date();
        
        // update
        User user = loadUser(userId);
        user.setImageId(null);
        
        // internals
        user.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("user");
        collection.replaceOne(
                new Document("_id", user.getId()),
                Manager.toDocument(user)
        );
        
    }
    
    public static void removeUser(ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("user");
        collection.findOneAndDelete(
                new Document("_id", userId));
        
    }
    
}
