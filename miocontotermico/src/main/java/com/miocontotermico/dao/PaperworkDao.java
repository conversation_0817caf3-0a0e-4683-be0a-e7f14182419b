package com.miocontotermico.dao;

import com.miocontotermico.core.Manager;
import com.miocontotermico.pojo.Paperwork;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.orderBy;
import java.security.InvalidParameterException;
import java.util.Date;
import java.util.List;
import org.bson.Document;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class PaperworkDao {
    
    public static Paperwork loadPaperwork(ObjectId paperworkId) throws Exception {
        if (paperworkId == null) {
            throw new InvalidParameterException("empty paperworkId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("paperwork");
        Document doc = collection.find(eq("_id", paperworkId)).first();
        return Manager.fromDocument(doc, Paperwork.class);
    }    
    
    public static List<Paperwork> loadPaperworkList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("paperwork");
        FindIterable<Document> list = collection
                .find(ne("cancelled", true))
                .sort(orderBy(ascending("creation")));
        return Manager.fromDocumentList(list, Paperwork.class);
    }

    public static List<Paperwork> loadPaperworkListWhereVisible() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("paperwork");
        FindIterable<Document> list = collection
                .find(and(ne("cancelled", true),eq("visible", true)))
                .sort(orderBy(ascending("creation")));
        return Manager.fromDocumentList(list, Paperwork.class);
    }

    public static ObjectId insertPaperwork(Paperwork paperwork) throws Exception {
        if (paperwork == null) {
            throw new InvalidParameterException("empty paperwork");
        }
        
        // defaults
        Date now = new Date();
        
        // internals
        paperwork.setCreation(now);
        paperwork.setLastUpdate(now);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("paperwork");
        Document doc = Manager.toDocument(paperwork);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }    
    
    public static void updatePaperwork(Paperwork paperwork) throws Exception {
        if (paperwork == null) {
            throw new InvalidParameterException("empty paperwork");
        }

        // defaults
        Date now = new Date();
        
        // internals
        paperwork.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("paperwork");
        collection.replaceOne(
                new Document("_id", paperwork.getId()),
                Manager.toDocument(paperwork)
        );
        
    }    
    
    public static void updatePaperworkCancelled(ObjectId paperworkId, boolean cancelled) throws Exception {
        if (paperworkId == null) {
            throw new InvalidParameterException("empty paperworkId");
        }
        
        // defaults
        Date now = new Date();
        
        // update
        Paperwork paperwork = loadPaperwork(paperworkId);
        paperwork.setCancelled(cancelled);
        
        // internals
        paperwork.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("paperwork");
        collection.replaceOne(
                new Document("_id", paperwork.getId()),
                Manager.toDocument(paperwork)
        );
        
    }
}
