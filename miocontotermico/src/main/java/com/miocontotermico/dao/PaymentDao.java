package com.miocontotermico.dao;

import com.miocontotermico.core.Manager;
import com.miocontotermico.pojo.Payment;
import com.miocontotermico.pojo.types.ImageType;
import com.miocontotermico.support.image.slim.SlimImage;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.ne;
import static com.mongodb.client.model.Sorts.ascending;
import static com.mongodb.client.model.Sorts.orderBy;
import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class PaymentDao {

    public static Payment loadPayment(ObjectId paymentId) throws Exception {
        if (paymentId == null) {
            throw new InvalidParameterException("empty paymentId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("payment");
        Document doc = collection.find(eq("_id", paymentId)).first();
        return Manager.fromDocument(doc, Payment.class);
    }

    public static Payment loadPaymentByCode(String code) throws Exception {
        if (code == null) {
            throw new InvalidParameterException("empty code");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("payment");
        Document doc = collection.find(and(ne("cancelled", true), eq("code", code))).first();
        return Manager.fromDocument(doc, Payment.class);
    }
    
    public static Payment loadPaymentByCodeAndChannel(String code, String channel) throws Exception {
        if (code == null) {
            throw new InvalidParameterException("empty code");
        }
        if (channel == null) {
            throw new InvalidParameterException("empty channel");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("payment");
        Document doc = collection.find(and(ne("cancelled", true), eq("code", code), eq("channel", channel))).first();
        return Manager.fromDocument(doc, Payment.class);
    }
    
    public static Payment loadPaymentByName(String name) throws Exception {
        if (name == null) {
            throw new InvalidParameterException("empty name");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("payment");
        Document doc = collection.find(and(ne("cancelled", true), eq("name", name))).first();
        return Manager.fromDocument(doc, Payment.class);
    }
    
    public static Payment loadPaymentByNameEnglish(String nameEnglish) throws Exception {
        if (nameEnglish == null) {
            throw new InvalidParameterException("empty nameEnglish");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("payment");
        Document doc = collection.find(and(ne("cancelled", true), eq("nameEnglish", nameEnglish))).first();
        return Manager.fromDocument(doc, Payment.class);
    }
    
    public static List<Payment> loadPaymentList() throws Exception {
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("payment");
        FindIterable<Document> list = collection
                .find(ne("cancelled", true))
                .sort(orderBy(ascending("name")));
        return Manager.fromDocumentList(list, Payment.class);
    }

    public static ObjectId insertPayment(Payment payment) throws Exception {
        if (payment == null) {
            throw new InvalidParameterException("empty payment");
        }
        
        // defaults
        Date now = new Date();
        
        // internals
        payment.setCreation(now);
        payment.setLastUpdate(now);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("payment");
        Document doc = Manager.toDocument(payment);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }
    
    public static void updatePayment(Payment payment) throws Exception {
        if (payment == null) {
            throw new InvalidParameterException("empty payment");
        }

        // defaults
        Date now = new Date();
        
        // internals
        payment.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("payment");
        collection.replaceOne(
                new Document("_id", payment.getId()),
                Manager.toDocument(payment)
        );
        
    }
    
    public static void updatePaymentShoppable(ObjectId paymentId, Boolean shoppable, Boolean vendorShoppable, Boolean shopDefault, Boolean vendorDefault) throws Exception {
        if (paymentId == null) {
            throw new InvalidParameterException("empty paymentId");
        }
        
        // defaults
        Date now = new Date();
        
        // update
        Payment payment = loadPayment(paymentId);
        
        if (shoppable != null) {
            payment.setShoppable(shoppable);
        }
        if (vendorShoppable != null) {
            payment.setVendorShoppable(vendorShoppable);
        }
        if (shopDefault != null) {
            payment.setShopDefault(shopDefault);
        }
        if (vendorDefault != null) {
            payment.setVendorDefault(vendorDefault);
        }
        
        // internals
        payment.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("payment");
        collection.replaceOne(
                new Document("_id", payment.getId()),
                Manager.toDocument(payment)
        );
        
    }
    
    public static void updatePaymentCancelled(ObjectId paymentId, boolean cancelled) throws Exception {
        if (paymentId == null) {
            throw new InvalidParameterException("empty paymentId");
        }
        
        // defaults
        Date now = new Date();
        
        // update
        Payment payment = loadPayment(paymentId);
        payment.setCancelled(cancelled);
        
        // internals
        payment.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("payment");
        collection.replaceOne(
                new Document("_id", payment.getId()),
                Manager.toDocument(payment)
        );
        
    }

    public static void updatePaymentImage(String username, ObjectId paymentId, SlimImage image) throws Exception {
        if (StringUtils.isBlank(username)) {
            throw new InvalidParameterException("empty username");
        }
        if (paymentId == null) {
            throw new InvalidParameterException("empty paymentId");
        }
        if (image == null) {
            throw new InvalidParameterException("empty image");
        }
        if (image.isEmpty()) {
            throw new InvalidParameterException("empty image bytes");
        }
        
        // defaults
        Date now = new Date();
        String filename = ImageDao.composeFilename(ImageType.payment, username, image.getExtension());
        String type = image.getType();
        
        // save image
        ObjectId imageId = ImageDao.insertImage(filename, 
                type, 
                image.getBytes()
        );
        
        // update imageId
        Payment payment = loadPayment(paymentId);
        if (payment.getImageIds() == null) {
            payment.setImageIds(new ArrayList<>());
        }
        if (payment.getImageIds().isEmpty()) {
            payment.getImageIds().add(new ObjectId());
        }
        payment.getImageIds().set(0, imageId);

        // internals
        payment.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("payment");
        collection.replaceOne(
                new Document("_id", payment.getId()),
                Manager.toDocument(payment)
        );
        
    }
    
    public static void removePaymentImage(ObjectId paymentId) throws Exception {
        if (paymentId == null) {
            throw new InvalidParameterException("empty paymentId");
        }
        
        // defaults
        Date now = new Date();
        
        // update
        Payment payment = loadPayment(paymentId);
        payment.setImageIds(null);
        
        // internals
        payment.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("payment");
        collection.replaceOne(
                new Document("_id", payment.getId()),
                Manager.toDocument(payment)
        );
        
    } 
    
}
