package com.miocontotermico.eneaprocedure;


import com.miocontotermico.commons.NotificationCommons;
import com.miocontotermico.commons.EneaprocedureCommons;
import com.miocontotermico.core.Manager;
import com.miocontotermico.core.Paths;
import com.miocontotermico.core.Templates;
import com.miocontotermico.dao.CounterDao;
import com.miocontotermico.dao.FileDao;
import com.miocontotermico.dao.EneaprocedureDao;
import com.miocontotermico.dao.UserDao;
import com.miocontotermico.pojo.Eneaprocedure;
import com.miocontotermico.pojo.User;
import com.miocontotermico.pojo.types.FileType;
import com.miocontotermico.pojo.types.ProfileType;
import com.miocontotermico.pojo.types.ServiceType;
import com.miocontotermico.pojo.types.StatusType;
import com.miocontotermico.property.PropertyUtils;
import com.miocontotermico.property.StatusEntry;
import com.miocontotermico.support.file.posted.PostedFile;
import com.miocontotermico.util.ParamUtils;
import com.miocontotermico.util.PojoUtils;
import com.miocontotermico.util.RouteUtils;
import com.miocontotermico.util.TimeUtils;
import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class EneaprocedureController {

    private static final Logger LOGGER = LoggerFactory.getLogger(EneaprocedureController.class.getName());

    public static TemplateViewRoute eneaprocedures = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user != null) {
            user = UserDao.loadUser(user.getId());
            Manager.putSession(token, "user", user);
        }
        
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN) ;
            return Manager.renderEmpty();
        }
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));
        
        List<StatusEntry> statusList = EneaprocedureDao.loadEneaprocedureStatusList();
        attributes.put("statusList", PropertyUtils.cleanStatusList(statusList));
        
        
        // order (optional) filters
        Date startDate = DateUtils.addMonths(new Date(), -3);
        Date endDate = TimeUtils.today();
        
        if (StringUtils.isNotBlank(request.queryParams("startDate"))) {
            Date tmp = TimeUtils.toDate(request.queryParams("startDate"));
            if (tmp != null) {
                startDate = tmp;
            }
        }
        if (StringUtils.isNotBlank(request.queryParams("endDate"))) {
            Date tmp = TimeUtils.toDate(request.queryParams("endDate"));
            if (tmp != null) {
                endDate = tmp;
            }
        }
        
        attributes.put("startDate", startDate);
        attributes.put("endDate", endDate);
        
        String[] selectedStatuses = null;
        if (StringUtils.isNotBlank(request.queryParams("selectedStatuses"))) {
            selectedStatuses = StringUtils.split(request.queryParams("selectedStatuses"), "|");
        }
        attributes.put("selectedStatuses", selectedStatuses);
        
        ObjectId userId = null;
        ObjectId assignedUserId = null;
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            if (StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.operatore.toString())) {
                assignedUserId = user.getId();
            } else {
                userId = user.getId();
            }
        }
//        if (user.getProfileType() !=)
        List<Eneaprocedure> eneaprocedureList = EneaprocedureDao.loadEneaprocedureListByDateRangeAndStatus(startDate, endDate, selectedStatuses, userId, assignedUserId);
        attributes.put("eneaprocedureList", EneaprocedureCommons.toEntries(eneaprocedureList));
        
        return Manager.render(Templates.ENEAPROCEDURES, attributes, RouteUtils.pathType(request));
    };

    public static TemplateViewRoute eneaprocedures_add = (Request request, Response response) -> {
        
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user != null) {
            user = UserDao.loadUser(user.getId());
            Manager.putSession(token, "user", user);
        }
        
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN) ;
            return Manager.renderEmpty();
        }

        if (user.getCredit() == null || user.getCredit() < 45) {
            response.redirect(RouteUtils.contextPath(request) + Paths.BUY_CREDIT + "?insufficient=true") ;
        }
        
        // draft
        Eneaprocedure draft = EneaprocedureDao.loadDraftEneaprocedure(user.getId());
        if (draft == null) {
            draft = EneaprocedureCommons.initDraft(user);
//        } else {
//            if (!user.getProfileType().equals("privato")) {
//                draft = EneaprocedureCommons.initExistingDraft(draft);
//            }
        }
        attributes.put("eneaprocedure", draft);
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // step
        attributes.put("step", NumberUtils.toInt(request.queryParams("step"), 0));
        
        attributes.put("tab", request.queryParams("tab"));
        
        return Manager.render(Templates.ENEAPROCEDURES_ADD, attributes, RouteUtils.pathType(request));
    };
    
    public static Route eneaprocedures_add_info_save = (Request request, Response response) -> {
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        user = UserDao.loadUser(user.getId());
        
        // draft
        Eneaprocedure draft = EneaprocedureDao.loadDraftEneaprocedure(user.getId());
        if (draft == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        // merge
        Map<String, List<PostedFile>> files = new HashMap<>();
        Map<String, String> params = PojoUtils.paramsFromRequest(request, null, files);
        PojoUtils.mergeFromParams(params, draft);
        
        // files
        if (!files.isEmpty()) {
            
            String fieldname = "identityDocumentFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getIdentityDocumentFileIds()== null) {
                    draft.setIdentityDocumentFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getIdentityDocumentFileIds().addAll(ids);
                }
            }
            
            fieldname = "invoiceFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getInvoiceFileIds()== null) {
                    draft.setInvoiceFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getInvoiceFileIds().addAll(ids);
                }
            }
            
            fieldname = "bankTransferFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getBankTransferFileIds()== null) {
                    draft.setBankTransferFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getBankTransferFileIds().addAll(ids);
                }
            }
            
            fieldname = "collectionFormFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getCollectionFormFileIds()== null) {
                    draft.setCollectionFormFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getCollectionFormFileIds().addAll(ids);
                }
            }
            fieldname = "technicalDataFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getTechnicalDataFileIds()== null) {
                    draft.setTechnicalDataFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getTechnicalDataFileIds().addAll(ids);
                }
            }
            
            fieldname = "adminFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getAdminFileIds()== null) {
                    draft.setAdminFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getAdminFileIds().addAll(ids);
                }
            }
        
        }
        
        // save
        try {
            EneaprocedureDao.updateEneaprocedure(draft);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        
        return "ok";
    };
    
    public static Route eneaprocedures_add_save = (Request request, Response response) -> {
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        user = UserDao.loadUser(user.getId());
        
        // draft
        Eneaprocedure draft = EneaprocedureDao.loadDraftEneaprocedure(user.getId());
        if (draft == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        // merge
        Map<String, String> params = PojoUtils.paramsFromRequest(request);
        PojoUtils.mergeFromParams(params, draft);
        
        if (StringUtils.isBlank(draft.getService())) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Servizio non selezionato");
        }
        
        int creditToSave = ServiceType.valueOf(draft.getService()).getCredit();
        
        if (draft.getPriority() != null && draft.getPriority() == true) {
            creditToSave = creditToSave + 30;
        }
        
        if (creditToSave > user.getCredit()) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Crediti non sufficienti");
        }
        
        // data
        draft.setStatus(StatusType.opened.toString());
        draft.setLastStatusUpdate(TimeUtils.now());
        draft.setDate(TimeUtils.now());

        // protocol
        String protocolKey = "eneaprocedure-protocol";
        int next = CounterDao.next(protocolKey);
        draft.setProtocol("" + next);
        
        // save
        try {
            EneaprocedureDao.updateEneaprocedure(draft);

            Integer credit = user.getCredit() != null ? user.getCredit() : 0;
            credit -= creditToSave;
            user.setCredit(credit);
            UserDao.updateUser(user);
            Manager.putSession(token, "user", user);
            
            // INVIARE MAIL A SOGENIT
            if (NotificationCommons.notifyEneaprocedureReceive(request, draft)) {
                // ...
            }
            if (draft.getUserId() != null) {
                User eneaprocedureUser = UserDao.loadUser(draft.getUserId());
                if (NotificationCommons.notifyEneaprocedureReceiveToUser(request, draft, eneaprocedureUser)) {
                    // ...
                }
            }
            
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        
        return "ok";
    };
    
    public static Route eneaprocedures_add_fileid_remove = (Request request, Response response) -> {
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        user = UserDao.loadUser(user.getId());
        
        // draft
        Eneaprocedure draft = EneaprocedureDao.loadDraftEneaprocedure(user.getId());
        if (draft == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        // list
        String listName = request.queryParams("listName");
        if (StringUtils.isEmpty(listName)) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        // id
        ObjectId fileId = ParamUtils.toObjectId(request.queryParams("fileId"));
        if (fileId == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        List<ObjectId> ids = null;

        switch (listName) {
            case "identityDocumentFileIds":
                ids = draft.getIdentityDocumentFileIds();
                break;
            case "invoiceFileIds":
                ids = draft.getInvoiceFileIds();
                break;
            case "bankTransferFileIds":
                ids = draft.getBankTransferFileIds();
                break;
            case "collectionFormFileIds":
                ids = draft.getCollectionFormFileIds();
                break;
            case "technicalDataFileIds":
                ids = draft.getTechnicalDataFileIds();
                break;
            case "adminFileIds":
                ids = draft.getAdminFileIds();
                break;
            default:
                break;
        }
        if (ids == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        // remove id
        Iterator<ObjectId> iter = ids.iterator();
        while (iter.hasNext()) {
            if (iter.next().equals(fileId)) {
                iter.remove();
                break;
            }
        }
        
        // manca rimozione lista
        // ??????
        
        // save
        try {
            EneaprocedureDao.updateEneaprocedure(draft);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        
        return "ok";
    };
    
    public static TemplateViewRoute eneaprocedure_edit = (Request request, Response response) -> {
        
        ObjectId eneaprocedureId = ParamUtils.toObjectId(request.queryParams("eneaprocedureId"));
        
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user != null) {
            user = UserDao.loadUser(user.getId());
            Manager.putSession(token, "user", user);
        }
        
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN) ;
            return Manager.renderEmpty();
        }

        Eneaprocedure eneaprocedure = null;
        if (eneaprocedureId != null) {
            eneaprocedure = EneaprocedureDao.loadEneaprocedure(eneaprocedureId);
        }
        attributes.put("eneaprocedure", eneaprocedure);
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));
        
        List<User> userList = UserDao.loadSystemUserList();
        attributes.put("userList", userList);

        return Manager.render(Templates.ENEAPROCEDURE_EDIT, attributes, RouteUtils.pathType(request));
    };
    
    public static Route eneaprocedure_edit_save = (Request request, Response response) -> {
        // params
        ObjectId eneaprocedureId = ParamUtils.toObjectId(request.queryParams("eneaprocedureId"));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }
        
        // merge
        Map<String, List<PostedFile>> files = new HashMap<>();
        Map<String, String> params = PojoUtils.paramsFromRequest(request, null, files);
        
        if (eneaprocedureId != null) {
            // params
            Eneaprocedure eneaprocedure = EneaprocedureDao.loadEneaprocedure(eneaprocedureId);
            if (eneaprocedure != null) {
                Boolean sendDocumentMail = false;
                Boolean sendFinalMail = false;
                Boolean sendAssignedMail = false;
                Boolean sendUpdateStatusMail = false;
                
                if (StringUtils.isNotBlank(params.get("status"))) {
                    if (!StringUtils.equalsIgnoreCase(eneaprocedure.getStatus(), params.get("status"))) {
                        sendUpdateStatusMail = true;
                    }
                    eneaprocedure.setStatus(params.get("status"));
                }
                if (ParamUtils.toObjectId(params.get("assignedUserId")) != null) {
                    if (eneaprocedure.getAssignedUserId() != null) {
                        if (!eneaprocedure.getAssignedUserId().equals(ParamUtils.toObjectId(params.get("assignedUserId")))) {
                            sendAssignedMail = true;
                        }
                    } else {
                        sendAssignedMail = true;
                    }
                    eneaprocedure.setAssignedUserId(ParamUtils.toObjectId(params.get("assignedUserId")));
                }
                
                eneaprocedure.setNote(params.get("note"));
                
                if (!files.isEmpty()) {
                    String fieldname = "adminFileIds";
                    if (files.containsKey(fieldname)) {
                        if (eneaprocedure.getAdminFileIds()== null) {
                            eneaprocedure.setAdminFileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            eneaprocedure.getAdminFileIds().addAll(ids);
//                            sendFinalMail = true;
                        }
                    }

                }                
                
                // save
                try {
                    EneaprocedureDao.updateEneaprocedure(eneaprocedure);
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                
                // notify
                if (sendFinalMail) {
                    if (NotificationCommons.notifyEneaprocedureFinalUpload(request, eneaprocedure)) {
                        // ...
                    }
                }
                if (sendDocumentMail) {
                    if (NotificationCommons.notifyEneaprocedureUpload(request, eneaprocedure)) {
                        // ...
                    }
                }
                if (sendAssignedMail) {
                    if (eneaprocedure.getUserId() != null) {
                        User userAssigned = UserDao.loadUser(eneaprocedure.getAssignedUserId());
                        if (NotificationCommons.notifyEneaprocedureAssigned(request, eneaprocedure, userAssigned)) {
                            // ...
                        }
                    }
                }
                if (sendUpdateStatusMail) {
                    if (eneaprocedure.getUserId() != null) {
                        User eneaprocedureUser = UserDao.loadUser(eneaprocedure.getUserId());
                        if (NotificationCommons.notifyEneaprocedure(request, eneaprocedure, eneaprocedureUser)) {
                            // ...
                        }
                    }
                }
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        // files
        
        

        return "ok";        

    };  
    
    public static Route eneaprocedure_edit_fileid_remove = (Request request, Response response) -> {
        
        // params
        ObjectId eneaprocedureId = ParamUtils.toObjectId(request.queryParams("eneaprocedureId"));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }
        
        // list
        String listName = request.queryParams("listName");
        if (StringUtils.isEmpty(listName)) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        if (eneaprocedureId != null) {
            Eneaprocedure eneaprocedure = EneaprocedureDao.loadEneaprocedure(eneaprocedureId);
            if (eneaprocedure != null) {
                // id
                ObjectId fileId = ParamUtils.toObjectId(request.queryParams("fileId"));
                if (fileId == null) {
                    throw Spark.halt(HttpStatus.BAD_REQUEST_400);
                }

                List<ObjectId> ids = null;
                switch (listName) {
                    case "adminFileIds":
                        ids = eneaprocedure.getAdminFileIds();
                        break;
                    default:
                        break;
                }
                if (ids == null) {
                    throw Spark.halt(HttpStatus.BAD_REQUEST_400);
                }

                // remove id
                Iterator<ObjectId> iter = ids.iterator();
                while (iter.hasNext()) {
                    if (iter.next().equals(fileId)) {
                        iter.remove();
                        break;
                    }
                }

                // manca rimozione lista
                // ??????

                // save
                try {
                    EneaprocedureDao.updateEneaprocedure(eneaprocedure);
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }

            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }

        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return "ok";        
    };

    public static Route eneaprocedure_remove = (Request request, Response response) -> {
        
        ObjectId eneaprocedureId = ParamUtils.toObjectId(request.queryParams("eneaprocedureId"));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        
        if (eneaprocedureId != null) {
            // params
            Eneaprocedure eneaprocedure = EneaprocedureDao.loadEneaprocedure(eneaprocedureId);
            if (eneaprocedure != null) {
                EneaprocedureDao.updateEneaprocedureCancelled(eneaprocedureId, true);
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
                    
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return "ok";        
    };          
    
    public static Route eneaprocedure_status_update = (Request request, Response response) -> {
        
        ObjectId eneaprocedureId = ParamUtils.toObjectId(request.queryParams("eneaprocedureId"));
        String status = request.queryParams("status");
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        
        if (eneaprocedureId != null) {
            // params
            Eneaprocedure eneaprocedure = EneaprocedureDao.loadEneaprocedure(eneaprocedureId);
            if (eneaprocedure != null) {
                eneaprocedure.setStatus(status);
                EneaprocedureDao.updateEneaprocedure(eneaprocedure);
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
            // notify
            if (eneaprocedure.getUserId() != null) {
                User eneaprocedureUser = UserDao.loadUser(eneaprocedure.getUserId());
                if (NotificationCommons.notifyEneaprocedure(request, eneaprocedure, eneaprocedureUser)) {
                    // ...
                }
            }
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return "ok";        
    };



    ////////////
    // internals

    private static List<ObjectId> insertFiles(List<PostedFile> posteds) {
        if (posteds == null) {
            return null;
        }
        if (posteds.isEmpty()) {
            return null;
        }
        List<ObjectId> ids = new ArrayList<>();
        for (PostedFile posted : posteds) {
            
            ObjectId oid = null;
            try {
                
                // filename
                String filename = FileDao.composeFilename(FileType.attachment, posted.getExtension());
                
                // save file
                File fll = new File(posted.getFilename());
                oid = FileDao.insertFile(filename, 
                        posted.getName(),
                        posted.getContentType(), 
                        FileUtils.readFileToByteArray(fll)
                );
                
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }

            if (oid != null) {
                ids.add(oid);
            }
        }
        return ids;
    }
    
}
