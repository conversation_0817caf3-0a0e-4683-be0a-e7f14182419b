package com.miocontotermico.shop;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.miocontotermico.commons.NotificationCommons;
import com.miocontotermico.core.Manager;
import com.miocontotermico.core.Paths;
import com.miocontotermico.core.Templates;
import com.miocontotermico.dao.CounterDao;
import com.miocontotermico.dao.PaymentDao;
import com.miocontotermico.dao.PaymentPlatformDao;
import com.miocontotermico.dao.UserDao;
import com.miocontotermico.order.OrderDao;
import com.miocontotermico.payment.nexi.NexiGateway;
import com.miocontotermico.payment.paypal.PaymentInitResponse;
import com.miocontotermico.payment.paypal.PaypalGateway;
import com.miocontotermico.pojo.Order;
import com.miocontotermico.pojo.Payment;
import com.miocontotermico.pojo.PaymentPlatform;
import com.miocontotermico.pojo.User;
import com.miocontotermico.pojo.types.OrderStatusType;
import com.miocontotermico.pojo.types.OrderType;
import com.miocontotermico.pojo.types.PaymentStatusType;
import com.miocontotermico.pojo.types.PaymentType;
import com.miocontotermico.util.MoneyUtils;
import com.miocontotermico.util.ParamUtils;
import com.miocontotermico.util.RouteUtils;
import com.miocontotermico.util.TimeUtils;
import java.util.Date;
import java.util.List;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class ShopController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ShopController.class.getName());

    public static TemplateViewRoute buy_credit = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN) ;
            return Manager.renderEmpty();
        }
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));
        
        Boolean insufficient = BooleanUtils.toBoolean(request.queryParams("insufficient"));
        attributes.put("insufficient", insufficient);
        
        List<Payment> paymentList = PaymentDao.loadPaymentList();
        attributes.put("paymentList", paymentList);
        
        return Manager.render(Templates.BUY_CREDIT, attributes, RouteUtils.pathType(request));
    };    

    public static Route buy_credit_send = (Request request, Response response) -> {
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        
        String paymentCode = request.queryParams("payment");
        
        // order
        Order order = OrderDao.loadCartOrder(user.getId());
        if (order == null) {
            order = new Order();
        }
        double finalPrice = NumberUtils.toDouble(request.queryParams("numCredit"), 0);
        
        if (finalPrice >= 15000) {
            finalPrice = finalPrice * 0.92;
        } else if (finalPrice >= 10000) {
            finalPrice = finalPrice * 0.95;
        } else if (finalPrice >= 5000) {
            finalPrice = finalPrice * 0.98;
        }
        
        finalPrice = finalPrice * 1.22;
        finalPrice = MoneyUtils.round(finalPrice);
        
        
        order.setFinalPrice(finalPrice);
        order.setCredit(NumberUtils.toInt(request.queryParams("numCredit"), 0));
        order.setOrderType(OrderType.contract.toString());
        order.setStatus(OrderStatusType.cart.toString());
        order.setUserId(user.getId());
        order.setPaymentCode(paymentCode);
        
        User userOrder = UserDao.loadUser(user.getId());
        order.setFullname(userOrder.getFullname());
        order.setName(userOrder.getName());
        order.setLastname(userOrder.getLastname());
        order.setPec(userOrder.getPec());
        order.setSdiNumber(userOrder.getSdiNumber());
        order.setTin(userOrder.getTin());
        order.setVatNumber(userOrder.getVatNumber());
        order.setCity(userOrder.getCity());
        order.setAddress(userOrder.getAddress());
        order.setProvinceCode(userOrder.getProvinceCode());
        order.setPostalCode(userOrder.getPostalCode());
        order.setPhoneNumber(userOrder.getPhoneNumber());
        order.setExternalPaymentKey(null);
        Payment payment = null;
        try {
            payment = PaymentDao.loadPaymentByCode(order.getPaymentCode());
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        if (payment == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Pagamento non utilizzabile");
        }
        

        PaymentPlatform platform = null;
        if (!StringUtils.equalsIgnoreCase(payment.getPaymentType(), PaymentType.manual.toString())) {
            try {
                platform = PaymentPlatformDao.loadPaymentPlatformByPaymentType(payment.getPaymentType());
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
            if (platform == null) {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Pagamento non utilizzabile");
            }    
        }

        // transaction key
        // paypal
        // - username   = <EMAIL>
        // - password   = 12345678
        // - docs       = https://developer.paypal.com/docs/
        // - endpoint   = lo calcola l'SDK                                                   (PRODUZIONE)
        // -            = lo calcola l'SDK                                                   (TEST)
        // - alias      = AY_fMbg_8peuRrhzOGGAadUwaqVFFz9MWHJCA9T7aEYuZ1Lsh--PGsjaseMNBrR6OeLfBz4fyZ0JwDqB
        // - secret key = EEBc2l_Gwx9xu6BT7UXJjmDcw0cz2sH_gPwMVsJJLe7eQf6AdKXnYB2kv-4lrXOBjrFxn-4jqDtbU2uh

        // transazioni da provare
        // --> esito positivo
        // --> esito negativo
        // --> cancel da parte utente

        // redirect url
        String url = null;
        
        String transactionKey = StringUtils.defaultIfBlank(payment.getPaymentType(), PaymentType.manual.toString()) + "-" + TimeUtils.toString(TimeUtils.now(), "yyMMddHHmmss");
        order.setTransactionKey(transactionKey);

        ObjectId orderId = order.getId();
        if (orderId != null) {
            OrderDao.updateOrder(order);
        } else {
            orderId = OrderDao.insertOrder(order);
            order = OrderDao.loadOrder(orderId);
        }
        
        if (StringUtils.equalsIgnoreCase(payment.getPaymentType(), PaymentType.nexi.toString())) {

            // nexi
            // - username   = <EMAIL>
            // - password   = !midastouchNI
            // - docs       = https://ecommerce.nexi.it/specifiche-tecniche
            // - endpoint   = https://ecommerce.nexi.it/ecomm/ecomm/DispatcherServlet            (PRODUZIONE)
            // -            = https://int-ecommerce.nexi.it/ecomm/ecomm/DispatcherServlet        (TEST)
            // - alias      = ALIAS_WEB_00016526
            // - secret key = 5ZW0UAKDCAXIL2LEXNM7UP42KCXIG8A3

            // transazioni da provare
            // - numero     ****************                        --> VISA esito positivo
            // - numero     ****************                        --> VISA esito negativo
            // - numero     ****************                        --> MASTERCARD esito positivo
            // - numero     ****************                        --> MASTERCARD esito negativo
            //
            // - scadenza   12/2030
            // - CVV2       qualsiasi combinazione di 3 numeri è accettata

            // redirect url
            url = NexiGateway.paymentInit(request, platform, transactionKey, user, order, finalPrice);
            if (StringUtils.isBlank(url)) {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Pagamento nexi non configurato");
            }

            // save payment info
            OrderDao.updateOrder(order);

            // return with payment redirect
            return url;
        } else if (StringUtils.equalsIgnoreCase(payment.getPaymentType(), PaymentType.nexi_intesa.toString())) {

            //https://stgdeveloper.nexigroup.com/it/area-test/api-key
            
            // redirect url
            url = NexiGateway.paymentInitIntesa(request, platform, transactionKey, user, order, finalPrice);
            if (StringUtils.isBlank(url)) {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Pagamento nexi non configurato");
            }

            // save payment info
            OrderDao.updateOrder(order);

            // return with payment redirect
            return url;
        } else if (StringUtils.equalsIgnoreCase(payment.getPaymentType(), PaymentType.paypal.toString())) {
            order = OrderDao.loadOrder(orderId);

            PaymentInitResponse paymentInitResponse = PaypalGateway.createOrder(request, platform, transactionKey, user, order, finalPrice);
            if (paymentInitResponse != null) {
                url = paymentInitResponse.getPaymentURL();
                order.setExternalPaymentKey(paymentInitResponse.getPaymentID());
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Pagamento paypal non accettato");
            }
            OrderDao.updateOrder(order);
            // return with payment redirect
            return url;
        } else {
            return RouteUtils.contextPath(request) + Paths.PAID + "?orderId=" + orderId.toString();
        }
        
        // return "ok";

    };    
    
    public static TemplateViewRoute paid = (Request request, Response response) -> {
        ObjectId orderId = ParamUtils.toObjectId(request.queryParams("orderId"));
        String transactionKey = request.queryParams("transactionKey");
        String paymentId = request.queryParams("paymentid");

        Order order = OrderDao.loadOrder(orderId);
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        
        attributes.put("user", user);

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));
        
        // confirmed
        boolean confirmed = false;
        boolean settled = false;
        Date paymentDate = null;


        // payment
        Payment payment = null;
        if (order != null) {
            if (StringUtils.isNotBlank(order.getPaymentCode())) {
                try {
                    payment = PaymentDao.loadPaymentByCode(order.getPaymentCode());
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
            }
        }
        attributes.put("payment", payment);

        // check payment platform (MANDATORY when not manual)
        PaymentPlatform platform = null;
        if (payment != null) {
            if (StringUtils.isNotBlank(payment.getPaymentType())) {
                if (!StringUtils.equalsIgnoreCase(payment.getPaymentType(), PaymentType.manual.toString())) {
                    try {
                        platform = PaymentPlatformDao.loadPaymentPlatformByPaymentType(payment.getPaymentType());
                    } catch (Exception ex) {
                        LOGGER.error("suppressed", ex);
                    }
                }
            }
        }
        
        if (((order != null) && payment != null) && StringUtils.isNotBlank(payment.getPaymentType())) {

            PaymentType type = EnumUtils.getEnum(PaymentType.class, payment.getPaymentType());
            if (type != null) {

                switch (type) {

                    case manual:
                        confirmed = true;
                        break;
                    case paypal:
                        // MANDATORY final price
                        double finalPrice = order.getFinalPrice();

                        confirmed = BooleanUtils.toBoolean(request.queryParams("confirmed"));
                        attributes.put("price", finalPrice);
                        attributes.put("transactionKey", transactionKey);
                        attributes.put("error", "-");
                        attributes.put("errorMessage", "-");
                        attributes.put("token", request.queryParams("token"));
                        attributes.put("PayerID", request.queryParams("PayerID"));
                        if (confirmed) {
                            settled = PaypalGateway.captureOrder(request, platform, transactionKey, user, order, finalPrice);
                        }
                        if (settled) {
                            paymentDate = TimeUtils.now();
                        }
                        break;
                    case nexi:
                        confirmed = BooleanUtils.toBoolean(request.queryParams("confirmed"));
                        if (StringUtils.isNotBlank(request.queryParams("codiceEsito"))) {
                            confirmed = NumberUtils.toInt(request.queryParams("codiceEsito"), -1) == 0;
                        }
                        attributes.put("alias", request.queryParams("alias"));
                        attributes.put("price", request.queryParams("importo"));
                        attributes.put("currency", request.queryParams("divisa"));
                        attributes.put("transactionKey", request.queryParams("codTrans"));
                        attributes.put("errorCode", request.queryParams("codiceEsito"));
                        attributes.put("error", request.queryParams("esito"));
                        attributes.put("errorMessage", request.queryParams("messaggio"));
                        if (confirmed) {
                            settled = StringUtils.equals(order.getTransactionKey(), request.queryParams("codTrans"));
                        }
                        if (settled) {
                            // data=20190612
                            // orario=112410
                            paymentDate = TimeUtils.toDate(request.queryParams("data") + request.queryParams("orario"), "yyyyMMddhhmmss");
                        }
                        break;
                    case nexi_intesa:
                        if (StringUtils.isNotBlank(paymentId)) {
                            String apiKey = platform.getSecretKey();
                            String correlationId = UUID.randomUUID().toString();
                            order.setExternalPaymentKey(paymentId);
                            try {
                                Map<String, String> headers = new HashMap<>();
                                headers.put("X-Api-Key", apiKey);
                                headers.put("Correlation-Id", correlationId);

                                String apiUrl = platform.getEndpoint() + "/operations/" + paymentId;
                                HttpResponse<String> responseHttp = sendGetRequest(apiUrl, headers);

                                String responseBody = responseHttp.body();

                                // Parse JSON response
                                ObjectMapper objectMapper = new ObjectMapper();
                                JsonNode jsonNode = objectMapper.readTree(responseBody);

                                // Extract lastOperationType
                                String lastOperationType = jsonNode.path("operationResult").asText();
                                confirmed = StringUtils.equalsIgnoreCase(lastOperationType, "EXECUTED");
                                settled = StringUtils.equalsIgnoreCase(lastOperationType, "EXECUTED");
                                if (settled) {
                                    paymentDate = TimeUtils.now();
                                }
                            } catch (Exception e) {
                                //
                            }
                        }
                        break;
                    default:
                        break;

                }

            }


            attributes.put("confirmed", confirmed);
            attributes.put("settled", settled);

            // order confirmation
            if (confirmed) {
                int next = CounterDao.next("order-protocol");
                order.setProtocol("" + next);
                order.setDate(TimeUtils.now());
                order.setStatus(OrderStatusType.opened.toString());
                order.setLastStatusUpdate(TimeUtils.now());

                // payment confirmation
                order.setPaymentStatus(null);
                order.setPaymentDate(null);
                if (settled) {
                    order.setPaymentStatus(PaymentStatusType.paid.toString());
                    order.setPaymentDate(paymentDate);
                    Integer credit = user.getCredit() != null ? user.getCredit() : 0;
                    credit += order.getCredit();
                    user.setCredit(credit);
                    UserDao.updateUser(user);
                    // update user on redis
                    Manager.putSession(token, "user", user);
                }

                OrderDao.updateOrder(order);

                if (NotificationCommons.notifyPaid(request, order)) {
//                    // ...
                }

            }
        }        
        

        // refresh cart
        attributes.put("order", order);
        return Manager.render(Templates.PAID, attributes, RouteUtils.pathType(request));
    };
    
    private static HttpResponse<String> sendGetRequest(String url, Map<String, String> headers) throws Exception {
        HttpClient client = HttpClient.newHttpClient();
        HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .GET();

        for (Map.Entry<String, String> entry : headers.entrySet()) {
            requestBuilder.header(entry.getKey(), entry.getValue());
        }

        HttpRequest request = requestBuilder.build();
        return client.send(request, HttpResponse.BodyHandlers.ofString());
    }
    
}
