package com.miocontotermico.data;

import com.miocontotermico.dao.CityDao;
import com.miocontotermico.dao.ImageDao;
import com.miocontotermico.pojo.City;
import com.miocontotermico.support.image.limitless.Image;
import com.miocontotermico.util.ImageUtils;
import java.net.URLEncoder;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;

/**
 *
 * <AUTHOR>
 */
public class DataController {

    private static final Logger LOGGER = LoggerFactory.getLogger(DataController.class.getName());

    public static Route data_cities = (Request request, Response response) -> {

        String name = request.queryParams("name");

        String stringToRemove = StringUtils.substringBetween(name, "(", ")");
        if (StringUtils.isNotBlank(stringToRemove)) {
            name = StringUtils.remove(name, "(" + stringToRemove + ")");
        }
        name = StringUtils.remove(name, "(");
        name = StringUtils.remove(name, ")");
        name = StringUtils.remove(name, "^");
        name = StringUtils.remove(name, "$");

        List<City> cities = CityDao.loadCityList(name);

        //description

        String[][] names;
        if ((cities != null) && (cities.size() > 0)) {
            names = new String[cities.size()][3];
            for (int i = 0; i < cities.size(); i++) {
                names[i][0] = cities.get(i).getCity();
                names[i][1] = cities.get(i).getPostalCode() != null ? cities.get(i).getPostalCode() : "";
                names[i][2] = cities.get(i).getProvinceCode() != null ? cities.get(i).getProvinceCode() : "";
            }
        } else {
            names = new String[0][0];
        }

        return names;
    };

    ////////////
    // internals
    private static String imageAsDatauri(ObjectId imageId) {
        String datauri = null;
        
        if (imageId != null) {

            Image img = null;
            try {
                img = ImageDao.loadImage(imageId);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }

            if (img != null) {
                
                // create "data URI"
                datauri = ImageUtils.toDatauri(img.getContentType(), img.getBytes());
                
            } else {
                LOGGER.warn("empty image oid " + imageId);
            }
        } else {
            LOGGER.warn("unexistent image oid " + imageId);
        }
        
        return datauri;
    }

    private static String encode(String value) {
        if (StringUtils.isNotBlank(value)) {
            try {
                String encoded = URLEncoder.encode(value, "UTF-8");
                value = encoded;
            } catch (Exception ex) {
                LOGGER.error("unable to encode value", ex);
            }
        }
        return value;
    }
    
}
