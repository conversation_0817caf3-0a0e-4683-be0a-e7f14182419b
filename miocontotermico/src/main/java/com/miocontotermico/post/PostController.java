package com.miocontotermico.post;

import com.github.slugify.Slugify;
import com.miocontotermico.core.Manager;
import com.miocontotermico.core.Paths;
import com.miocontotermico.core.Templates;
import com.miocontotermico.dao.PostDao;
import com.miocontotermico.pojo.Post;
import com.miocontotermico.pojo.User;
import com.miocontotermico.support.image.slim.SlimImage;
import com.miocontotermico.util.ParamUtils;
import com.miocontotermico.util.PojoUtils;
import com.miocontotermico.util.RouteUtils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class PostController {
    private static final Logger LOGGER = LoggerFactory.getLogger(PostController.class.getName());    
    
    public static TemplateViewRoute posts = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN) ;
            return Manager.renderEmpty();
        }
        
        attributes.put("user", user);
        
        List<Post> postList = PostDao.loadPostList();
        attributes.put("postList", postList);        
        
        return Manager.render(Templates.POSTS, attributes, RouteUtils.pathType(request));
    };    

    public static TemplateViewRoute post_edit = (Request request, Response response) -> {
        
        ObjectId postId = ParamUtils.toObjectId(request.queryParams("postId"));
        
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.HOME) ;
            return Manager.renderEmpty();
        }
        
        
        Post post;
        if (postId != null) {
            post = PostDao.loadPost(postId);
        } else {
            post = new Post();
        }
        attributes.put("post", post);
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        boolean gallery = BooleanUtils.toBoolean(request.queryParams("gallery"));
        if (postId == null) {
            gallery = true;
        }
        attributes.put("gallery", gallery);
        
        return Manager.render(Templates.POST_EDIT, attributes, RouteUtils.pathType(request));
    };
    
    public static Route post_edit_save = (Request request, Response response) -> {
        // params
        ObjectId postId = ParamUtils.toObjectId(request.queryParams("postId"));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }
        
        boolean gallery = BooleanUtils.toBoolean(request.queryParams("gallery"));
        
        if (postId != null) {
            Post postOld = PostDao.loadPost(postId);
            Post post = PojoUtils.mergeFromRequest(request, postOld);
            
            if (isValidPost(post)) {
                
                Slugify slg = new Slugify();
                String identifier = post.getTitle() + "-" + RouteUtils.generateIdentifier();
                post.setIdentifier(slg.slugify(identifier));                
                PostDao.updatePost(post);

                // images
                if (gallery) {
                    List<SlimImage> uploadeds = new ArrayList<>();
                    for (String param : request.queryParams()) {
                        if (StringUtils.startsWithIgnoreCase(param, "uploaded-files-")) {

                            String slim = request.queryParams(param);
                            if (StringUtils.isNotBlank(slim)) {
                                SlimImage uploaded = null;
                                try {
                                    uploaded = Manager.deserializeFromJson(slim, SlimImage.class);
                                } catch (Exception ex) {
                                    LOGGER.error("suppressed", ex);
                                }
                                if (uploaded != null) {
                                    if (!uploaded.isEmpty()) {
                                        uploadeds.add(uploaded);
                                    }
                                }
                            }

                        }
                    }
                    if (!uploadeds.isEmpty()) {
                        PostDao.updatePostImages(user.getUsername(), postId, uploadeds);
                    } else {
                        PostDao.removePostImages(postId);
                    }
                }
                    
                // navigation
                response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.POSTS));

            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.POST_EDIT) + "?postId=" + post.getId());
            }
        } else {
            // params
            Post post = PojoUtils.createFromRequest(request, Post.class);
            
            if (isValidPost(post)) {
                
                Slugify slg = new Slugify();
                String identifier = post.getTitle() + "-" + RouteUtils.generateIdentifier();
                post.setIdentifier(slg.slugify(identifier));                
                
                postId = PostDao.insertPost(post);

                // images
                List<SlimImage> uploadeds = new ArrayList<>();
                for (String param : request.queryParams()) {
                    if (StringUtils.startsWithIgnoreCase(param, "uploaded-files-")) {
                        
                        String slim = request.queryParams(param);
                        if (StringUtils.isNotBlank(slim)) {
                            SlimImage uploaded = null;
                            try {
                                uploaded = Manager.deserializeFromJson(slim, SlimImage.class);
                            } catch (Exception ex) {
                                LOGGER.error("suppressed", ex);
                            }
                            if (uploaded != null) {
                                if (!uploaded.isEmpty()) {
                                    uploadeds.add(uploaded);
                                }
                            }
                        }
                        
                    }
                }
                if (!uploadeds.isEmpty()) {
                    PostDao.updatePostImages(user.getUsername(), postId, uploadeds);
                }
                
                // navigation
                response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.POSTS));

            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.POST_EDIT));
            }
        }        

        return null;
    };  
    
    public static Route post_remove = (Request request, Response response) -> {
        
        ObjectId postId = ParamUtils.toObjectId(request.queryParams("postId"));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            Spark.halt(HttpStatus.UNAUTHORIZED_401);
            return null;
        }
        
        if (postId != null) {
            // params
            Post post = PostDao.loadPost(postId);
            if (post != null) {
                PostDao.updatePostCancelled(postId, true);
            } else {
                Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
                    
        } else {
            Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return "ok";        
    };    

    ////////////
    // internals
    
    private static boolean isValidPost(Post entity) {
        boolean valid = (entity != null) &&
                StringUtils.isNotBlank(entity.getTitle())
                ;
        
        // ?????? @leonardo: inserire gli attuali vincoli
        if (!valid) {
            if (entity != null) {
                LOGGER.warn(
                        "city validation problem:\n" +
                        "- ref " + entity.getTitle()+ "\n"
                );
            } else {
                LOGGER.warn(
                        "city validation problem:\n" +
                        "- empty"
                );
            }
        }
        
        return valid;
    }
}

