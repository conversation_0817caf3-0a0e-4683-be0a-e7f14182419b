package com.miocontotermico.invoicediscount;


import com.miocontotermico.commons.InvoicediscountCommons;
import com.miocontotermico.commons.NotificationCommons;
import com.miocontotermico.core.Manager;
import com.miocontotermico.core.Paths;
import com.miocontotermico.core.Templates;
import com.miocontotermico.dao.CounterDao;
import com.miocontotermico.dao.FileDao;
import com.miocontotermico.dao.InvoicediscountDao;
import com.miocontotermico.dao.UserDao;
import com.miocontotermico.pojo.Invoicediscount;
import com.miocontotermico.pojo.User;
import com.miocontotermico.pojo.types.FileType;
import com.miocontotermico.pojo.types.ProfileType;
import com.miocontotermico.pojo.types.ServiceType;
import com.miocontotermico.pojo.types.StatusType;
import com.miocontotermico.property.PropertyUtils;
import com.miocontotermico.property.StatusEntry;
import com.miocontotermico.support.file.posted.PostedFile;
import com.miocontotermico.util.ParamUtils;
import com.miocontotermico.util.PojoUtils;
import com.miocontotermico.util.RouteUtils;
import com.miocontotermico.util.TimeUtils;
import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class InvoicediscountController {

    private static final Logger LOGGER = LoggerFactory.getLogger(InvoicediscountController.class.getName());

    public static TemplateViewRoute invoicediscounts = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user != null) {
            user = UserDao.loadUser(user.getId());
            Manager.putSession(token, "user", user);
        }
        
        attributes.put("user", user);
        
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN) ;
            return Manager.renderEmpty();
        }
                
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));
        
        List<StatusEntry> statusList = InvoicediscountDao.loadInvoicediscountStatusList();
        attributes.put("statusList", PropertyUtils.cleanStatusList(statusList));
        
        
        // order (optional) filters
        Date startDate = DateUtils.addMonths(new Date(), -3);
        Date endDate = TimeUtils.today();
        
        if (StringUtils.isNotBlank(request.queryParams("startDate"))) {
            Date tmp = TimeUtils.toDate(request.queryParams("startDate"));
            if (tmp != null) {
                startDate = tmp;
            }
        }
        if (StringUtils.isNotBlank(request.queryParams("endDate"))) {
            Date tmp = TimeUtils.toDate(request.queryParams("endDate"));
            if (tmp != null) {
                endDate = tmp;
            }
        }
        
        attributes.put("startDate", startDate);
        attributes.put("endDate", endDate);
        
        String[] selectedStatuses = null;
        if (StringUtils.isNotBlank(request.queryParams("selectedStatuses"))) {
            selectedStatuses = StringUtils.split(request.queryParams("selectedStatuses"), "|");
        }
        attributes.put("selectedStatuses", selectedStatuses);
        
        ObjectId userId = null;
        ObjectId assignedUserId = null;
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            if (StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.operatore.toString())) {
                assignedUserId = user.getId();
            } else {
                userId = user.getId();
            }
        }
//        if (user.getProfileType() !=)
        List<Invoicediscount> invoicediscountList = InvoicediscountDao.loadInvoicediscountListByDateRangeAndStatus(startDate, endDate, selectedStatuses, userId, assignedUserId);
        attributes.put("invoicediscountList", InvoicediscountCommons.toEntries(invoicediscountList));
        
        return Manager.render(Templates.INVOICEDISCOUNTS, attributes, RouteUtils.pathType(request));
    };

    public static TemplateViewRoute invoicediscounts_add = (Request request, Response response) -> {
        
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user != null) {
            user = UserDao.loadUser(user.getId());
            Manager.putSession(token, "user", user);
        }
        
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN) ;
            return Manager.renderEmpty();
        }

        if (user.getCredit() == null || user.getCredit() < 125) {
            response.redirect(RouteUtils.contextPath(request) + Paths.BUY_CREDIT + "?insufficient=true") ;
        }
        
        // draft
        Invoicediscount draft = InvoicediscountDao.loadDraftInvoicediscount(user.getId());
        if (draft == null) {
            draft = InvoicediscountCommons.initDraft(user);
//        } else {
//            if (!user.getProfileType().equals("privato")) {
//                draft = InvoicediscountCommons.initExistingDraft(draft);
//            }
        }
        attributes.put("invoicediscount", draft);
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // step
        attributes.put("step", NumberUtils.toInt(request.queryParams("step"), 0));
        
        attributes.put("tab", request.queryParams("tab"));
        
        return Manager.render(Templates.INVOICEDISCOUNTS_ADD, attributes, RouteUtils.pathType(request));
    };
    
    public static Route invoicediscounts_add_info_save = (Request request, Response response) -> {
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        user = UserDao.loadUser(user.getId());
        
        // draft
        Invoicediscount draft = InvoicediscountDao.loadDraftInvoicediscount(user.getId());
        if (draft == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        // merge
        Map<String, List<PostedFile>> files = new HashMap<>();
        Map<String, String> params = PojoUtils.paramsFromRequest(request, null, files);
        PojoUtils.mergeFromParams(params, draft);
        
        // files
        if (!files.isEmpty()) {
            
            String fieldname = "identityDocumentFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getIdentityDocumentFileIds()== null) {
                    draft.setIdentityDocumentFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getIdentityDocumentFileIds().addAll(ids);
                }
            }
            
            fieldname = "invoiceFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getInvoiceFileIds()== null) {
                    draft.setInvoiceFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getInvoiceFileIds().addAll(ids);
                }
            }
            
            fieldname = "bankTransferFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getBankTransferFileIds()== null) {
                    draft.setBankTransferFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getBankTransferFileIds().addAll(ids);
                }
            }
            
            fieldname = "collectionFormFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getCollectionFormFileIds()== null) {
                    draft.setCollectionFormFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getCollectionFormFileIds().addAll(ids);
                }
            }
            fieldname = "technicalDataFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getTechnicalDataFileIds()== null) {
                    draft.setTechnicalDataFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getTechnicalDataFileIds().addAll(ids);
                }
            }
            fieldname = "revenueAgencyDelegationFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getRevenueAgencyDelegationFileIds()== null) {
                    draft.setRevenueAgencyDelegationFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getRevenueAgencyDelegationFileIds().addAll(ids);
                }
            }
            fieldname = "revenueAgencyFormFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getRevenueAgencyFormFileIds()== null) {
                    draft.setRevenueAgencyFormFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getRevenueAgencyFormFileIds().addAll(ids);
                }
            }
            fieldname = "selfDeclarationFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getSelfDeclarationFileIds()== null) {
                    draft.setSelfDeclarationFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getSelfDeclarationFileIds().addAll(ids);
                }
            }
            
            fieldname = "adminFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getAdminFileIds()== null) {
                    draft.setAdminFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getAdminFileIds().addAll(ids);
                }
            }
        
        }
        
        // save
        try {
            InvoicediscountDao.updateInvoicediscount(draft);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        
        return "ok";
    };
    
    public static Route invoicediscounts_add_save = (Request request, Response response) -> {
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        user = UserDao.loadUser(user.getId());
        
        // draft
        Invoicediscount draft = InvoicediscountDao.loadDraftInvoicediscount(user.getId());
        if (draft == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        // merge
        Map<String, String> params = PojoUtils.paramsFromRequest(request);
        PojoUtils.mergeFromParams(params, draft);
        
        if (StringUtils.isBlank(draft.getService())) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Servizio non selezionato");
        }
        
        int creditToSave = ServiceType.valueOf(draft.getService()).getCredit();
        
        if (creditToSave > user.getCredit()) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Crediti non sufficienti");
        }
        
        // data
        draft.setStatus(StatusType.opened.toString());
        draft.setLastStatusUpdate(TimeUtils.now());
        draft.setDate(TimeUtils.now());

        // protocol
        String protocolKey = "invoicediscount-protocol";
        int next = CounterDao.next(protocolKey);
        draft.setProtocol("" + next);
        
        // save
        try {
            InvoicediscountDao.updateInvoicediscount(draft);
            
            Integer credit = user.getCredit() != null ? user.getCredit() : 0;
            credit -= creditToSave;
            user.setCredit(credit);
            UserDao.updateUser(user);
            Manager.putSession(token, "user", user);

            // INVIARE MAIL A SOGENIT
            if (NotificationCommons.notifyInvoicediscountReceive(request, draft)) {
                // ...
            }
            if (draft.getUserId() != null) {
                User invoicediscountUser = UserDao.loadUser(draft.getUserId());
                if (NotificationCommons.notifyInvoicediscountReceiveToUser(request, draft, invoicediscountUser)) {
                    // ...
                }

            }
            
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        
        return "ok";
    };
    
    public static Route invoicediscounts_add_fileid_remove = (Request request, Response response) -> {
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        user = UserDao.loadUser(user.getId());
        
        // draft
        Invoicediscount draft = InvoicediscountDao.loadDraftInvoicediscount(user.getId());
        if (draft == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        // list
        String listName = request.queryParams("listName");
        if (StringUtils.isEmpty(listName)) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        // id
        ObjectId fileId = ParamUtils.toObjectId(request.queryParams("fileId"));
        if (fileId == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        List<ObjectId> ids = null;

        switch (listName) {
            case "identityDocumentFileIds":
                ids = draft.getIdentityDocumentFileIds();
                break;
            case "invoiceFileIds":
                ids = draft.getInvoiceFileIds();
                break;
            case "bankTransferFileIds":
                ids = draft.getBankTransferFileIds();
                break;
            case "collectionFormFileIds":
                ids = draft.getCollectionFormFileIds();
                break;
            case "revenueAgencyDelegationFileIds":
                ids = draft.getRevenueAgencyDelegationFileIds();
                break;
            case "revenueAgencyFormFileIds":
                ids = draft.getRevenueAgencyFormFileIds();
                break;
            case "selfDeclarationFileIds":
                ids = draft.getSelfDeclarationFileIds();
                break;
            case "technicalDataFileIds":
                ids = draft.getTechnicalDataFileIds();
                break;
            case "adminFileIds":
                ids = draft.getAdminFileIds();
                break;
            default:
                break;
        }
        if (ids == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        // remove id
        Iterator<ObjectId> iter = ids.iterator();
        while (iter.hasNext()) {
            if (iter.next().equals(fileId)) {
                iter.remove();
                break;
            }
        }
        
        // manca rimozione lista
        // ??????
        
        // save
        try {
            InvoicediscountDao.updateInvoicediscount(draft);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        
        return "ok";
    };
    
    public static TemplateViewRoute invoicediscount_edit = (Request request, Response response) -> {
        
        ObjectId invoicediscountId = ParamUtils.toObjectId(request.queryParams("invoicediscountId"));
        
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN) ;
            return Manager.renderEmpty();
        }

        Invoicediscount invoicediscount = null;
        if (invoicediscountId != null) {
            invoicediscount = InvoicediscountDao.loadInvoicediscount(invoicediscountId);
        }
        attributes.put("invoicediscount", invoicediscount);
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));
        
        List<User> userList = UserDao.loadSystemUserList();
        attributes.put("userList", userList);

        return Manager.render(Templates.INVOICEDISCOUNT_EDIT, attributes, RouteUtils.pathType(request));
    };
    
    public static Route invoicediscount_edit_save = (Request request, Response response) -> {
        // params
        ObjectId invoicediscountId = ParamUtils.toObjectId(request.queryParams("invoicediscountId"));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }
        
        // merge
        Map<String, List<PostedFile>> files = new HashMap<>();
        Map<String, String> params = PojoUtils.paramsFromRequest(request, null, files);
        
        if (invoicediscountId != null) {
            // params
            Invoicediscount invoicediscount = InvoicediscountDao.loadInvoicediscount(invoicediscountId);
            if (invoicediscount != null) {
                Boolean sendDocumentMail = false;
                Boolean sendFinalMail = false;
                Boolean sendAssignedMail = false;
                Boolean sendUpdateStatusMail = false;
                
                if (StringUtils.isNotBlank(params.get("status"))) {
                    if (!StringUtils.equalsIgnoreCase(invoicediscount.getStatus(), params.get("status"))) {
                        sendUpdateStatusMail = true;
                    }
                    invoicediscount.setStatus(params.get("status"));
                }
                if (ParamUtils.toObjectId(params.get("assignedUserId")) != null) {
                    if (invoicediscount.getAssignedUserId() != null) {
                        if (!invoicediscount.getAssignedUserId().equals(ParamUtils.toObjectId(params.get("assignedUserId")))) {
                            sendAssignedMail = true;
                        }
                    } else {
                        sendAssignedMail = true;
                    }
                    invoicediscount.setAssignedUserId(ParamUtils.toObjectId(params.get("assignedUserId")));
                }
                
                invoicediscount.setNote(params.get("note"));
                
                if (!files.isEmpty()) {
                    String fieldname = "adminFileIds";
                    if (files.containsKey(fieldname)) {
                        if (invoicediscount.getAdminFileIds()== null) {
                            invoicediscount.setAdminFileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            invoicediscount.getAdminFileIds().addAll(ids);
//                            sendFinalMail = true;
                        }
                    }

                }  
                                
                // save
                try {
                    InvoicediscountDao.updateInvoicediscount(invoicediscount);
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                
                // notify
                if (sendFinalMail) {
                    if (NotificationCommons.notifyInvoicediscountFinalUpload(request, invoicediscount)) {
                        // ...
                    }
                }
                if (sendDocumentMail) {
                    if (NotificationCommons.notifyInvoicediscountUpload(request, invoicediscount)) {
                        // ...
                    }
                }
                if (sendAssignedMail) {
                    if (invoicediscount.getUserId() != null) {
                        User userAssigned = UserDao.loadUser(invoicediscount.getAssignedUserId());
                        if (NotificationCommons.notifyInvoicediscountAssigned(request, invoicediscount, userAssigned)) {
                            // ...
                        }
                    }
                }
                if (sendUpdateStatusMail) {
                    if (invoicediscount.getUserId() != null) {
                        User invoicediscountUser = UserDao.loadUser(invoicediscount.getUserId());
                        if (NotificationCommons.notifyInvoicediscount(request, invoicediscount, invoicediscountUser)) {
                            // ...
                        }
                    }
                }
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        // files
        
        

        return "ok";        

    };  
    
    public static Route invoicediscount_edit_fileid_remove = (Request request, Response response) -> {
        
        // params
        ObjectId invoicediscountId = ParamUtils.toObjectId(request.queryParams("invoicediscountId"));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }
        
        // list
        String listName = request.queryParams("listName");
        if (StringUtils.isEmpty(listName)) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        if (invoicediscountId != null) {
            Invoicediscount invoicediscount = InvoicediscountDao.loadInvoicediscount(invoicediscountId);
            if (invoicediscount != null) {
                // id
                ObjectId fileId = ParamUtils.toObjectId(request.queryParams("fileId"));
                if (fileId == null) {
                    throw Spark.halt(HttpStatus.BAD_REQUEST_400);
                }

                List<ObjectId> ids = null;
                switch (listName) {
                    case "adminFileIds":
                        ids = invoicediscount.getAdminFileIds();
                        break;
                    default:
                        break;
                }
                if (ids == null) {
                    throw Spark.halt(HttpStatus.BAD_REQUEST_400);
                }

                // remove id
                Iterator<ObjectId> iter = ids.iterator();
                while (iter.hasNext()) {
                    if (iter.next().equals(fileId)) {
                        iter.remove();
                        break;
                    }
                }

                // manca rimozione lista
                // ??????

                // save
                try {
                    InvoicediscountDao.updateInvoicediscount(invoicediscount);
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }

            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }

        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return "ok";        
    };
    
    public static Route invoicediscount_remove = (Request request, Response response) -> {
        
        ObjectId invoicediscountId = ParamUtils.toObjectId(request.queryParams("invoicediscountId"));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        
        if (invoicediscountId != null) {
            // params
            Invoicediscount invoicediscount = InvoicediscountDao.loadInvoicediscount(invoicediscountId);
            if (invoicediscount != null) {
                InvoicediscountDao.updateInvoicediscountCancelled(invoicediscountId, true);
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
                    
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return "ok";        
    };          
    
    public static Route invoicediscount_status_update = (Request request, Response response) -> {
        
        ObjectId invoicediscountId = ParamUtils.toObjectId(request.queryParams("invoicediscountId"));
        String status = request.queryParams("status");
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        
        if (invoicediscountId != null) {
            // params
            Invoicediscount invoicediscount = InvoicediscountDao.loadInvoicediscount(invoicediscountId);
            if (invoicediscount != null) {
                invoicediscount.setStatus(status);
                InvoicediscountDao.updateInvoicediscount(invoicediscount);
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
            // notify
            if (invoicediscount.getUserId() != null) {
                User invoicediscountUser = UserDao.loadUser(invoicediscount.getUserId());
                if (NotificationCommons.notifyInvoicediscount(request, invoicediscount, invoicediscountUser)) {
                    // ...
                }
            }
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return "ok";        
    };



    ////////////
    // internals

    private static List<ObjectId> insertFiles(List<PostedFile> posteds) {
        if (posteds == null) {
            return null;
        }
        if (posteds.isEmpty()) {
            return null;
        }
        List<ObjectId> ids = new ArrayList<>();
        for (PostedFile posted : posteds) {
            
            ObjectId oid = null;
            try {
                
                // filename
                String filename = FileDao.composeFilename(FileType.attachment, posted.getExtension());
                
                // save file
                File fll = new File(posted.getFilename());
                oid = FileDao.insertFile(filename, 
                        posted.getName(),
                        posted.getContentType(), 
                        FileUtils.readFileToByteArray(fll)
                );
                
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }

            if (oid != null) {
                ids.add(oid);
            }
        }
        return ids;
    }
    
}
