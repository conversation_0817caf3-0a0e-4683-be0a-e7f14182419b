package com.miocontotermico.user;

import com.miocontotermico.commons.NotificationCommons;
import com.miocontotermico.commons.ProcedureCommons;
import com.miocontotermico.core.Manager;
import com.miocontotermico.core.Paths;
import com.miocontotermico.core.Templates;
import com.miocontotermico.dao.ProcedureDao;
import com.miocontotermico.dao.UserDao;
import com.miocontotermico.login.PasswordHash;
import com.miocontotermico.pojo.Procedure;
import com.miocontotermico.pojo.User;
import com.miocontotermico.pojo.types.ProfileType;
import com.miocontotermico.util.ParamUtils;
import com.miocontotermico.util.PojoUtils;
import com.miocontotermico.util.RouteUtils;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class UserController {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(UserController.class.getName());

    public static TemplateViewRoute users = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN) ;
            return Manager.renderEmpty();
        }
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));
        
        List<User> userList = UserDao.loadUserList();
        attributes.put("userList", userList);

        return Manager.render(Templates.USERS, attributes, RouteUtils.pathType(request));
    };
    
    public static TemplateViewRoute user_detail = (Request request, Response response) -> {
        
        ObjectId userId = ParamUtils.toObjectId(request.queryParams("userId"));
        
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN) ;
            return Manager.renderEmpty();
        }

        User userRegistered = null;
        List<Procedure> procedureList = null;
        if (userId != null) {
            userRegistered = UserDao.loadUser(userId);
            procedureList = ProcedureDao.loadProcedureListByUserId(userId);
        }
        attributes.put("userRegistered", userRegistered);
        attributes.put("procedureList", ProcedureCommons.toEntries(procedureList));
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        return Manager.render(Templates.USER_DETAIL, attributes, RouteUtils.pathType(request));
    };
    
    public static Route user_status_update = (Request request, Response response) -> {
        
        ObjectId userId = ParamUtils.toObjectId(request.queryParams("userId"));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        
        if (userId != null) {
            // params
            User userTmp = UserDao.loadUser(userId);
            if (userTmp != null) {
                userTmp.setActive(BooleanUtils.isFalse(userTmp.getActive()));
                UserDao.updateUser(userTmp);
                
                if (BooleanUtils.isTrue(userTmp.getActive())) {
                    if (NotificationCommons.notifyConfirmUser(request, userTmp)) {
                        // ...
                    }
                }
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }

        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return "ok";        
    };   
    
    public static Route user_remove = (Request request, Response response) -> {
        
        ObjectId userId = ParamUtils.toObjectId(request.queryParams("userId"));
        Integer credit = NumberUtils.toInt(request.queryParams("credit"), 0);
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            Spark.halt(HttpStatus.UNAUTHORIZED_401);
            return null;
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            Spark.halt(HttpStatus.UNAUTHORIZED_401);
            return null;
        }
        
        if (userId != null && credit != null) {
            // params
            User userRegistered = UserDao.loadUser(userId);

            if (userRegistered != null) {
                userRegistered.setCredit(credit);
                UserDao.updateUser(userRegistered);
            } else {
                Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
                    
        } else {
            Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return "ok";        
    };      
    

    public static TemplateViewRoute system_users = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN) ;
            return Manager.renderEmpty();
        }
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));
        
        List<User> userList = UserDao.loadUserList();
        attributes.put("userList", userList);

        return Manager.render(Templates.SYSTEM_USERS, attributes, RouteUtils.pathType(request));
    };
        
    public static TemplateViewRoute system_users_add = (Request request, Response response) -> {
        
        ObjectId userId = ParamUtils.toObjectId(request.queryParams("userId"));
        
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN) ;
            return Manager.renderEmpty();
        }

        User userRegistered = null;
        if (userId != null) {
            userRegistered = UserDao.loadUser(userId);
        }
        attributes.put("userRegistered", userRegistered);
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        return Manager.render(Templates.SYSTEM_USERS_ADD, attributes, RouteUtils.pathType(request));
    };
    
    public static Route system_users_add_save = (Request request, Response response) -> {

        // params
        User userTmp = PojoUtils.createFromRequest(request, User.class);
        String password = request.queryParams("password");
        String passwordConfirm = request.queryParams("password-confirm");
        String profileType = request.queryParams("profileType");

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN) ;
            return Manager.renderEmpty();
        }

        if (StringUtils.isNotBlank(userTmp.getUsername())) {
            User userExist = UserDao.loadUserByUsername(userTmp.getUsername());
            ObjectId userId = null;
            if (userExist == null) {
                if (StringUtils.isNotBlank(password) && StringUtils.equals(password, passwordConfirm)) {
                    userTmp.setPassword(PasswordHash.createHash(password));
                    userTmp.setProfileType(profileType);
                    userTmp.setEmail(userTmp.getUsername());
                    userId = UserDao.insertUser(userTmp);
                    
                } else {
                    response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.SYSTEM_USERS));
                    return null;
                }
            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.SYSTEM_USERS));
            }
            if (userId != null) {
                // navigation
                response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.SYSTEM_USERS));
            }
        } else {
            response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.SYSTEM_USERS));
        }

        return null;
    };
    
    public static TemplateViewRoute system_user_edit = (Request request, Response response) -> {
        
        ObjectId userId = ParamUtils.toObjectId(request.queryParams("userId"));
        
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN) ;
            return Manager.renderEmpty();
        }

        User userRegistered = null;
        if (userId != null) {
            userRegistered = UserDao.loadUser(userId);
        }
        attributes.put("userRegistered", userRegistered);
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        return Manager.render(Templates.SYSTEM_USER_EDIT, attributes, RouteUtils.pathType(request));
    };
    
    
    
}
