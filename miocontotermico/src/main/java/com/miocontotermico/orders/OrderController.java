package com.miocontotermico.orders;

import com.miocontotermico.commons.NotificationCommons;
import com.miocontotermico.commons.OrderCommons;
import com.miocontotermico.core.Manager;
import com.miocontotermico.core.Paths;
import com.miocontotermico.core.Templates;
import com.miocontotermico.dao.UserDao;
import com.miocontotermico.order.OrderDao;
import com.miocontotermico.pojo.Order;
import com.miocontotermico.pojo.User;
import com.miocontotermico.pojo.types.PaymentStatusType;
import com.miocontotermico.pojo.types.ProfileType;
import com.miocontotermico.pojo.types.StatusType;
import com.miocontotermico.support.datatable.Datatable;
import com.miocontotermico.util.ParamUtils;
import com.miocontotermico.util.RouteUtils;
import com.miocontotermico.util.TimeUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class OrderController {

    private static final Logger LOGGER = LoggerFactory.getLogger(OrderController.class.getName());

    public static TemplateViewRoute orders = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.azienda.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString()) && 
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.privato.toString()) && 
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.operatore.toString())) {
            response.redirect(RouteUtils.contextPath(request) + Paths.HOME);
            return Manager.renderEmpty();
        }
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // date filter
        Date startDate = DateUtils.addMonths(new Date(), -1);
        Date endDate = TimeUtils.today();
        
        if (StringUtils.isNotBlank(request.queryParams("startDate"))) {
            Date tmp = TimeUtils.toDate(request.queryParams("startDate"));
            if (tmp != null) {
                startDate = tmp;
            }
        }
        if (StringUtils.isNotBlank(request.queryParams("endDate"))) {
            Date tmp = TimeUtils.toDate(request.queryParams("endDate"));
            if (tmp != null) {
                endDate = tmp;
            }
        }
        
        attributes.put("startDate", startDate);
        attributes.put("endDate", endDate);
        
        String[] selectedStatuses = null;
        if (StringUtils.isNotBlank(request.queryParams("selectedStatuses"))) {
            selectedStatuses = StringUtils.split(request.queryParams("selectedStatuses"), "|");
        }
        attributes.put("selectedStatuses", selectedStatuses);
        
        // payment statuses filter
        String[] selectedPaymentStatuses = null;
        if (StringUtils.isNotBlank(request.queryParams("selectedPaymentStatuses"))) {
            selectedPaymentStatuses = ParamUtils.toStrings(request.queryParams("selectedPaymentStatuses"));
        }
        attributes.put("selectedPaymentStatuses", selectedPaymentStatuses);
        
        // payment statuses
        String[] paymentStatusList = new String[] {
            "-",
            PaymentStatusType.paid.toString()
        };
        attributes.put("paymentStatusList", paymentStatusList);
        
        return Manager.render(Templates.ORDERS, attributes, RouteUtils.pathType(request));
    };

    public static Route orders_data = (Request request, Response response) -> {
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.azienda.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString()) && 
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.privato.toString()) && 
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.operatore.toString())) {
            response.redirect(RouteUtils.contextPath(request) + Paths.HOME);
            return Manager.renderEmpty();
        }
        
        // order (optional) filters
        Date startDate = DateUtils.addMonths(new Date(), -1);
        Date endDate = TimeUtils.today();
        
        if (StringUtils.isNotBlank(request.queryParams("startDate"))) {
            Date tmp = TimeUtils.toDate(request.queryParams("startDate"));
            if (tmp != null) {
                startDate = tmp;
            }
        }
        if (StringUtils.isNotBlank(request.queryParams("endDate"))) {
            Date tmp = TimeUtils.toDate(request.queryParams("endDate"));
            if (tmp != null) {
                endDate = tmp;
            }
        }
        
        String[] selectedPaymentStatuses = null;
        if (StringUtils.isNotBlank(request.queryParams("selectedPaymentStatuses"))) {
            selectedPaymentStatuses = ParamUtils.toStrings(request.queryParams("selectedPaymentStatuses"));
        }
        
        // orders
        List<Order> ords;
        if (StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            ords = OrderDao.loadOrderListByDateRange(startDate, endDate);
        } else {
            ords = OrderDao.loadOrderListByDateRangeAndUserId(startDate, endDate, user.getId());
        }
        
        List<OrderEntry> orderList = null;
        if ((ords != null) &&
            (ords.size() > 0)) {
            for (Order order : ords) {
                boolean add = true;
                
                if ((selectedPaymentStatuses != null) &&
                        (selectedPaymentStatuses.length > 0)) {
                    // all payment statuses means, actually, no filter
                    if (selectedPaymentStatuses.length < (PaymentStatusType.values().length + 1)) {
                        if (StringUtils.isBlank(order.getPaymentStatus())) {
                            if (ArrayUtils.indexOf(selectedPaymentStatuses, "-") < 0) {
                                add = false;
                            }
                        }
                        if (StringUtils.equalsIgnoreCase(order.getPaymentStatus(), PaymentStatusType.installment.toString())) {
                            if (ArrayUtils.indexOf(selectedPaymentStatuses, PaymentStatusType.installment.toString()) < 0) {
                                add = false;
                            }
                        }
                        if (StringUtils.equalsIgnoreCase(order.getPaymentStatus(), PaymentStatusType.paid.toString())) {
                            if (ArrayUtils.indexOf(selectedPaymentStatuses, PaymentStatusType.paid.toString()) < 0) {
                                add = false;
                            }
                        }
                    }
                }
                
                OrderEntry entry = OrderCommons.toEntry(order);

                if (add) {
                    if (orderList == null) {
                        orderList = new ArrayList<>();
                    }
                    orderList.add(entry);
                }

            }
        }

        // result
        Datatable table = new Datatable();
        table.setData(orderList != null ? orderList.toArray() : (new OrderEntry[0]));
        
        return table;
    };

    public static TemplateViewRoute order_view = (Request request, Response response) -> {
        // params
        ObjectId orderId = ParamUtils.toObjectId(request.queryParams("orderId"));

        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.azienda.toString()) &&
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString()) && 
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.privato.toString()) && 
                !StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.operatore.toString())) {
            response.redirect(RouteUtils.contextPath(request) + Paths.HOME);
            return Manager.renderEmpty();
        }

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // order
        OrderEntry orderEntry = OrderCommons.toEntry(OrderDao.loadOrder(orderId));
        attributes.put("order", orderEntry);

        return Manager.render(Templates.ORDER_VIEW, attributes, RouteUtils.pathType(request));
    };
    
    public static Route order_status_update = (Request request, Response response) -> {
        
        ObjectId orderId = ParamUtils.toObjectId(request.queryParams("orderId"));
        String status = request.queryParams("status");
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.contextPath(request) + Paths.HOME);
            return Manager.renderEmpty();
        }
        
        if (orderId != null) {
            // params
            Order order = OrderDao.loadOrder(orderId);
            if (order != null) {
                order.setStatus(status);
                order.setLastStatusUpdate(new Date());
                
                // optional payment status
                if (StringUtils.isNotBlank(request.queryParams("paymentStatus"))) {
                    String paymentStatus = ParamUtils.lineToNull(ParamUtils.emptyToNull(request.queryParams("paymentStatus")));
                    order.setPaymentStatus(paymentStatus);
                    if (StringUtils.isNotBlank(paymentStatus)) {
                        order.setPaymentDate(TimeUtils.now());
                    } else {
                        order.setPaymentDate(null);
                    }
                }
                
                OrderDao.updateOrder(order);
                order = OrderDao.loadOrder(orderId);
                // notify
                if (StringUtils.equalsIgnoreCase(request.queryParams("paymentStatus"), "paid")) {
                    NotificationCommons.notifyOrderStatus(request, order);                    
                }
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
                    
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return "ok";        
    };  
    
    public static Route order_payment_status_update = (Request request, Response response) -> {
        
        ObjectId orderId = ParamUtils.toObjectId(request.queryParams("orderId"));
        String paymentStatus = ParamUtils.lineToNull(ParamUtils.emptyToNull(request.queryParams("paymentStatus")));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.contextPath(request) + Paths.HOME);
            return Manager.renderEmpty();
        }
        
        if (orderId != null) {
            // params
            Order order = OrderDao.loadOrder(orderId);
            boolean removeCredit = false;
            if (order != null) {
                if (StringUtils.isNotBlank(order.getPaymentStatus())) {
                    removeCredit = true;
                }

                order.setPaymentStatus(paymentStatus);
                if (StringUtils.isNotBlank(paymentStatus)) {
                    order.setPaymentDate(TimeUtils.now());
                    User userToUpdate = UserDao.loadUser(order.getUserId());
                    Integer credit = userToUpdate.getCredit() != null ? userToUpdate.getCredit() : 0;
                    credit += order.getCredit();
                    userToUpdate.setCredit(credit);
                    UserDao.updateUser(userToUpdate);
                } else {
                    if (removeCredit) {
                        User userToUpdate = UserDao.loadUser(order.getUserId());
                        Integer credit = userToUpdate.getCredit() != null ? userToUpdate.getCredit() : 0;
                        if (credit > 0) {
                            credit -= order.getCredit();
                            if (credit < 0) {
                                credit = 0;
                            }
                            userToUpdate.setCredit(credit);
                            UserDao.updateUser(userToUpdate);                        
                        }
                    }
                    order.setPaymentDate(null);
                }
                OrderDao.updateOrder(order);
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
                    
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return "ok";        
    };  
    
}
