package com.miocontotermico.payment.paypal;

import com.miocontotermico.core.Paths;
import com.miocontotermico.pojo.PaymentPlatform;
import com.miocontotermico.pojo.User;
import com.miocontotermico.pojo.types.PaymentEnvironmentType;
import com.miocontotermico.pojo.types.PaymentType;
import com.miocontotermico.util.RouteUtils;
import com.paypal.core.PayPalEnvironment;
import com.paypal.core.PayPalHttpClient;
import com.paypal.http.HttpResponse;
import com.paypal.orders.AmountWithBreakdown;
import com.paypal.orders.ApplicationContext;
import com.paypal.orders.LinkDescription;
import com.paypal.orders.Order;
import com.paypal.orders.OrderRequest;
import com.paypal.orders.OrdersCaptureRequest;
import com.paypal.orders.OrdersCreateRequest;
import com.paypal.orders.PurchaseUnitRequest;
import java.io.IOException;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.jetty.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;

/**
 *
 * <AUTHOR>
 */
public class PaypalGateway {

    private static final Logger LOGGER = LoggerFactory.getLogger(PaypalGateway.class.getName());
    
    public static PaymentInitResponse createOrder(Request request, PaymentPlatform platform, String transactionKey, User user, com.miocontotermico.pojo.Order cart, Double finalPrice) {
        if (request == null) {
            return null;
        }
        if (platform == null) {
            return null;
        }
        if (StringUtil.isBlank(transactionKey)) {
            return null;
        }

        finalPrice = (finalPrice != null) ? finalPrice : 0D;
        
        PaymentEnvironmentType environmentType = EnumUtils.getEnum(PaymentEnvironmentType.class, platform.getEnvironmentType());
        String clientId = platform.getAlias();
        String clientSecret = platform.getSecretKey();
                
        
        if (environmentType == null) {
            return null;
        }
        if (StringUtil.isBlank(clientId)) {
            return null;
        }
        if (StringUtil.isBlank(clientSecret)) {
            return null;
        }
        
        // environment
        PayPalEnvironment environment;
        if (environmentType == PaymentEnvironmentType.live) {
            environment = new PayPalEnvironment.Live(
                    clientId,
                    clientSecret
            );
        } else {
            environment = new PayPalEnvironment.Sandbox(
                    clientId,
                    clientSecret
            );
        }
        
        // client
        PayPalHttpClient client;
        try {
            client = new PayPalHttpClient(environment);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
            return null;
        }
        String urlOk = RouteUtils.baseUrl(request) + Paths.PAID + "?confirmed=true&paymentType=" + PaymentType.paypal + "&transactionKey=" + transactionKey + "&orderId=" + cart.getId();
        String urlKo = RouteUtils.baseUrl(request) + Paths.PAID + "?confirmed=false&paymentType=" + PaymentType.paypal + "&transactionKey=" + transactionKey + "&orderId=" + cart.getId();
        
        
        HttpResponse<Order> order = null;
		try {
			order = createOrderWithMinimumPayload(client, finalPrice, urlOk, urlKo, true);
		} catch (com.paypal.http.exceptions.HttpException ex) {
            LOGGER.error("suppressed", ex);
		} catch (IOException | RuntimeException ex) {
            LOGGER.error("suppressed", ex);
		}
        
        /*
        SAMPLE DEBUG LOG
        
        Order with Minimum Payload: 
        Status Code: 201
        Status: CREATED
        Order ID: 0DY90215JD589761V
        Intent: CAPTURE
        Links: 
            self: https://api.sandbox.paypal.com/v2/checkout/orders/0DY90215JD589761V	Call Type: GET
            approve: https://www.sandbox.paypal.com/checkoutnow?token=0DY90215JD589761V	Call Type: GET
            update: https://api.sandbox.paypal.com/v2/checkout/orders/0DY90215JD589761V	Call Type: PATCH
            capture: https://api.sandbox.paypal.com/v2/checkout/orders/0DY90215JD589761V/capture	Call Type: POST
        Total Amount: EUR 1234.56
        Full response body:
        com.paypal.orders.Order@6771beb3
        */
        
        PaymentInitResponse payment = null;
        if (order != null) {
            if (order.result() != null) {
                if ((order.result().links() != null) && (order.result().id() != null)) {
                    for (LinkDescription link : order.result().links()) {
                        if (StringUtils.equalsIgnoreCase(link.rel(), "approve")) {
                            if (StringUtils.isNotBlank(order.result().id()) && StringUtils.isNotBlank(link.href())) {
                                payment = new PaymentInitResponse();
                                payment.setPaymentID(order.result().id());
                                payment.setPaymentURL(link.href());
                            }
                            break;
                        }
                    }
                }
            }
        }
        
        return payment;
    }
    
    public static boolean captureOrder(Request request, PaymentPlatform platform, String transactionKey, User user, com.miocontotermico.pojo.Order cart, Double finalPrice) {
        if (request == null) {
            return false;
        }
        if (platform == null) {
            return false;
        }
        if (StringUtil.isBlank(transactionKey)) {
            return false;
        }
        if (user == null) {
            return false;
        }

        finalPrice = (finalPrice != null) ? finalPrice : 0D;
        
        PaymentEnvironmentType environmentType = EnumUtils.getEnum(PaymentEnvironmentType.class, platform.getEnvironmentType());
        String clientId = platform.getAlias();
        String clientSecret = platform.getSecretKey();
        String orderId = cart.getExternalPaymentKey();
                
        
        if (environmentType == null) {
            return false;
        }
        if (StringUtil.isBlank(clientId)) {
            return false;
        }
        if (StringUtil.isBlank(clientSecret)) {
            return false;
        }
        if (StringUtil.isBlank(orderId)) {
            return false;
        }
        
        // environment
        PayPalEnvironment environment;
        if (environmentType == PaymentEnvironmentType.live) {
            environment = new PayPalEnvironment.Live(
                    clientId,
                    clientSecret
            );
        } else {
            environment = new PayPalEnvironment.Sandbox(
                    clientId,
                    clientSecret
            );
        }
        
        // client
        PayPalHttpClient client;
        try {
            client = new PayPalHttpClient(environment);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
            return false;
        }
        
        String urlOk = RouteUtils.baseUrl(request) + Paths.PAID + "?paymentType=" + PaymentType.paypal + "&transactionKey=" + transactionKey + "&domainId=" + cart.getId();
        String urlKo = RouteUtils.baseUrl(request) + Paths.PAID + "?paid=false&paymentType=" + PaymentType.paypal + "&transactionKey=" + transactionKey + "&domainId=" + cart.getId();
        
        
        HttpResponse<Order> order = null;
        try {
            order = captureOrderWithMinimumPayload(client, orderId, finalPrice, urlOk, urlKo, true);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        
        /*
        SAMPLE DEBUG LOG
        
        Status Code: 201
        Status: COMPLETED
        Order ID: 8UP857636M664844U
        Links: 
            self: https://api.sandbox.paypal.com/v2/checkout/orders/8UP857636M664844U	Call Type: GET
        Full response body:
        com.paypal.orders.Order@2fe2fc2a
        */
        
        boolean done = false;
        if (order != null) {
            if (order.result() != null) {
                if (StringUtils.equalsIgnoreCase(order.result().status(), "COMPLETED")) {
                    done = true;
                }
            }
        }
        
        return done;
    }

    public static HttpResponse<Order> createOrderWithMinimumPayload(PayPalHttpClient client, Double finalPrice, String urlOk, String urlKo, boolean debug) throws IOException {
    if (client == null) {
        return null;
    }
    if (finalPrice == null) {
        return null;
    }

            OrdersCreateRequest request = new OrdersCreateRequest();
            request.header("prefer","return=representation");
            request.requestBody(buildMinimumRequestBody(finalPrice, urlOk, urlKo));
            HttpResponse<Order> response = client.execute(request);
            if (debug) {
                    if (response.statusCode() == 201) {
                            System.out.println("Order with Minimum Payload: ");
                            System.out.println("Status Code: " + response.statusCode());
                            System.out.println("Status: " + response.result().status());
                            System.out.println("Order ID: " + response.result().id());
                            System.out.println("Intent: " + response.result().checkoutPaymentIntent());
                            System.out.println("Links: ");
                            for (LinkDescription link : response.result().links()) {
                                    System.out.println("\t" + link.rel() + ": " + link.href() + "\tCall Type: " + link.method());
                            }
                            System.out.println("Total Amount: " + response.result().purchaseUnits().get(0).amountWithBreakdown().currencyCode()
                                            + " " + response.result().purchaseUnits().get(0).amountWithBreakdown().value());
                            System.out.println("Full response body:");
                            System.out.println(response.result());
                    }
            }
            return (response.statusCode() == 201) ? response : null;
    }

    public static HttpResponse<Order> captureOrderWithMinimumPayload(PayPalHttpClient client, String orderId, Double finalPrice, String urlOk, String urlKo, boolean debug) throws IOException {
    if (client == null) {
        return null;
    }
    if (StringUtil.isBlank(orderId)) {
        return null;
    }
    if (finalPrice == null) {
        return null;
    }

            OrdersCaptureRequest request = new OrdersCaptureRequest(orderId);
            request.requestBody(buildMinimumRequestBody(finalPrice, urlOk, urlKo));
            HttpResponse<Order> response = client.execute(request);
            if (debug) {
                    System.out.println("Status Code: " + response.statusCode());
                    System.out.println("Status: " + response.result().status());
                    System.out.println("Order ID: " + response.result().id());
                    System.out.println("Links: ");
                    for (LinkDescription link : response.result().links()) {
                            System.out.println("\t" + link.rel() + ": " + link.href() + "\tCall Type: " + link.method());
                    }
                    System.out.println("Full response body:");
                    System.out.println(response.result());
            }
            return (response.statusCode() == 201) ? response : null;
    }

    private static OrderRequest buildMinimumRequestBody(Double finalPrice, String urlOk, String urlKo) {
    if (finalPrice == null) {
        return null;
    }
    if (StringUtils.isBlank(urlOk)) {
        return null;
    }
    if (StringUtils.isBlank(urlKo)) {
        return null;
    }

            OrderRequest orderRequest = new OrderRequest();
            orderRequest.checkoutPaymentIntent("CAPTURE");
            ApplicationContext applicationContext = new ApplicationContext()
                            .cancelUrl(urlKo)
            .returnUrl(urlOk);
            orderRequest.applicationContext(applicationContext);
            List<PurchaseUnitRequest> purchaseUnitRequests = new ArrayList<>();
            PurchaseUnitRequest purchaseUnitRequest = new PurchaseUnitRequest()
                            .amountWithBreakdown(new AmountWithBreakdown().currencyCode("EUR").value(formatPrice(finalPrice)));
            purchaseUnitRequests.add(purchaseUnitRequest);
            orderRequest.purchaseUnits(purchaseUnitRequests);

            return orderRequest;
    }
    
    private static String formatPrice(Double price) {
        if (price == null) {
            return null;
        }
        DecimalFormat df = new DecimalFormat("#.00", new DecimalFormatSymbols(Locale.US));
        return df.format(price);
    }
}
