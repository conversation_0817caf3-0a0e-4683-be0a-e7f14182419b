package com.miocontotermico.payment;

import com.miocontotermico.commons.PaymentCommons;
import com.miocontotermico.core.Defaults;
import com.miocontotermico.core.Manager;
import com.miocontotermico.core.Paths;
import com.miocontotermico.core.Templates;
import com.miocontotermico.dao.PaymentPlatformDao;
import com.miocontotermico.dao.PaymentDao;
import com.miocontotermico.pojo.PaymentPlatform;
import com.miocontotermico.pojo.Payment;
import com.miocontotermico.pojo.User;
import com.miocontotermico.pojo.types.ProfileType;
import com.miocontotermico.support.image.slim.SlimImage;
import com.miocontotermico.util.ParamUtils;
import com.miocontotermico.util.PojoUtils;
import com.miocontotermico.util.RouteUtils;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class PaymentController {

    private static final Logger LOGGER = LoggerFactory.getLogger(PaymentController.class.getName());

    public static TemplateViewRoute payments = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // payments
        List<Payment> paymentList = PaymentDao.loadPaymentList();
        attributes.put("paymentList", paymentList);
        
        return Manager.render(Templates.PAYMENTS, attributes, RouteUtils.pathType(request));
    };

    public static TemplateViewRoute payments_add = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        return Manager.render(Templates.PAYMENTS_ADD, attributes, RouteUtils.pathType(request));
    };
    
    public static Route payments_add_save = (Request request, Response response) -> {
        // params
        Payment payment = PojoUtils.createFromRequest(request, Payment.class);
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        
        if (PaymentCommons.isValidPayment(payment)) {
            
            ObjectId paymentId = PaymentDao.insertPayment(payment);
            
            // image
            String slim = request.queryParams("uploaded-files");
            if (StringUtils.isNotBlank(slim)) {
                SlimImage uploaded = null;
                try {
                    uploaded = Manager.deserializeFromJson(slim, SlimImage.class);
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                if ((uploaded != null)) {
                    PaymentDao.updatePaymentImage(user.getUsername(), paymentId, uploaded);
                }
            }            

            // navigation
            boolean saveAndContinue = BooleanUtils.toBoolean(request.queryParams("saveAndContinue"));
            if (saveAndContinue) {
                response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.PAYMENTS_ADD));
            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.PAYMENTS));
            }

        } else {
            response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.PAYMENTS_ADD));
        }

        return null;
    };
    
    public static TemplateViewRoute payment_view = (Request request, Response response) -> {
        // params
        ObjectId paymentId = ParamUtils.toObjectId(request.queryParams("paymentId"));
        
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));
        
        // payment
        Payment payment = null;
        if (paymentId != null) {
            payment = PaymentDao.loadPayment(paymentId);
        }
        attributes.put("payment", payment);

        return Manager.render(Templates.PAYMENT_VIEW, attributes, RouteUtils.pathType(request));
    };
    
    public static Route payment_view_update = (Request request, Response response) -> {
        // ...tbd
        return Manager.renderEmpty();
    };
	
    public static Route payment_shoppable_update = (Request request, Response response) -> {
        
        ObjectId paymentId = ParamUtils.toObjectId(request.queryParams("paymentId"));
        Boolean shoppable = StringUtils.isNotBlank(request.queryParams("shoppable")) ? BooleanUtils.toBoolean(request.queryParams("shoppable")) : null;
        Boolean vendorShoppable = StringUtils.isNotBlank(request.queryParams("vendorShoppable")) ? BooleanUtils.toBoolean(request.queryParams("vendorShoppable")) : null;
        Boolean shopDefault = StringUtils.isNotBlank(request.queryParams("shopDefault")) ? BooleanUtils.toBoolean(request.queryParams("shopDefault")) : null;
        Boolean vendorDefault = StringUtils.isNotBlank(request.queryParams("vendorDefault")) ? BooleanUtils.toBoolean(request.queryParams("vendorDefault")) : null;

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }


        if ((paymentId != null) && (shoppable != null || vendorShoppable != null || shopDefault != null || vendorDefault != null)) {
            // params
            Payment payment = PaymentDao.loadPayment(paymentId);
            if (payment != null) {
                PaymentDao.updatePaymentShoppable(paymentId, shoppable, vendorShoppable, shopDefault, vendorDefault);
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return "ok";
    };
    
    public static Route payment_remove = (Request request, Response response) -> {
        
        ObjectId paymentId = ParamUtils.toObjectId(request.queryParams("paymentId"));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        if (paymentId != null) {
            // params
            Payment payment = PaymentDao.loadPayment(paymentId);
            if (payment != null) {
                PaymentDao.updatePaymentCancelled(paymentId, true);
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
                    
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return "ok";        
    }; 
    
    public static TemplateViewRoute payment_platforms = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // payments
        List<PaymentPlatform> paymentPlatformList = PaymentPlatformDao.loadPaymentPlatformList();
        attributes.put("paymentPlatformList", paymentPlatformList);
        
        return Manager.render(Templates.PAYMENT_PLATFORMS, attributes, RouteUtils.pathType(request));
    };
    
    public static TemplateViewRoute payment_platforms_add = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        return Manager.render(Templates.PAYMENT_PLATFORMS_ADD, attributes, RouteUtils.pathType(request));
    };
    
    public static Route payment_platforms_add_save = (Request request, Response response) -> {
        // params
        PaymentPlatform paymentPlatform = PojoUtils.createFromRequest(request, PaymentPlatform.class);
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        
        if (isValidPaymentPlatform(paymentPlatform)) {
            
            ObjectId paymentPlatformId = PaymentPlatformDao.insertPaymentPlatform(paymentPlatform);
            
            // navigation
            boolean saveAndContinue = BooleanUtils.toBoolean(request.queryParams("saveAndContinue"));
            if (saveAndContinue) {
                response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.PAYMENT_PLATFORMS_ADD));
            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.PAYMENT_PLATFORMS));
            }

        } else {
            response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.PAYMENT_PLATFORMS_ADD));
        }

        return null;
    };
    
    public static TemplateViewRoute payment_platform_view = (Request request, Response response) -> {
        // params
        ObjectId paymentPlatformId = ParamUtils.toObjectId(request.queryParams("paymentPlatformId"));
        
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));
        
        // payment
        PaymentPlatform paymentPlatform = null;
        if (paymentPlatformId != null) {
            paymentPlatform = PaymentPlatformDao.loadPaymentPlatform(paymentPlatformId);
        }
        attributes.put("paymentPlatform", paymentPlatform);

        return Manager.render(Templates.PAYMENT_PLATFORM_VIEW, attributes, RouteUtils.pathType(request));
    };
    
    public static Route payment_platform_view_update = (Request request, Response response) -> {
        
        ObjectId paymentPlatformId = ParamUtils.toObjectId(request.queryParams("paymentPlatformId"));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        if (paymentPlatformId != null) {
            PaymentPlatform paymentPlatformOld = PaymentPlatformDao.loadPaymentPlatform(paymentPlatformId);
            PaymentPlatform paymentPlatform = PojoUtils.mergeFromRequest(request, paymentPlatformOld);

            if (isValidPaymentPlatform(paymentPlatform)) {
                PaymentPlatformDao.updatePaymentPlatform(paymentPlatform);

                // navigation
                response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.PAYMENT_PLATFORMS));

            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.PAYMENT_PLATFORM_VIEW) + "?paymentPlatformId=" + paymentPlatform.getId());
            }   
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }        
        
        return Manager.renderEmpty();
    };
	

    public static Route payment_platform_remove = (Request request, Response response) -> {
        
        ObjectId paymentPlatformId = ParamUtils.toObjectId(request.queryParams("paymentPlatformId"));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        if (paymentPlatformId != null) {
            // params
            PaymentPlatform paymentPlatform = PaymentPlatformDao.loadPaymentPlatform(paymentPlatformId);
            if (paymentPlatform != null) {
                PaymentPlatformDao.updatePaymentPlatformCancelled(paymentPlatformId, true);
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
                    
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return "ok";        
    };   
    
    private static boolean isValidPaymentPlatform(PaymentPlatform entity) {
        boolean valid = (entity != null) &&
                StringUtils.isNotBlank(entity.getPaymentType()) &&
                StringUtils.isNotBlank(entity.getAlias()) &&
                StringUtils.isNotBlank(entity.getSecretKey())
                ;
        
        if (!valid) {
            if (entity != null) {
                LOGGER.warn(
                        "paymentType " + entity.getPaymentType()+ "\n" +
                        "alias " + entity.getAlias()+ "\n" +
                        "secretKey " + entity.getSecretKey()+ "\n"
                );
            } else {
                LOGGER.warn(
                        "payment platform validation problem:\n" +
                        "- empty"
                );
            }
        }
        
        return valid;
    }
    
}
