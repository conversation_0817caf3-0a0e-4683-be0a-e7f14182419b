package com.miocontotermico.payment.nexi;

import io.github.nexipayments.sdknpg.securitytokenstorage.ISecurityTokenStorage;

import java.util.HashMap;
import java.util.Map;

public class SecurityTokenStorage implements ISecurityTokenStorage {

    private final Map<String, String> tokenMap = new HashMap<>();

    @Override
    public void store(String orderId, String securityToken) {
        // Implementa la logica per salvare il token di sicurezza
        tokenMap.put(orderId, securityToken);
    }
    
    @Override
    public boolean verifyExistence(String orderId, String securityToken) {
        // Implementa la logica di verifica se il token di sicurezza associato all'orderId esiste e coincide con quello fornito
        String storedToken = tokenMap.get(orderId);
        return storedToken != null && storedToken.equals(securityToken);
    }
}