package com.miocontotermico.payment.nexi;

import io.github.nexipayments.sdknpg.configuration.IConfiguration;

import java.net.MalformedURLException;
import java.net.URL;

public class ConfigurationTest implements IConfiguration {

    private final String apiKey;

    public ConfigurationTest(String apiKey) {
        this.apiKey = apiKey;
    }

    @Override
    public URL getGatewayBaseUrl() {
        try {
            return new URL("https://stg-ta.nexigroup.com/api/phoenix-0.0/psp/api/v1");  // indirizzo ambiente di test
        } catch (MalformedURLException e) {
            throw new IllegalStateException(e);
        }
    }

    @Override
    public String getApiKey() {
        return apiKey;
    }
}
