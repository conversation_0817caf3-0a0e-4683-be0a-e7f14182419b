package com.miocontotermico.payment.nexi;

import com.miocontotermico.commons.OrderCommons;
import com.miocontotermico.core.Paths;
import com.miocontotermico.pojo.PaymentPlatform;
import com.miocontotermico.pojo.User;
import com.miocontotermico.pojo.types.PaymentEnvironmentType;
import com.miocontotermico.pojo.types.PaymentType;
import com.miocontotermico.util.MoneyUtils;
import com.miocontotermico.util.RouteUtils;
import io.github.nexipayments.sdknpg.PaymentGatewayClient;
import io.github.nexipayments.sdknpg.exceptions.BaseException;
import io.github.nexipayments.sdknpg.pojo.CreateHostedOrderRequest;
import io.github.nexipayments.sdknpg.pojo.CreateHostedOrderResponse;
import io.github.nexipayments.sdknpg.pojo.Order;
import io.github.nexipayments.sdknpg.pojo.PaymentSession;
import io.github.nexipayments.sdknpg.securitytokenstorage.ISecurityTokenStorage;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;

/**
 *
 * <AUTHOR>
 */
public class NexiGateway {

    private static final Logger LOGGER = LoggerFactory.getLogger(NexiGateway.class.getName());
    
    public static String paymentInit(Request request, PaymentPlatform platform, String transactionKey, User user, com.miocontotermico.pojo.Order cart, double finalPrice) {
        
        String redirectTo = null;
        
        if ((request != null) && (platform != null) && StringUtils.isNotBlank(transactionKey) && (user != null)) {

            String endpoint = StringUtils.defaultIfBlank(platform.getEndpoint(), "");
            String alias = StringUtils.defaultIfBlank(platform.getAlias(), "");
            String secretKey = StringUtils.defaultIfBlank(platform.getSecretKey(), "");

            String requestUrl = endpoint;
            String currency = "EUR";

            double price = finalPrice * 100D;
            String value = StringUtils.substringBeforeLast("" + price, ".");

            // mac
            String decodedMac =
                    "codTrans=" + transactionKey +
                    "divisa=" + currency +
                    "importo=" + value +
                    secretKey;

            String encodedMac = hashMac(decodedMac);

            // mandatory params
            String prms = "";
            prms += "alias=" + encode(alias) + "&";
            prms += "importo=" + encode(value) + "&";
            prms += "divisa=" + encode(currency) + "&";
            prms += "codTrans=" + encode(transactionKey) + "&";

            String urlOk = RouteUtils.baseUrl(request) + Paths.PAID + "?paymentType=" + PaymentType.nexi + "&transactionKey=" + transactionKey + "&orderId=" + cart.getId();
            prms += "url=" + encode(urlOk) + "&";

            String urlKo = RouteUtils.baseUrl(request) + Paths.PAID + "?confirmed=false&paymentType=" + PaymentType.nexi + "&transactionKey=" + transactionKey + "&orderId=" + cart.getId();
            prms += "url_back=" + encode(urlKo) + "&";

            prms += "mac=" + encode(encodedMac) + "&";

            // optional params
            if (StringUtils.isNotBlank(user.getEmail())) {
                prms += "mail=" + encode(user.getEmail()) + "&";
            }
            prms += "languageId=ITA&";
            prms += "descrizione=" + encode(OrderCommons.describe(cart)) + "&";
            if (BooleanUtils.isTrue(cart.getCustomized())) {
                prms += "Note1=" + URLEncoder.encode(StringUtils.defaultIfBlank(cart.getCustomizedNote(), "")) + "&";
            }

            // redirect url
            if (StringUtils.isNotBlank(requestUrl) && StringUtils.isNotBlank(prms)) {
                redirectTo = requestUrl + "?" + prms;
            } else {
                LOGGER.error("Pagamento non configurato");
            }

        } else {
            LOGGER.error("Parametri mancanti");
        }
        
        return redirectTo;
    }
    
    public static String paymentInitIntesa(Request request, PaymentPlatform platform, String transactionKey, User user, com.miocontotermico.pojo.Order cart, double finalPrice) {
        

        String redirectTo = null;
        
        if ((request != null) && (platform != null) && StringUtils.isNotBlank(transactionKey) && (user != null)) {
            final Order order = new Order(); // creazione oggetto ordine
            order.setOrderId(transactionKey); // assegnazione identificativo all'ordine
            order.setCurrency("EUR"); // assegnazione divisa all'ordine
            order.setAmount((long)(finalPrice * 100D)); // assegnazione importo all'ordine
            order.setDescription(OrderCommons.describe(cart));

            final PaymentSession paymentSession = new PaymentSession(); // creazione oggetto sessione di pagamento
            paymentSession.setAmount((long) (finalPrice * 100D)); // impostazione dell'importo per la sessione di pagamento
            
            String urlOk = RouteUtils.baseUrl(request) + Paths.PAID + "?paymentType=" + PaymentType.nexi + "&transactionKey=" + transactionKey + "&orderId=" + cart.getId();
            String urlKo = RouteUtils.baseUrl(request) + Paths.PAID + "?confirmed=false&paymentType=" + PaymentType.nexi + "&transactionKey=" + transactionKey + "&orderId=" + cart.getId();
            
            paymentSession.setResultUrl(urlOk); // impostazione indirizzo di esito
            paymentSession.setCancelUrl(urlKo); // impostazione indirizzo di annullo
//            paymentSession.setNotificationUrl("http://www.example.com/payments/s2s-notify/" + order.getOrderId()); // impostazione indirizzo di notifica

            // impostazione oggetto di chiamata per pagamento Hosted Payment Page
            String apiKey = platform.getSecretKey();

            if (StringUtils.equalsIgnoreCase(platform.getEnvironmentType(), PaymentEnvironmentType.live.toString())) {
                Configuration conf = new Configuration(apiKey);
                ISecurityTokenStorage securityTokenStorage = new SecurityTokenStorage();
                // Creazione del client
                PaymentGatewayClient paymentGatewayClient = PaymentGatewayClient.getInstanceOkHttp3(conf, securityTokenStorage);

                final CreateHostedOrderRequest createHostedOrderRequest = new CreateHostedOrderRequest(order, paymentSession); 
                // impostazione oggetto di esito per il pagamento Hosted Payment Page
                final CreateHostedOrderResponse createHostedOrderResponse; 
                try {
                    createHostedOrderResponse = paymentGatewayClient.createOrderForHostedPayment(createHostedOrderRequest);
                    redirectTo = createHostedOrderResponse.getHostedPage();
                } catch (BaseException ex) {
                    //
                }
            } else {
                ConfigurationTest conf = new ConfigurationTest(apiKey);
                    ISecurityTokenStorage securityTokenStorage = new SecurityTokenStorage();
                // Creazione del client
                PaymentGatewayClient paymentGatewayClient = PaymentGatewayClient.getInstanceOkHttp3(conf, securityTokenStorage);

                final CreateHostedOrderRequest createHostedOrderRequest = new CreateHostedOrderRequest(order, paymentSession); 
                // impostazione oggetto di esito per il pagamento Hosted Payment Page
                final CreateHostedOrderResponse createHostedOrderResponse; 
                try {
                    createHostedOrderResponse = paymentGatewayClient.createOrderForHostedPayment(createHostedOrderRequest);
                    redirectTo = createHostedOrderResponse.getHostedPage();
                } catch (BaseException ex) {
                    //
                }            
            }

        } else {
            LOGGER.error("Parametri mancanti");
        }
        
        return redirectTo;
    }
    
    private static String encode(String text) {
        String encoded = text;
        try {
            encoded = URLEncoder.encode(text, "UTF-8");
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        return encoded;
    }
    
    private static String hashMac(String stringaMac) {
        String mac = null;
        
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-1");
            byte[] in = digest.digest(stringaMac.getBytes("UTF-8"));

            final StringBuilder builder = new StringBuilder();

            for(byte b : in) {
                builder.append(String.format("%02x", b));
            }

            mac = builder.toString();
        } catch (NoSuchAlgorithmException | UnsupportedEncodingException | RuntimeException ex) {
            LOGGER.error("suppressed", ex);
        }
        
        return mac;
    }
    
}
