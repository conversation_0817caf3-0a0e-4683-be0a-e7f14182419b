package com.miocontotermico.core;

import com.miocontotermico.util.RouteUtils;
import java.io.IOException;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import spark.servlet.SparkFilter;

/**
 *
 * <AUTHOR>
 */
public class ServletFilter extends SparkFilter {

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        // this static manager is initialized before any route mapping in order
        // to grant a static access, from routes, to the whole management set:
        // - infos
        // - mongodb
        // - redis
        // - json
        // - pebble engine
        // - SMTP server
        Manager.init(filterConfig.getServletContext());
        
        // init applications AFTER manager initialization
        super.init(filterConfig);
    }

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) throws IOException, ServletException {
        
        // for static resources like css and javascripts we manually provide a "Content-Type"
        // in order to have IE working; we can't use an after route 'cause static
        // resources aren't handled as normal routes: they don't have before and
        // after event handler
        String mimetype = RouteUtils.mimetype(request);
        if (StringUtils.isNotBlank(mimetype)) {
            ((HttpServletResponse) response).setHeader("Content-Type", mimetype);
        }
        
        // standard handling
        super.doFilter(request, response, chain);
    }
    
}
