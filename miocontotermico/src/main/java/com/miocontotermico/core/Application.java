package com.miocontotermico.core;

import com.miocontotermico.data.DataController;
import com.miocontotermico.eneaprocedure.EneaprocedureController;
import com.miocontotermico.home.HomeController;
import com.miocontotermico.login.LoginController;
import com.miocontotermico.mail.MailController;
import com.miocontotermico.post.PostController;
import com.miocontotermico.procedure.ProcedureController;
import com.miocontotermico.procedurenote.ProcedureNoteController;
import com.miocontotermico.support.ImageController;
import com.miocontotermico.profile.ProfileController;
import com.miocontotermico.evaluation.EvaluationController;
import com.miocontotermico.invoicediscount.InvoicediscountController;
import com.miocontotermico.orders.OrderController;
import com.miocontotermico.paperwork.PaperworkController;
import com.miocontotermico.payment.PaymentController;
import com.miocontotermico.shop.ShopController;
import com.miocontotermico.support.ErrorController;
import com.miocontotermico.support.FileController;
import com.miocontotermico.user.UserController;
import com.miocontotermico.util.EnvironmentUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;
import spark.servlet.SparkApplication;
import static spark.Spark.notFound;

/**
 *
 * <AUTHOR>
 */
public class Application implements SparkApplication {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public void init() {

        // configure static resources
        // ... remember to put index.html in your static folder if you want a
        //     project default page
        if (EnvironmentUtils.isLocal()) {
            Spark.staticFiles.externalLocation("/projects/miocontotermico-bundle/miocontotermico/src/main/resources/public");
            // no caching
        } else {
            Spark.staticFileLocation("/public");
            Spark.staticFiles.expireTime(Defaults.STATIC_RESOURCE_EXPIRATION_TIME);
        }

        // default exception handling (simply logs exceptions)
        Spark.exception(Exception.class, (exception, request, response) -> {
            logger.error("exception ", exception);
        });

        // clean-up useless slashes
        Spark.before("*", Filters.removeTrailingSlashes);

        // check connections (when mongodb or redis aren't working)
        Spark.before("*", Filters.checkConnections);

        // root default (when a static index.html doesn't exists)
        Spark.before("/", Filters.defaultRootRedirect);

        // not found
        notFound(ErrorController.notFound);

        /////////
        // routes

        ///////////
        // FRONTEND
        ///////////

        // home
        Spark.get (Paths.HOME,                                          HomeController.home, Manager.engine);
        Spark.get (Paths.ABOUT,                                         HomeController.about, Manager.engine);
        Spark.get (Paths.CONTOTERMICO,                                  HomeController.contotermico, Manager.engine);
        Spark.get (Paths.INVIAPRATICA,                                  HomeController.inviapratica, Manager.engine);
        Spark.post(Paths.INVIAPRATICA_REGISTRATI,                       HomeController.inviapratica_register);
        Spark.get (Paths.CONTACTS,                                      HomeController.contacts, Manager.engine);
        Spark.post(Paths.CONTACTS_SEND,                                 HomeController.contacts_send);
        Spark.get (Paths.NEWS,                                          HomeController.news, Manager.engine);
        Spark.get (Paths.NEWS_DETAIL_IDENTIFIER,                        HomeController.news_detail_identifier, Manager.engine);

        
        //////////
        // BACKEND
        //////////
        
        // profile
        Spark.get (Paths.PROFILE,                                       ProfileController.profile, Manager.engine);
        Spark.get (Paths.success(Paths.PROFILE),                        ProfileController.profile, Manager.engine);
        Spark.get (Paths.error(Paths.PROFILE),                          ProfileController.profile, Manager.engine);
        Spark.post(Paths.PROFILE_SAVE,                                  ProfileController.profile_save);
        // smtp
        Spark.get (Paths.SMTP,                                          ProfileController.smtp, Manager.engine);
        Spark.get (Paths.success(Paths.SMTP),                           ProfileController.smtp, Manager.engine);
        Spark.get (Paths.error(Paths.SMTP),                             ProfileController.smtp, Manager.engine);
        Spark.post(Paths.SMTP_SAVE,                                     ProfileController.smtp_save);
        
        // login
        Spark.get (Paths.LOGIN,                                         LoginController.login, Manager.engine);
        Spark.post(Paths.LOGIN_DO,                                      LoginController.login_do);
        Spark.get (Paths.error(Paths.LOGIN),                            LoginController.login, Manager.engine);

        Spark.get (Paths.FORGOT,                                        LoginController.forgot, Manager.engine);
        Spark.post(Paths.FORGOT_SEND,                                   LoginController.forgot_send);
        Spark.get (Paths.success(Paths.FORGOT),                         LoginController.forgot, Manager.engine);
        Spark.get (Paths.error(Paths.FORGOT),                           LoginController.forgot, Manager.engine);

        Spark.get (Paths.LOGOUT_DO,                                     LoginController.logout_do);


        // users
        Spark.get (Paths.USERS,                                         UserController.users, Manager.engine);
        Spark.get (Paths.success(Paths.USERS),                          UserController.users, Manager.engine);
        Spark.get (Paths.error(Paths.USERS),                            UserController.users, Manager.engine);        
        Spark.get (Paths.USER_DETAIL,                                   UserController.user_detail, Manager.engine);
        Spark.get (Paths.success(Paths.USER_DETAIL),                    UserController.user_detail, Manager.engine);
        Spark.get (Paths.error(Paths.USER_DETAIL),                      UserController.user_detail, Manager.engine);    
        Spark.post(Paths.USER_STATUS_UPDATE,                            UserController.user_status_update);
       
        Spark.post(Paths.USER_REMOVE,                                   UserController.user_remove);
        Spark.post(Paths.USER_UPDATE_CREDIT,                            UserController.user_remove);
        
        // system users
        Spark.get (Paths.SYSTEM_USERS,                                  UserController.system_users, Manager.engine);
        Spark.get (Paths.success(Paths.SYSTEM_USERS),                   UserController.system_users, Manager.engine);
        Spark.get (Paths.error(Paths.SYSTEM_USERS),                     UserController.system_users, Manager.engine);        
        Spark.get (Paths.SYSTEM_USERS_ADD,                              UserController.system_users_add, Manager.engine);        
        Spark.post(Paths.SYSTEM_USERS_ADD_SAVE,                         UserController.system_users_add_save);        
        Spark.get (Paths.SYSTEM_USER_EDIT,                              UserController.system_user_edit, Manager.engine);
        Spark.get (Paths.error(Paths.SYSTEM_USER_EDIT),                 UserController.system_user_edit, Manager.engine);
        //Spark.post(Paths.SYSTEM_USER_EDIT_SAVE,                         UserController.system_user_edit_save);
        //Spark.post(Paths.SYSTEM_USER_REMOVE,                            UserController.system_user_remove);
        
        
        // buy
        Spark.get (Paths.BUY_CREDIT,                                    ShopController.buy_credit, Manager.engine);
        Spark.get (Paths.success(Paths.BUY_CREDIT),                     ShopController.buy_credit, Manager.engine);
        Spark.get (Paths.error(Paths.BUY_CREDIT),                       ShopController.buy_credit, Manager.engine);        
        Spark.post(Paths.BUY_CREDIT_SEND,                               ShopController.buy_credit_send);
        Spark.get (Paths.PAID,                                          ShopController.paid, Manager.engine);        

        
        // procedures
        Spark.get (Paths.PROCEDURES,                                    ProcedureController.procedures, Manager.engine);
        Spark.get (Paths.success(Paths.PROCEDURES),                     ProcedureController.procedures, Manager.engine);
        Spark.get (Paths.error(Paths.PROCEDURES),                       ProcedureController.procedures, Manager.engine);        

        // procedure
        Spark.get (Paths.PROCEDURES_ADD,                                ProcedureController.procedures_add, Manager.engine);
        Spark.post(Paths.PROCEDURES_ADD_INFO_SAVE,                      ProcedureController.procedures_add_info_save);
        Spark.post(Paths.PROCEDURES_ADD_SAVE,                           ProcedureController.procedures_add_save);
        Spark.post(Paths.PROCEDURES_ADD_FILEID_REMOVE,                  ProcedureController.procedures_add_fileid_remove);
        Spark.get (Paths.PROCEDURE_EDIT,                                ProcedureController.procedure_edit, Manager.engine);
        Spark.get (Paths.error(Paths.PROCEDURE_EDIT),                   ProcedureController.procedure_edit, Manager.engine);
        Spark.post(Paths.PROCEDURE_EDIT_SAVE,                           ProcedureController.procedure_edit_save);
        Spark.post(Paths.PROCEDURES_EDIT_FILEID_REMOVE,                 ProcedureController.procedures_edit_fileid_remove);
        Spark.post(Paths.PROCEDURE_REMOVE,                              ProcedureController.procedure_remove);
        Spark.post(Paths.PROCEDURE_STATUS_UPDATE,                       ProcedureController.procedure_status_update);

        // procedure notes
        Spark.get (Paths.PROCEDURE_NOTES_LOAD,                          ProcedureNoteController.loadProcedureNotes);
        Spark.post(Paths.PROCEDURE_NOTE_CREATE,                         ProcedureNoteController.createProcedureNote);
        Spark.post(Paths.PROCEDURE_NOTE_UPDATE,                         ProcedureNoteController.updateProcedureNote);
        Spark.post(Paths.PROCEDURE_NOTE_DELETE,                         ProcedureNoteController.deleteProcedureNote);

        // evaluations
        Spark.get (Paths.EVALUATIONS,                                   EvaluationController.evaluations, Manager.engine);
        Spark.get (Paths.success(Paths.EVALUATIONS),                    EvaluationController.evaluations, Manager.engine);
        Spark.get (Paths.error(Paths.EVALUATIONS),                      EvaluationController.evaluations, Manager.engine);        

        // evaluation
        Spark.get (Paths.EVALUATIONS_ADD,                               EvaluationController.evaluations_add, Manager.engine);
        Spark.post(Paths.EVALUATIONS_ADD_INFO_SAVE,                     EvaluationController.evaluations_add_info_save);
        Spark.post(Paths.EVALUATIONS_ADD_SAVE,                          EvaluationController.evaluations_add_save);
        Spark.post(Paths.EVALUATIONS_ADD_FILEID_REMOVE,                 EvaluationController.evaluations_add_fileid_remove);
        Spark.get (Paths.EVALUATION_EDIT,                               EvaluationController.evaluation_edit, Manager.engine);
        Spark.get (Paths.error(Paths.EVALUATION_EDIT),                  EvaluationController.evaluation_edit, Manager.engine);
        Spark.post(Paths.EVALUATION_EDIT_SAVE,                          EvaluationController.evaluation_edit_save);
        Spark.post(Paths.EVALUATION_REMOVE,                             EvaluationController.evaluation_remove);
        Spark.post(Paths.EVALUATION_STATUS_UPDATE,                      EvaluationController.evaluation_status_update);
        
        // eneaprocedure
        Spark.get (Paths.ENEAPROCEDURES,                                EneaprocedureController.eneaprocedures, Manager.engine);
        Spark.get (Paths.success(Paths.ENEAPROCEDURES),                 EneaprocedureController.eneaprocedures, Manager.engine);
        Spark.get (Paths.error(Paths.ENEAPROCEDURES),                   EneaprocedureController.eneaprocedures, Manager.engine);        

        // eneaprocedure
        Spark.get (Paths.ENEAPROCEDURES_ADD,                            EneaprocedureController.eneaprocedures_add, Manager.engine);
        Spark.post(Paths.ENEAPROCEDURES_ADD_INFO_SAVE,                  EneaprocedureController.eneaprocedures_add_info_save);
        Spark.post(Paths.ENEAPROCEDURES_ADD_SAVE,                       EneaprocedureController.eneaprocedures_add_save);
        Spark.post(Paths.ENEAPROCEDURES_ADD_FILEID_REMOVE,              EneaprocedureController.eneaprocedures_add_fileid_remove);
        Spark.get (Paths.ENEAPROCEDURE_EDIT,                            EneaprocedureController.eneaprocedure_edit, Manager.engine);
        Spark.get (Paths.error(Paths.ENEAPROCEDURE_EDIT),               EneaprocedureController.eneaprocedure_edit, Manager.engine);
        Spark.post(Paths.ENEAPROCEDURE_EDIT_SAVE,                       EneaprocedureController.eneaprocedure_edit_save);
        Spark.post(Paths.ENEAPROCEDURE_EDIT_FILEID_REMOVE,              EneaprocedureController.eneaprocedure_edit_fileid_remove);
        Spark.post(Paths.ENEAPROCEDURE_REMOVE,                          EneaprocedureController.eneaprocedure_remove);
        Spark.post(Paths.ENEAPROCEDURE_STATUS_UPDATE,                   EneaprocedureController.eneaprocedure_status_update);
        
        // invoicediscount
        Spark.get (Paths.INVOICEDISCOUNTS,                              InvoicediscountController.invoicediscounts, Manager.engine);
        Spark.get (Paths.success(Paths.INVOICEDISCOUNTS),               InvoicediscountController.invoicediscounts, Manager.engine);
        Spark.get (Paths.error(Paths.INVOICEDISCOUNTS),                 InvoicediscountController.invoicediscounts, Manager.engine);        

        // invoicediscount
        Spark.get (Paths.INVOICEDISCOUNTS_ADD,                          InvoicediscountController.invoicediscounts_add, Manager.engine);
        Spark.post(Paths.INVOICEDISCOUNTS_ADD_INFO_SAVE,                InvoicediscountController.invoicediscounts_add_info_save);
        Spark.post(Paths.INVOICEDISCOUNTS_ADD_SAVE,                     InvoicediscountController.invoicediscounts_add_save);
        Spark.post(Paths.INVOICEDISCOUNTS_ADD_FILEID_REMOVE,            InvoicediscountController.invoicediscounts_add_fileid_remove);
        Spark.get (Paths.INVOICEDISCOUNT_EDIT,                          InvoicediscountController.invoicediscount_edit, Manager.engine);
        Spark.get (Paths.error(Paths.INVOICEDISCOUNT_EDIT),             InvoicediscountController.invoicediscount_edit, Manager.engine);
        Spark.post(Paths.INVOICEDISCOUNT_EDIT_SAVE,                     InvoicediscountController.invoicediscount_edit_save);
        Spark.post(Paths.INVOICEDISCOUNT_EDIT_FILEID_REMOVE,            InvoicediscountController.invoicediscount_edit_fileid_remove);
        Spark.post(Paths.INVOICEDISCOUNT_REMOVE,                        InvoicediscountController.invoicediscount_remove);
        Spark.post(Paths.INVOICEDISCOUNT_STATUS_UPDATE,                 InvoicediscountController.invoicediscount_status_update);
        
        // post
        getHtml   (Paths.POSTS,                                         PostController.posts, FlashMessageType.successAndError);
        getHtml   (Paths.POST_EDIT,                                     PostController.post_edit, FlashMessageType.error);
        post      (Paths.POST_EDIT_SAVE,                                PostController.post_edit_save);
        post      (Paths.POST_REMOVE,                                   PostController.post_remove);
        
        // paperwork
        getHtml   (Paths.PAPERWORKS,                                    PaperworkController.paperworks, FlashMessageType.successAndError);
        getHtml   (Paths.PAPERWORKS_VIEW,                               PaperworkController.paperworks_view, FlashMessageType.successAndError);
        getHtml   (Paths.PAPERWORK_EDIT,                                PaperworkController.paperwork_edit, FlashMessageType.error);
        post      (Paths.PAPERWORK_EDIT_SAVE,                           PaperworkController.paperwork_edit_save);
        post      (Paths.PAPERWORK_REMOVE,                              PaperworkController.paperwork_remove);
        post      (Paths.PAPERWORK_EDIT_FILEID_REMOVE,                  PaperworkController.paperwork_edit_fileid_remove);
        
        
        // payments
        getHtml (Paths.PAYMENTS,                                        PaymentController.payments, FlashMessageType.successAndError);
        getHtml (Paths.PAYMENTS_ADD,                                    PaymentController.payments_add, FlashMessageType.successAndError);
        post    (Paths.PAYMENTS_ADD_SAVE,                               PaymentController.payments_add_save);
        getHtml (Paths.PAYMENT_VIEW,                                    PaymentController.payment_view, FlashMessageType.successAndError);
        post    (Paths.PAYMENT_VIEW_UPDATE,                             PaymentController.payment_view_update);
        post    (Paths.PAYMENT_SHOPPABLE_UPDATE,                        PaymentController.payment_shoppable_update);
        post    (Paths.PAYMENT_REMOVE,                                  PaymentController.payment_remove);

        // payment platforms
        getHtml (Paths.PAYMENT_PLATFORMS,                               PaymentController.payment_platforms, FlashMessageType.successAndError);
        getHtml (Paths.PAYMENT_PLATFORMS_ADD,                           PaymentController.payment_platforms_add, FlashMessageType.successAndError);
        postJson(Paths.PAYMENT_PLATFORMS_ADD_SAVE,                      PaymentController.payment_platforms_add_save);
        getHtml (Paths.PAYMENT_PLATFORM_VIEW,                           PaymentController.payment_platform_view, FlashMessageType.successAndError);
        post    (Paths.PAYMENT_PLATFORM_VIEW_UPDATE,                    PaymentController.payment_platform_view_update);
        post    (Paths.PAYMENT_PLATFORM_REMOVE,                         PaymentController.payment_platform_remove);
        
        // orders
        getHtml (Paths.ORDERS,                                          OrderController.orders, FlashMessageType.successAndError);
        getJson (Paths.ORDERS_DATA,                                     OrderController.orders_data);        
        getHtml (Paths.ORDER_VIEW,                                      OrderController.order_view, FlashMessageType.successAndError);
        post    (Paths.ORDER_STATUS_UPDATE,                             OrderController.order_status_update);
        post    (Paths.ORDER_PAYMENT_STATUS_UPDATE,                     OrderController.order_payment_status_update);
        
        // mail
        getHtml   (Paths.MAILS,                                         MailController.mails, FlashMessageType.successAndError);
        getHtml   (Paths.MAIL_DETAIL,                                   MailController.mail_detail, FlashMessageType.error);
        post      (Paths.MAIL_DETAIL_SAVE,                              MailController.mail_detail_save);
        
        // SUPPORT
        Spark.get (Paths.IMAGE,                                         ImageController.image);
        Spark.get (Paths.THUMBNAIL,                                     ImageController.thumbnail);
        Spark.get (Paths.FILE,                                          FileController.file);
        Spark.get (Paths.FILEZIP,                                       FileController.createProcedureZipFile);

        // DATA
        Spark.get (Paths.DATA_CITIES,                                   DataController.data_cities, Manager.jsonTransformer);

    }

    @Override
    public void destroy() {
        Manager.destroy();
    }

    private void get(String path, Route route) {
        String[] paths = Paths.localizedPaths(path);
        for (String pth : paths) {
            Spark.get(pth, route);
        }
    }

    private void getHtml(String path, TemplateViewRoute route) {
        getHtml(path, route, FlashMessageType.none);
    }

    private void getHtml(String path, TemplateViewRoute route, FlashMessageType flash) {
        String[] paths = Paths.localizedPaths(path);
        for (String pth : paths) {
            Spark.get(pth, route, Manager.engine);
            if (flash != null) switch (flash) {
                case successAndError:
                    Spark.get(Paths.success(pth), route, Manager.engine);
                    Spark.get(Paths.error(pth), route, Manager.engine);
                    break;
                case success:
                    Spark.get(Paths.success(pth), route, Manager.engine);
                    break;
                case error:
                    Spark.get(Paths.error(pth), route, Manager.engine);
                    break;
                default:
                    break;
            }
        }
    }

    private void getJson(String path, Route route) {
        String[] paths = Paths.localizedPaths(path);
        for (String pth : paths) {
            Spark.get(pth, route, Manager.jsonTransformer);
        }
    }

    private void post(String path, Route route) {
        String[] paths = Paths.localizedPaths(path);
        for (String pth : paths) {
            Spark.post(pth, route);
        }
    }

    private void postJson(String path, Route route) {
        String[] paths = Paths.localizedPaths(path);
        for (String pth : paths) {
            Spark.post(pth, route, Manager.jsonTransformer);
        }
    }
}
