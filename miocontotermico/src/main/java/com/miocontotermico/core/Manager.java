package com.miocontotermico.core;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.miocontotermico.util.RouteUtils;
import com.miocontotermico.message.Attachment;
import com.miocontotermico.message.MessageSender;
import com.miocontotermico.message.SmtpService;
import com.mitchellbosecke.pebble.loader.ServletLoader;
import com.mongodb.MongoClient;
import com.mongodb.MongoClientOptions;
import com.mongodb.client.AggregateIterable;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoDatabase;
import com.miocontotermico.pojo.User;
import com.miocontotermico.support.print.PdfGenerator;
import com.miocontotermico.util.EnvironmentUtils;
import java.io.File;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.UUID;
import javax.servlet.ServletContext;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;
import spark.ModelAndView;
import spark.Request;
import spark.Response;

/**
 *
 * <AUTHOR>
 */
public class Manager {

    private static final Logger LOGGER = LoggerFactory.getLogger(Manager.class.getName());
    
    // common
    private static String contextPath;
    public static String realPath;
    private static String buildNumber;
    private static String applicationName;
    private static int sessionDuration;
    private static int sessionRememberDuration;

    // mongodb
    private static MongoClient mongoClient;
    public static MongoDatabase mongoDatabase;

    // redis
    private static String redisHostname;
    private static JedisPool redisPool;

    // pebble
    public static PebbleTemplateEngine engine;

    // json
    private static ObjectMapper mapper;
    public static JsonTransformer jsonTransformer;
    
    // SMTP
    public static String smtpHostname;
    public static int smtpPort;
    public static boolean smtpAuthentication;
    public static String smtpUsername;
    public static String smtpPassword;
    public static boolean smtpEmailEncryption;
    public static boolean smtpStartTls;
    public static String smtpApikey;
    public static String smtpSender;
    
    
    public static void init(ServletContext servletContext) {
        // commons
        contextPath = servletContext.getContextPath();      // i.e. "/miocontotermico"
        realPath = servletContext.getRealPath("/");
        applicationName = StringUtils.stripStart(servletContext.getContextPath(), "/");
        sessionDuration = NumberUtils.toInt(servletContext.getInitParameter("com.miocontotermico.session.duration"), Defaults.SESSION_EXPIRATION_TIME);
        sessionRememberDuration = NumberUtils.toInt(servletContext.getInitParameter("com.miocontotermico.session.remember.duration"), Defaults.SESSION_REMEMBER_EXPIRATION_TIME);
        buildNumber = getBuildNumber(servletContext);
        
        // mongodb
        MongoClientOptions options = MongoClientOptions.builder()
                .description("miocontotermico")
                .serverSelectionTimeout(1000)
                .connectTimeout(10000)
                .socketTimeout(60000)
                .build()
                ;
        mongoClient = new MongoClient(servletContext.getInitParameter("com.miocontotermico.mongodb.hostname"), options);
        mongoDatabase = mongoClient.getDatabase(servletContext.getInitParameter("com.miocontotermico.mongodb.database"));
        
        // redis
        redisHostname = servletContext.getInitParameter("com.miocontotermico.redis.hostname");
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setTestOnBorrow(true);
        redisPool = new JedisPool(poolConfig, redisHostname);
        
        // json
        mapper = new ObjectMapper();
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        SimpleModule module = new SimpleModule();
        module.addSerializer(Date.class, new DateJsonSerializer());
        module.addDeserializer(Date.class, new DateJsonDeserializer());
        module.addSerializer(ObjectId.class, new ObjectIdJsonSerializer());
        module.addDeserializer(ObjectId.class, new ObjectIdJsonDeserializer());
        mapper.registerModule(module);
        jsonTransformer = new JsonTransformer();
        
        // engine
        engine = new PebbleTemplateEngine(new ServletLoader(servletContext), EnvironmentUtils.isLocal());
        
        // SMTP
        smtpHostname = servletContext.getInitParameter("com.miocontotermico.smtp.hostname");
        smtpPort = NumberUtils.toInt(servletContext.getInitParameter("com.miocontotermico.smtp.port"), -1);
        smtpAuthentication = BooleanUtils.toBoolean(servletContext.getInitParameter("com.miocontotermico.smtp.authentication"));
        smtpUsername = servletContext.getInitParameter("com.miocontotermico.smtp.username");
        smtpPassword = servletContext.getInitParameter("com.miocontotermico.smtp.password");
        smtpEmailEncryption = BooleanUtils.toBoolean(servletContext.getInitParameter("com.miocontotermico.smtp.emailencryption"));
        smtpStartTls = BooleanUtils.toBoolean(servletContext.getInitParameter("com.miocontotermico.smtp.starttls"));
        smtpApikey = servletContext.getInitParameter("com.miocontotermico.smtp.apikey");
        smtpSender = servletContext.getInitParameter("com.miocontotermico.smtp.sender");
        
    }
    
    public static void destroy() {
        // redis
        if (redisPool != null) {
            redisPool.close();
        }
        // mongodb
        if (mongoClient != null) {
            mongoClient.close();
        }
    }

    
    //////////
    // mongodb
    public static <T> T fromDocument(Document document, Class<T> objectClass) {
        T result = null;
        if (document != null) {
            result = deserializeFromJson(document.toJson(), objectClass);
        }
        return result;
    }

    public static <T> List<T> fromDocumentList(FindIterable<Document> list, Class<T> objectClass) {
        List<T> result = null;
        if (list != null) {
            result = new ArrayList<>();
            for (Document document : list) {
                result.add(fromDocument(document, objectClass));
            }
        }
        return result;
    }

    public static <T> List<T> fromAggregateList(AggregateIterable<Document> list, Class<T> objectClass) {
        List<T> result = null;
        if (list != null) {
            result = new ArrayList<>();
            for (Document document : list) {
                result.add(fromDocument(document, objectClass));
            }
        }
        return result;
    }
    
    public static Document toDocument(Object object) {
        Document result = null;
        if (object != null) {
            result = Document.parse(serializeToJson(object));
        }
        return result;
    }
    
    
    ////////
    // redis
    public static User getUser(String token, Response response) {
        User user = null;
        if (StringUtils.isNotBlank(token)) {
            if (!Manager.validateSession(token)) {
                response.removeCookie("/", token);
            } else {
                user = Manager.getSession(token, "user", User.class);
            }
        }
        return user;
    }
    
    public static boolean validateSession(String token) {
        boolean exists = false;
        if (StringUtils.isNotBlank(token)) {
            try (Jedis redis = redisPool.getResource()) {
                exists = redis.exists(sessionPrefix(token));
                // keeping session alive
                if (exists) {
                    boolean remember = BooleanUtils.toBoolean(redis.hget(sessionPrefix(token), "remember"));
                    int duration = (remember ? sessionRememberDuration : sessionDuration);
                    redis.expire(sessionPrefix(token), duration);
                }
            }
        }
        return exists;
    }

    public static void createSession(Request request, Response response, String token, boolean remember) {
        if (StringUtils.isNotBlank(token)) {
            Map<String, String> hash = new HashMap<>();
            hash.put("creation", serializeToJson(new Date()));
            hash.put("ip", RouteUtils.getIp(request));
            hash.put("userAgent", StringUtils.defaultString(request.userAgent()));
            hash.put("remember", String.valueOf(remember));
            int duration = (remember ? sessionRememberDuration : sessionDuration);
            try (Jedis redis = redisPool.getResource()) {
                redis.hmset(sessionPrefix(token), hash);
                redis.expire(sessionPrefix(token), duration);
            }
            response.cookie(EnvironmentUtils.domain(request), "/", Defaults.SESSION_COOKIE_NAME, token, duration, false, false);
        }
    }

    public static void destroySession(Response response, String token) {
        if (StringUtils.isNotBlank(token)) {
            try (Jedis redis = redisPool.getResource()) {
                redis.del(sessionPrefix(token));
            }
            response.removeCookie("/", Defaults.SESSION_COOKIE_NAME);
        }
    }

    public static void keepAliveSession(String token) {
        if (StringUtils.isNotBlank(token)) {
            try (Jedis redis = redisPool.getResource()) {
                boolean exists = redis.exists(sessionPrefix(token));
                // keeping session alive
                if (exists) {
                    boolean remember = BooleanUtils.toBoolean(redis.hget(sessionPrefix(token), "remember"));
                    int duration = (remember ? sessionRememberDuration : sessionDuration);
                    redis.expire(sessionPrefix(token), duration);
                }
            }
        }
    }

    public static <T> T getSession(String token, String key, Class<T> objectClass) {
        T result = null;
        if (StringUtils.isNotBlank(token)) {
            if (StringUtils.isNotBlank(key)) {
                String json;
                try (Jedis redis = redisPool.getResource()) {
                    json = redis.hget(sessionPrefix(token), key);
                }
                if (StringUtils.isNotBlank(json)) {
                    result = deserializeFromJson(json, objectClass);
                }
            }
        }
        return result;
    }

    public static String getSessionValue(String token, String key) {
        String result = null;
        if (StringUtils.isNotBlank(token)) {
            if (StringUtils.isNotBlank(key)) {
                try (Jedis redis = redisPool.getResource()) {
                    result = redis.hget(sessionPrefix(token), key);
                }
            }
        }
        return result;
    }
    
    public static <T> List<T> getSessionList(String token, String key, Class<T> objectClass) {
        List<T> result = null;
        if (StringUtils.isNotBlank(token)) {
            if (StringUtils.isNotBlank(key)) {
                String json;
                try (Jedis redis = redisPool.getResource()) {
                    json = redis.hget(sessionPrefix(token), key);
                }
                if (StringUtils.isNotBlank(json)) {
                    result = deserializeListFromJson(json, objectClass);
                }
            }
        }
        return result;
    }
    
    public static void putSession(String token, String key, Object value) {
        if (StringUtils.isNotBlank(token)) {
            if (StringUtils.isNotBlank(key)) {
                String json = serializeToJson(value);
                if (StringUtils.isNotBlank(json)) {
                    try (Jedis redis = redisPool.getResource()) {
                        redis.hset(sessionPrefix(token), key, json);
                    }
                }
            }
        }
    }

    public static void deleteSession(String token, String key) {
        if (StringUtils.isNotBlank(token)) {
            if (StringUtils.isNotBlank(key)) {
                try (Jedis redis = redisPool.getResource()) {
                    redis.hdel(sessionPrefix(token), key);
                }
            }
        }
    }
    
    public static boolean existsSession(String token, String key) {
        boolean exists = false;
        if (StringUtils.isNotBlank(token)) {
            if (StringUtils.isNotBlank(key)) {
                try (Jedis redis = redisPool.getResource()) {
                    exists = redis.hexists(sessionPrefix(token), key);
                }
            }
        }
        return exists;
    }

    public static String getToken(Request request) {
        String cookie = null;
        if (request != null) {
            cookie = request.cookies().get(Defaults.SESSION_COOKIE_NAME);
        }
        return cookie;
    }
    
    private static String sessionPrefix(String token) {
        if (StringUtils.isNotBlank(token)) {
            return "miocontotermico-sessions:" + token;
        } else {
            return null;
        }
    }
    

    ///////
    // json
    public static String serializeToJson(Object object) {
        try {
            return mapper.writeValueAsString(object);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        return null;
    }

    public static <T> T deserializeFromJson(String json, Class<T> objectClass) {
        try {
            return mapper.readValue(json, objectClass);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        return null;
    }

    public static <T> List<T> deserializeListFromJson(String json, Class<T> objectClass) {
        try {
            return mapper.readValue(json, mapper.getTypeFactory().constructCollectionType(List.class, objectClass));
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        return null;
    }


    /////////
    // pebble
    public static ModelAndView render(String templateName, Map<String, Object> viewAttributes, PathType pathType) {
        Map<String, Object> attributes = new HashMap<>();
        if (viewAttributes != null) {
            attributes.putAll(viewAttributes);
        }
        
        if (pathType != null) {
            
            // url on local environment are in the format  /<context name>/list/detail ...
            // url on remote environment are in the format /list/detail ...
            switch (pathType) {
                case bare:
                    attributes.put("contextPath", "");
                    break;
                case servlet:
                    attributes.put("contextPath", contextPath);
                    break;
                case full:
                    // ?????? @mike: non è gestito il caso "full"
                    break;
                default:
                    LOGGER.error("unhandled path type " + pathType);
                    break;
            }
            
        }
        
        attributes.put("buildNumber", buildNumber);
        
        return new ModelAndView(attributes, templateName);
    }
    
    public static ModelAndView renderEmpty() {
        return new ModelAndView(null, Templates.EMPTY);
    }
    
    ///////
    // SMTP
    public static SmtpService defaultSmtpService() {
        if (StringUtils.isNotBlank(smtpApikey)) {
            return new SmtpService(null, null, null, null, null, null, null, smtpApikey, smtpSender);
        } else {
            return new SmtpService(smtpHostname, smtpPort, smtpAuthentication, smtpUsername, smtpPassword, smtpEmailEncryption, smtpStartTls, null, smtpSender);
        }
    }
    
    public static boolean sendMail(SmtpService smtp, String from, String fromName, String to, String templateName, Map<String, Object> attributes, PathType pathType) {
        return sendMail(smtp, from, fromName, to, templateName, attributes, pathType, null);
    }
    
    public static boolean sendMail(SmtpService smtp, String from, String fromName, String to, String templateName, Map<String, Object> attributes, PathType pathType, List<Attachment> attachments) {
        
        boolean done = false;
        
        // host validation
        if (MessageSender.isValidHost(smtp.getHostname(),
                    smtp.getPort(),
                    smtp.getAuthentication(),
                    smtp.getUsername(),
                    smtp.getPassword(),
                    smtp.getEncryption(),
                    smtp.getStartTls(),
                    smtp.getApikey())) {

            // render body from template
            ModelAndView mv = render(templateName, attributes, pathType);
            String body = engine.render(mv);
            
            // grad subject from htmol title
            String subject = StringUtils.substringBetween(body, "<title>", "</title>");
            
            // defaults
            String cc = null;
            int maxAttachmentSize = 0;
            
            // message validation
            if (MessageSender.isValidMessage(from,
                    fromName,
                    to,
                    cc,
                    subject,
                    body,
                    maxAttachmentSize,
                    attachments)) {
                
                // send mail
                if (StringUtils.isNotBlank(smtp.getApikey())) {
                    try {
                        done = MessageSender.sendMessage(smtp.getApikey(),
                                from,
                                fromName,
                                to,
                                cc,
                                subject,
                                body,
                                maxAttachmentSize,
                                attachments);
                    } catch (Exception ex) {
                        LOGGER.error("suppressed", ex);
                        LOGGER.error(
                                "params:\n" +
                                "smtpApikey "+ smtp.getApikey()+ "\n" +
                                "from "+ from + "\n" +
                                "fromName "+ fromName + "\n" +
                                "to "+ to + "\n" +
                                "cc "+ cc + "\n" +
                                "subject "+ subject + "\n" +
                                "body "+ body + "\n" +
                                "maxAttachmentSize "+ maxAttachmentSize + "\n" +
                                "attachments count "+ (attachments != null ? attachments.size() : 0) + "\n"
                        );
                    }
                } else {
                    try {
                        done = MessageSender.sendMessage(smtp.getHostname(),
                                smtp.getPort(),
                                smtp.getAuthentication(),
                                smtp.getUsername(),
                                smtp.getPassword(),
                                smtp.getEncryption(),
                                smtp.getStartTls(),
                                from,
                                fromName,
                                to,
                                cc,
                                subject,
                                body,
                                maxAttachmentSize,
                                attachments);
                    } catch (Exception ex) {
                        LOGGER.error("suppressed", ex);
                        LOGGER.error(
                                "params:\n" +
                                "smtpHostname "+ smtp.getHostname() + "\n" +
                                "smtpPort "+ smtp.getPort() + "\n" +
                                "smtpAuthentication "+ smtp.getAuthentication() + "\n" +
                                "smtpUsername "+ smtp.getUsername() + "\n" +
                                "smtpPassword "+ smtp.getPassword() + "\n" +
                                "smtpEmailEncryption "+ smtp.getEncryption() + "\n" +
                                "from "+ from + "\n" +
                                "fromName "+ fromName + "\n" +
                                "to "+ to + "\n" +
                                "cc "+ cc + "\n" +
                                "subject "+ subject + "\n" +
                                "body "+ body + "\n" +
                                "maxAttachmentSize "+ maxAttachmentSize + "\n" +
                                "attachments count "+ (attachments != null ? attachments.size() : 0) + "\n"
                        );
                    }
                }
                
            } else {
                LOGGER.error("error, invalid message");
                LOGGER.error("from = "+ from);
                LOGGER.error("fromName = " + fromName);
                LOGGER.error("to = " + to);
                LOGGER.error("cc = " + cc);
                LOGGER.error("subject = " + subject);
                LOGGER.error("body = " + body);
                LOGGER.error("maxAttachmentSize = " + maxAttachmentSize);
                LOGGER.error("attachments count = " + (attachments != null ? attachments.size() : 0));
            }
            
        } else {
            LOGGER.error("error, invalid host");
            LOGGER.error("smtpHostname = " + smtp.getHostname());
            LOGGER.error("smtpPort = " + smtp.getPort());
            LOGGER.error("smtpAuthentication = " + smtp.getAuthentication());
            LOGGER.error("smtpUsername = " + smtp.getUsername());
            LOGGER.error("smtpPassword = " + smtp.getPassword());
            LOGGER.error("smtpEmailEncryption = " + smtp.getEncryption());
        }
        
        return done;
    }


    //////
    // pdf
    public static byte[] generatePdf(Request request, String templateName, Map<String, Object> attributes, PathType pathType) {
        
        byte[] bytes = null;
        
        // host validation
        if (StringUtils.isNotBlank(templateName)) {

            // render body from template
            ModelAndView mv = render(templateName, attributes, pathType);
            String text = engine.render(mv);
            
            // text validation
            if (PdfGenerator.isValidText(text)) {
                
                // generate xhtml file
                String xhtmlName = null;
                try {
                    File xhtml = File.createTempFile("invoice-tmp-" + UUID.randomUUID().toString(), ".xhtml");
                    FileUtils.writeStringToFile(xhtml, text, "UTF-8");
                    xhtmlName = "file://" + xhtml.getAbsolutePath();
                    xhtmlName = StringUtils.replace(xhtmlName, "C:", "");
                    xhtmlName = StringUtils.replace(xhtmlName, "c:", "");
                    xhtmlName = StringUtils.replace(xhtmlName, "\\", "/");
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                
                // generate pdf
                if (StringUtils.isNotBlank(xhtmlName)) {
                    try {
                        bytes = PdfGenerator.convert(xhtmlName);
                    } catch (Exception ex) {
                        LOGGER.error("suppressed", ex);
                    }
                }
                
            } else {
                LOGGER.error("error, invalid text " + text);
            }
            
        } else {
            LOGGER.error("error, invalid template " + templateName);
        }
        
        return bytes;
    }

    private static String getBuildNumber(ServletContext servletContext) {
        String build = null;
        
        // when running from netbeans, we don't have a manifest file
        File file = new File(realPath + "/" + Defaults.MANIFEST_NAME);
        if (file.exists()) {
            
            Properties prop = new Properties();
            try {
                try (InputStream res = servletContext.getResourceAsStream("/" + Defaults.MANIFEST_NAME)) {
                    prop.load(res);
                }
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
            build = prop.getProperty("Implementation-Build");
            
        } else {
            LOGGER.warn("no build number when running from netbeans...");
        }
        
        return build;
    }
    
}
