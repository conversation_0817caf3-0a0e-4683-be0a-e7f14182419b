package com.miocontotermico.core;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

/**
 *
 * <AUTHOR>
 */
public class DateJsonSerializer extends JsonSerializer<Date> {

    @Override
    public void serialize(Date value, JsonGenerator gen, SerializerProvider serializers) throws IOException, JsonProcessingException {
        
        // mongodb date serialization sample myDate: {$date: "2016-11-08T23:50:32.987Z" }
        
        if (value == null) {
            gen.writeNull();
        } else {
            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", Locale.ITALIAN);
            String txt = dateFormat.format(value);
            gen.writeStartObject();
            gen.writeFieldName("$date");
            gen.writeString(txt);
            gen.writeEndObject();
        }
    }
    
}
