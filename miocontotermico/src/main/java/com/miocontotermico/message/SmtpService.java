package com.miocontotermico.message;

/**
 *
 * <AUTHOR>
 */
public class SmtpService {

    private String hostname;
    private Integer port;
    private Boolean authentication;
    private String username;
    private String password;
    private Boolean encryption;
    private Boolean startTls;
    private String apikey;
    private String sender;

    public SmtpService() {
    }

    public SmtpService(String hostname, Integer port, Boolean authentication, String username, String password, Boolean encryption, Boolean startTls, String apikey, String sender) {
        this.hostname = hostname;
        this.port = port;
        this.authentication = authentication;
        this.username = username;
        this.password = password;
        this.encryption = encryption;
        this.startTls = startTls;
        this.apikey = apikey;
        this.sender = sender;
    }

    public String getHostname() {
        return hostname;
    }

    public void setHostname(String hostname) {
        this.hostname = hostname;
    }

    public Integer getPort() {
        return port;
    }

    public void setPort(Integer port) {
        this.port = port;
    }

    public Boolean getAuthentication() {
        return authentication;
    }

    public void setAuthentication(Boolean authentication) {
        this.authentication = authentication;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Boolean getEncryption() {
        return encryption;
    }

    public void setEncryption(Boolean encryption) {
        this.encryption = encryption;
    }

    public Boolean getStartTls() {
        return startTls;
    }

    public void setStartTls(Boolean startTls) {
        this.startTls = startTls;
    }

    public String getApikey() {
        return apikey;
    }

    public void setApikey(String apikey) {
        this.apikey = apikey;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }
    
}
