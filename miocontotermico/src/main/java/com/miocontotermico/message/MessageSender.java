package com.miocontotermico.message;

import com.sendgrid.ClickTrackingSetting;
import com.sendgrid.Content;
import com.sendgrid.Email;
import com.sendgrid.GoogleAnalyticsSetting;
import com.sendgrid.Mail;
import com.sendgrid.Method;
import com.sendgrid.OpenTrackingSetting;
import com.sendgrid.Personalization;
import com.sendgrid.Request;
import com.sendgrid.Response;
import com.sendgrid.SendGrid;
import com.sendgrid.SubscriptionTrackingSetting;
import com.sendgrid.TrackingSettings;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.mail.DefaultAuthenticator;
import org.apache.commons.mail.EmailAttachment;
import org.apache.commons.mail.HtmlEmail;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class MessageSender {

    private static final String VALID_EMAIL_PATTERN = "\\b[a-zA-Z0-9._%+-]+@(?:[àèéìòùa-zA-Z0-9-]+\\.)+[a-zA-Z]{2,7}\\b";

    private static final Logger LOGGER = LoggerFactory.getLogger(MessageSender.class.getName());

    public static boolean isValidHost(String host, int port, boolean authentication, String authenticationUser, String authenticationPassword, boolean emailEncryption, boolean startTls, String apikey) {
        boolean valid = false;
        if (StringUtils.isNotBlank(apikey)) {
            valid = StringUtils.isNotBlank(host) &&
                    (port > 0) &&
                    (!authentication || StringUtils.isNotBlank(authenticationUser));
        } else {
            valid = true;
        }
        return valid;
    }
    
    public static boolean isValidMessage(String from, String personalName, String to, String cc, String subject, String message, int maxAttachmentSize, List<Attachment> attachments) {
        boolean exceedLimit = false;
        // maxAttachmentSize = 0 means "unlimited"
        if ((maxAttachmentSize > 0) && (attachments != null) && (attachments.size() > 0)) {
            for (Attachment attachment : attachments) {
                File file = new File(attachment.getFilename());
                if (file.length() > maxAttachmentSize) {
                    exceedLimit = true;
                    break;
                }
            }
        }
        boolean valid = StringUtils.isNotBlank(from) &&
                validEmailAddress(from) &&
                StringUtils.isNotBlank(to) &&
                validEmailAddress(to) &&
                (StringUtils.isBlank(cc) || validEmailAddress(cc)) &&
                StringUtils.isNotBlank(subject) &&
                StringUtils.isNotBlank(message) &&
                !exceedLimit
                ;
        
        if (!valid) {
            LOGGER.warn("from " + from);
            LOGGER.warn("to " + to);
            LOGGER.warn("cc " + cc);
            LOGGER.warn("subject " + subject);
            LOGGER.warn("message " + message);
            LOGGER.warn("exceedLimit " + exceedLimit);
        }
        
        return valid;         
    }
    
    public static boolean sendMessage(String host, int port,
            boolean authentication,
            String authenticationUser,
            String authenticationPassword,
            boolean emailEncryption,
            boolean startTls,
            String from,
            String personalName,
            String to,
            String cc,
            String subject,
            String text,
            int maxAttachmentSize,
            List<Attachment> attachments) throws Exception {

        // mailsend1.19.exe -smtp mail.symmathesy.org -port 465 -ssl -auth -user <EMAIL> -pass !midastouchSY -to <EMAIL> -from <EMAIL> -sub oggetto
        
        HtmlEmail email = new HtmlEmail();
        email.setHostName(host);
        email.setSmtpPort(port);
        email.setAuthenticator(new DefaultAuthenticator(authenticationUser, authenticationPassword));
        if (emailEncryption) {
            email.setSSLOnConnect(true);
        }
        if (startTls) {
            email.setStartTLSEnabled(true);
        }
        email.setCharset("UTF-8");
        email.setFrom(from, personalName);
        email.addTo(to);
        if (StringUtils.isNotBlank(cc)) {
            email.addCc(cc);
        }
        email.setSubject(subject);

        // html
        StringBuilder html = new StringBuilder();
        if (StringUtils.startsWithIgnoreCase(text, "<!DOCTYPE")) {
            // received an html text
            html.append(text);
        } else {
            // received a plain text
            html.append("<!DOCTYPE html>\n");
            html.append("<html>\n");
            html.append("    <head>\n");
            html.append("    </head>\n");
            html.append("    <body>\n");
            html.append("        <p>").append(htmlNewLiner(text)).append("</p>\n");
            html.append("    </body>\n");
            html.append("</html>\n");
        }
        email.setHtmlMsg(html.toString());

        // attachments
        if (attachments != null) {
            for (Attachment attachment : attachments) {
                EmailAttachment tmp = new EmailAttachment();
                tmp.setPath(attachment.getFilename());
                tmp.setDisposition(EmailAttachment.ATTACHMENT);
                tmp.setDescription(attachment.getName());
                tmp.setName(attachment.getName());
                email.attach(tmp);
            }
        }
        String messageId = email.send();
        return StringUtils.isNotBlank(messageId);
    }

    public static boolean sendMessage(String apikey,
            String from,
            String personalName,
            String to,
            String cc,
            String subject,
            String text,
            int maxAttachmentSize,
            List<Attachment> attachments) throws Exception {
        
        Email sgFrom = new Email(from, personalName);
        Email sgTo = new Email(to);
        Email sgCc = null;
        if (StringUtils.isNotBlank(cc)) {
            sgCc = new Email(cc);
        }
        
        // html
        String content;
        if (StringUtils.startsWithIgnoreCase(text, "<!DOCTYPE")) {
            // received an html text
            content = text;
        } else {
            // received a plain text
            content =
                    "<!DOCTYPE html>\n" +
                    "<html>\n" +
                    "    <head>\n" + 
                    "    </head>\n" + 
                    "    <body>\n" +
                    "        <p>" + htmlNewLiner(text) + "</p>\n" +
                    "    </body>\n" +
                    "</html>\n";
        }
        Content sgContent = new Content("text/html", content);
        Mail mail = new Mail(sgFrom, subject, sgTo, sgContent);
        if (sgCc != null) {
            Personalization persCc = new Personalization();
            persCc.addCc(sgCc);
            mail.addPersonalization(persCc);
        }
        
        // attachments
        if (attachments != null) {
            
            // ?????? @mike: non ho provato l'invio di più allegati in un solo email
            
            for (Attachment attachment : attachments) {
                
                File attachmentFile = new File(attachment.getFilename());
                try {
                    InputStream attachmentStream = FileUtils.openInputStream(attachmentFile);
                    try {
                        com.sendgrid.Attachments att = new com.sendgrid.Attachments.Builder(attachment.getName(), attachmentStream)
                                .withType(StringUtils.defaultIfBlank(attachment.getType(), "text/plain"))
                                .withContentId(attachment.getName())
                                .withDisposition("attachment")
                                .build();
                        mail.addAttachments(att);
                    } finally {
                        IOUtils.closeQuietly(attachmentStream);
                    }
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
            }
        }
        
        // disable tracking
        TrackingSettings trackingSettings = new TrackingSettings();
        
        trackingSettings.setClickTrackingSetting(new ClickTrackingSetting());
        trackingSettings.getClickTrackingSetting().setEnable(false);
        
        trackingSettings.setGoogleAnalyticsSetting(new GoogleAnalyticsSetting());
        trackingSettings.getGoogleAnalyticsSetting().setEnable(false);
        
        trackingSettings.setOpenTrackingSetting(new OpenTrackingSetting());
        trackingSettings.getOpenTrackingSetting().setEnable(false);
        
        trackingSettings.setSubscriptionTrackingSetting(new SubscriptionTrackingSetting());
        trackingSettings.getSubscriptionTrackingSetting().setEnable(false);
        
        mail.setTrackingSettings(trackingSettings);
        
        // send
        SendGrid sg = new SendGrid(apikey);
        Request request = new Request();
        
        boolean sent = false;
        try {
            request.setMethod(Method.POST);
            request.setEndpoint("mail/send");
            request.setBody(mail.build());
            Response response = sg.api(request);
            
            sent = (response.getStatusCode() >= 200) && (response.getStatusCode() < 300);
            if (!sent) {
                LOGGER.error("status " + response.getStatusCode());
                LOGGER.error("headers " + response.getHeaders().toString());
                LOGGER.error("body " + response.getBody());
            }
        } catch (IOException ex) {
            LOGGER.error("suppressed ", ex);
        }        

        return sent;
    }

    public static boolean writeMessageToFile(String host, int port, boolean authentication, String authenticationUser, String authenticationPassword, boolean emailEncryption, boolean startTls, String from, String personalName, String to, String cc, String subject, String text, int maxAttachmentSize, List<Attachment> attachments, String filename) throws Exception {
        HtmlEmail email = new HtmlEmail();
        email.setHostName(host);
        email.setSmtpPort(port);
        email.setCharset("UTF-8");
        email.setFrom(from, personalName);
        email.addTo(to);
        if (StringUtils.isNotBlank(cc)) {
            email.addCc(cc);
        }
        email.setSubject(subject);

        // html
        StringBuilder html = new StringBuilder();
        if (StringUtils.startsWithIgnoreCase(text, "<!DOCTYPE html>")) {
            // received an html text
            html.append(text);
        } else {
            // received a plain text
            if (StringUtils.contains(text, "\r\n")) {
                text = StringUtils.replace(text, "\r\n", "<br>\r\n");
            } else {
                if (StringUtils.contains(text, "\n")) {
                    text = StringUtils.replace(text, "\n", "<br>\n");
                }
            }
            html.append("<!DOCTYPE html>\n");
            html.append("<html>\n");
            html.append("    <head>\n");
            html.append("    </head>\n");
            html.append("    <body>\n");
            html.append("        <p>").append(text).append("</p>\n");
            html.append("    </body>\n");
            html.append("</html>\n");
        }
        email.setHtmlMsg(html.toString());

        // attachments
        if (attachments != null) {
            for (Attachment attachment : attachments) {
                EmailAttachment tmp = new EmailAttachment();
                tmp.setPath(attachment.getFilename());
                tmp.setDisposition(EmailAttachment.ATTACHMENT);
                tmp.setDescription(attachment.getName());
                tmp.setName(attachment.getName());
                email.attach(tmp);
            }
        }
        email.buildMimeMessage();
        FileUtils.forceMkdir(new File(FilenameUtils.getFullPath(filename)));
        try (OutputStream out = new FileOutputStream(new File(filename));) {
            email.getMimeMessage().writeTo(out);
        }
        return true;
    }
    
    public static boolean writeMessageToFile(String apikey, String from, String personalName, String to, String cc, String subject, String text, int maxAttachmentSize, List<Attachment> attachments, String filename) throws Exception {
        HtmlEmail email = new HtmlEmail();
        email.setCharset("UTF-8");
        email.setFrom(from, personalName);
        email.addTo(to);
        if (StringUtils.isNotBlank(cc)) {
            email.addCc(cc);
        }
        email.setSubject(subject);

        // html
        StringBuilder html = new StringBuilder();
        if (StringUtils.startsWithIgnoreCase(text, "<!DOCTYPE html>")) {
            // received an html text
            html.append(text);
        } else {
            // received a plain text
            if (StringUtils.contains(text, "\r\n")) {
                text = StringUtils.replace(text, "\r\n", "<br>\r\n");
            } else {
                if (StringUtils.contains(text, "\n")) {
                    text = StringUtils.replace(text, "\n", "<br>\n");
                }
            }
            html.append("<!DOCTYPE html>\n");
            html.append("<html>\n");
            html.append("    <head>\n");
            html.append("    </head>\n");
            html.append("    <body>\n");
            html.append("        <p>").append(text).append("</p>\n");
            html.append("    </body>\n");
            html.append("</html>\n");
        }
        email.setHtmlMsg(html.toString());

        // attachments
        if (attachments != null) {
            for (Attachment attachment : attachments) {
                EmailAttachment tmp = new EmailAttachment();
                tmp.setPath(attachment.getFilename());
                tmp.setDisposition(EmailAttachment.ATTACHMENT);
                tmp.setDescription(attachment.getName());
                tmp.setName(attachment.getName());
                email.attach(tmp);
            }
        }
        email.buildMimeMessage();
        FileUtils.forceMkdir(new File(FilenameUtils.getFullPath(filename)));
        try (OutputStream out = new FileOutputStream(new File(filename));) {
            email.getMimeMessage().writeTo(out);
        }
        return true;
    }
    
    public static boolean validEmailAddress(String address) {
		// this function validates a single email address 
        boolean result = false;
        if (StringUtils.isNotBlank(address)) {
            result = address.trim().toLowerCase().matches(VALID_EMAIL_PATTERN);
        }
        return result;
    }

    public static boolean validEmailAddresses(String addresses) {
		// this function validates a single email address 
		// or a list of addresses (separed by ";" or "," or " - ")
        boolean result = false;
        if (StringUtils.isNotBlank(addresses)) {
			addresses = addresses.trim();
			if (StringUtils.containsAny(addresses, ",;")) {
				String[] parts = StringUtils.split(addresses.replace(";", ","), ",");
				if ((parts != null) && (parts.length > 0)) {
					result = true;
					for (String part : parts) {
						result &= validEmailAddress(part);
					}
				}
			} else {
                result = validEmailAddress(addresses);
			}
        }
        return result;
    }

    public static String htmlNewLiner(String text) {
        if (StringUtils.isNotBlank(text)) {
            if (StringUtils.contains(text, "\r\n")) {
                text = StringUtils.replace(text, "\r\n", "<br>\r\n");
            } else {
                if (StringUtils.contains(text, "\n")) {
                    text = StringUtils.replace(text, "\n", "<br>\n");
                }
            }
        }
        return text;
    }
    
}
