package com.miocontotermico.util;

import java.nio.file.Files;
import java.nio.file.Path;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class TemporaryUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(TemporaryUtils.class.getName());

    public static String saveTemporaryFile(byte[] data, String extension) {
        String filename = null;
        
        if ((data != null) &&
                (data.length > 0)) {
            
            // temporary folder
            Path folder = null;
            try {
                folder = Files.createTempDirectory("fly");
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }

            // temporary file
            Path file = null;
            if (folder != null) {

                try {
                    file = Files.createTempFile(folder, "fly", "." + extension);
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }

            } else {
                LOGGER.error("unable to create temporary folder");
            }

            if (file != null) {
                try {
                    FileUtils.writeByteArrayToFile(file.toFile(), data);
                    filename = file.toString();
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
            } else {
                LOGGER.error("unable to create temporary file");
            }
            
        }
        
        return filename;
    }
    
}
