package com.miocontotermico.util;

import com.miocontotermico.core.Manager;
import com.miocontotermico.support.file.posted.PostedFile;
import com.miocontotermico.support.image.slim.SlimImage;
import java.beans.PropertyDescriptor;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.beanutils.ConvertUtils;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.beanutils.converters.DateConverter;
import org.apache.commons.beanutils.converters.DateTimeConverter;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;

/**
 *
 * <AUTHOR>
 */
public class PojoUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(PojoUtils.class.getName());

    static {
        DateTimeConverter dtConverter = new DateConverter();
        dtConverter.setPattern("dd/MM/yyyy");
        ConvertUtils.register(dtConverter, Date.class);        
    }
    
    public static <T> T createFromRequest(Request request, Class<T> objectClass) {
        T result = null;
        if (request != null) {
            try {
                result = objectClass.newInstance();
            } catch (InstantiationException | IllegalAccessException | RuntimeException ex) {
                LOGGER.error("suppressed", ex);
            }
            if (result != null) {
                for (String name : request.queryParams()) {
                    
                    // property descriptor
                    PropertyDescriptor descriptor = null;
                    try {
                        descriptor = PropertyUtils.getPropertyDescriptor(result, name);
                    } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException | RuntimeException ex) {
                        LOGGER.error("suppressed", ex);
                    }
                    
                    if (descriptor != null) {
                        
                        // param value
                        String value = request.queryParams(name);

                        // on objectId fields, nulling "-" values
                        if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "ObjectId")) {
                            value = !StringUtils.equals(value, "-") ? value : null;
                            value = !StringUtils.equals(value, "") ? value : null;
                        }
                        
                        // on date fields, nulling empty string values
                        if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "Date")) {
                            value = StringUtils.isNotBlank(value) ? value : null;
                        }
                        
                        // on date fields, nulling empty string values
                        if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "Integer")) {
                            value = StringUtils.isNotBlank(value) ? value : null;
                        }

                        // on double fields, remove thousand separators
                        if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "Double")) {
                            if (StringUtils.contains(StringUtils.right(value, 3), ".")) {
                                value = StringUtils.remove(value, ",");
                            } else if (StringUtils.contains(StringUtils.right(value, 3), ",")) {
                                value = StringUtils.remove(value, ".");
                            } else {
                                value = StringUtils.remove(value, ",");
                                value = StringUtils.remove(value, ".");
                            }
                            
                        }
                        
                        // on double fields, handling "," decimal separator as "."
                        if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "Double")) {
                            value = StringUtils.replace(value, ",", ".");
                        }
                        
                        // setting value from request param
                        if (value != null) {
                            // manual pre-conversion of objectId references
                            Object obj = value;
                            if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "ObjectId")) {
                                try {
                                    obj = new ObjectId(value);
                                } catch (Exception ex) {
                                    LOGGER.error("suppressed", ex);
                                }
                            }
                            // manual pre-conversion of date and datetime references
                            if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "Date")) {
                                if (value.length() > 10) {
                                    Date datetime = TimeUtils.toDate(value, "dd/MM/yyyy HH:mm");
                                    if (datetime != null) {
                                        obj = datetime;
                                    }
                                }
                            }
                            try {
                                BeanUtils.setProperty(result, name, obj);
                            } catch (IllegalAccessException | InvocationTargetException | RuntimeException ex) {
                                LOGGER.error("suppressed", ex);
                            }
                        } else {
                            // workaround to avoid BeanUtils limitation
                            try {
                                descriptor.getWriteMethod().invoke(result, (Object) null);
                            } catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException ex) {
                                LOGGER.error("suppressed", ex);
                            }
                        }
                        
                    }
                }
            }
        }
        return result;
    }

    public static <T> T mergeFromRequest(Request request, T object) {
        if (request != null) {
            if (object != null) {
                for (String name : request.queryParams()) {
                    
                    // property descriptor
                    PropertyDescriptor descriptor = null;
                    try {
                        descriptor = PropertyUtils.getPropertyDescriptor(object, name);
                    } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException | RuntimeException ex) {
                        LOGGER.error("suppressed", ex);
                    }
                    
                    if (descriptor != null) {
                        
                        // param value
                        String value = request.queryParams(name);

                        // on objectId fields, nulling "-" values
                        if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "ObjectId")) {
                            value = !StringUtils.equals(value, "-") ? value : null;
                            value = !StringUtils.equals(value, "") ? value : null;
                        }
                        
                        // on date fields, nulling empty string values
                        if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "Date")) {
                            value = StringUtils.isNotBlank(value) ? value : null;
                        }
                        // on date fields, nulling empty string values
                        if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "Integer")) {
                            value = StringUtils.isNotBlank(value) ? value : null;
                        }
                        // on double fields, remove thousand separators
                        if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "Double")) {
                            if (StringUtils.contains(StringUtils.right(value, 3), ".")) {
                                value = StringUtils.remove(value, ",");
                            } else if (StringUtils.contains(StringUtils.right(value, 3), ",")) {
                                value = StringUtils.remove(value, ".");
                            } else {
                                value = StringUtils.remove(value, ",");
                                value = StringUtils.remove(value, ".");
                            }
                            
                        }
                        
                        // on double fields, handling "," decimal separator as "."
                        if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "Double")) {
                            value = StringUtils.replace(value, ",", ".");
                        }
                        
                        // setting value from request param
                        if (value != null) {
                            // manual pre-conversion of objectId references
                            Object obj = value;
                            if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "ObjectId")) {
                                try {
                                    obj = new ObjectId(value);
                                } catch (Exception ex) {
                                    LOGGER.error("suppressed", ex);
                                }
                            }
                            // manual pre-conversion of date and datetime references
                            if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "Date")) {
                                if (value.length() > 10) {
                                    Date datetime = TimeUtils.toDate(value, "dd/MM/yyyy HH:mm");
                                    if (datetime != null) {
                                        obj = datetime;
                                    }
                                }
                            }
                            try {
                                BeanUtils.setProperty(object, name, obj);
                            } catch (IllegalAccessException | InvocationTargetException | RuntimeException ex) {
                                LOGGER.error("suppressed", ex);
                            }
                        } else {
                            // workaround to avoid BeanUtils limitation
                            try {
                                descriptor.getWriteMethod().invoke(object, (Object) null);
                            } catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException ex) {
                                LOGGER.error("suppressed", ex);
                            }
                        }
                        
                    }
                }
            }
        }
        return object;
    }

    public static <T> T createFromAjaxRequest(Request request, Class<T> objectClass) {
        return createFromAjaxRequest(request, objectClass, null);
    }
    
    public static <T> T createFromAjaxRequest(Request request, Class<T> objectClass, Map<String, String> customParams) {
        T result = null;
        if (request != null) {
            try {
                result = objectClass.newInstance();
            } catch (InstantiationException | IllegalAccessException | RuntimeException ex) {
                LOGGER.error("suppressed", ex);
            }
            if (result != null) {
                
                List<FileItem> fields = null;
                try {
                    fields = new ServletFileUpload(new DiskFileItemFactory()).parseRequest(request.raw());
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                
                if (fields != null) {
                    // multipart fields parsing
                    for (FileItem field : fields) {
                        if (field.isFormField()) {
                            
                            // posted field
                            String name = field.getFieldName();

                            // property descriptor
                            PropertyDescriptor descriptor = null;
                            try {
                                descriptor = PropertyUtils.getPropertyDescriptor(result, name);
                            } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException | RuntimeException ex) {
                                LOGGER.error("suppressed", ex);
                            }

                            if (descriptor != null) {

                                // param value
                                String value = null;
                                try {
                                    value = field.getString("UTF-8");
                                } catch (Exception ex) {
                                    LOGGER.error("suppressed", ex);
                                }

                                // on objectId fields, nulling "-" values
                                if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "ObjectId")) {
                                    value = !StringUtils.equals(value, "-") ? value : null;
                                    value = !StringUtils.equals(value, "") ? value : null;
                                }
                                
                                // on date fields, nulling empty string values
                                if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "Date")) {
                                    value = StringUtils.isNotBlank(value) ? value : null;
                                }
                                
                                // on date fields, nulling empty string values
                                if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "Integer")) {
                                    value = StringUtils.isNotBlank(value) ? value : null;
                                }

                                // on double fields, remove thousand separators
                                if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "Double")) {
                                    if (StringUtils.contains(StringUtils.right(value, 3), ".")) {
                                        value = StringUtils.remove(value, ",");
                                    } else if (StringUtils.contains(StringUtils.right(value, 3), ",")) {
                                        value = StringUtils.remove(value, ".");
                                    } else {
                                        value = StringUtils.remove(value, ",");
                                        value = StringUtils.remove(value, ".");
                                    }

                                }
                                
                                // on double fields, handling "," decimal separator as "."
                                if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "Double")) {
                                    value = StringUtils.replace(value, ",", ".");
                                }
                                
                                // setting value from request param
                                if (value != null) {
                                    // manual pre-conversion of objectId references
                                    Object obj = value;
                                    if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "ObjectId")) {
                                        try {
                                            obj = new ObjectId(value);
                                        } catch (Exception ex) {
                                            LOGGER.error("suppressed", ex);
                                        }
                                    }
                                    // manual pre-conversion of date and datetime references
                                    if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "Date")) {
                                        if (value.length() > 10) {
                                            Date datetime = TimeUtils.toDate(value, "dd/MM/yyyy HH:mm");
                                            if (datetime != null) {
                                                obj = datetime;
                                            }
                                        }
                                    }
                                    try {
                                        BeanUtils.setProperty(result, name, obj);
                                    } catch (IllegalAccessException | InvocationTargetException | RuntimeException ex) {
                                        LOGGER.error("suppressed", ex);
                                    }
                                } else {
                                    // workaround to avoid BeanUtils limitation
                                    try {
                                        descriptor.getWriteMethod().invoke(result, (Object) null);
                                    } catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException ex) {
                                        LOGGER.error("suppressed", ex);
                                    }
                                }

                            } else {
                                if (customParams != null) {
                                    if (customParams.containsKey(name)) {
                                        // param value
                                        String value = null;
                                        try {
                                            value = field.getString("UTF-8");
                                        } catch (Exception ex) {
                                            LOGGER.error("suppressed", ex);
                                        }
                                        // setting custom param
                                        customParams.put(name, value);
                                    }
                                }
                            }
                            
                        }
                    }
                }
            }
        }
        return result;
    }

    public static <T> T mergeFromAjaxRequest(Request request, T object) {
        return mergeFromAjaxRequest(request, object, null);
    }
    
    public static <T> T mergeFromAjaxRequest(Request request, T object, Map<String, String> customParams) {
        if (request != null) {
            if (object != null) {
                
                List<FileItem> fields = null;
                try {
                    fields = new ServletFileUpload(new DiskFileItemFactory()).parseRequest(request.raw());
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                
                if (fields != null) {
                    // multipart fields parsing
                    for (FileItem field : fields) {
                        if (field.isFormField()) {
                            
                            // posted field
                            String name = field.getFieldName();

                            // property descriptor
                            PropertyDescriptor descriptor = null;
                            try {
                                descriptor = PropertyUtils.getPropertyDescriptor(object, name);
                            } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException | RuntimeException ex) {
                                LOGGER.error("suppressed", ex);
                            }

                            if (descriptor != null) {

                                // param value
                                String value = null;
                                try {
                                    value = field.getString("UTF-8");
                                } catch (Exception ex) {
                                    LOGGER.error("suppressed", ex);
                                }

                                // on objectId fields, nulling "-" values
                                if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "ObjectId")) {
                                    value = !StringUtils.equals(value, "-") ? value : null;
                                    value = !StringUtils.equals(value, "") ? value : null;
                                }
                                
                                // on date fields, nulling empty string values
                                if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "Date")) {
                                    value = StringUtils.isNotBlank(value) ? value : null;
                                }
                                
                                // on date fields, nulling empty string values
                                if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "Integer")) {
                                    value = StringUtils.isNotBlank(value) ? value : null;
                                }

                                // on double fields, remove thousand separators
                                if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "Double")) {
                                    if (StringUtils.contains(StringUtils.right(value, 3), ".")) {
                                        value = StringUtils.remove(value, ",");
                                    } else if (StringUtils.contains(StringUtils.right(value, 3), ",")) {
                                        value = StringUtils.remove(value, ".");
                                    } else {
                                        value = StringUtils.remove(value, ",");
                                        value = StringUtils.remove(value, ".");
                                    }

                                }
                                
                                // on double fields, handling "," decimal separator as "."
                                if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "Double")) {
                                    value = StringUtils.replace(value, ",", ".");
                                }
                                
                                // setting value from request param
                                if (value != null) {
                                    // manual pre-conversion of objectId references
                                    Object obj = value;
                                    if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "ObjectId")) {
                                        try {
                                            obj = new ObjectId(value);
                                        } catch (Exception ex) {
                                            LOGGER.error("suppressed", ex);
                                        }
                                    }
                                    // manual pre-conversion of date and datetime references
                                    if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "Date")) {
                                        if (value.length() > 10) {
                                            Date datetime = TimeUtils.toDate(value, "dd/MM/yyyy HH:mm");
                                            if (datetime != null) {
                                                obj = datetime;
                                            }
                                        }
                                    }
                                    try {
                                        BeanUtils.setProperty(object, name, obj);
                                    } catch (IllegalAccessException | InvocationTargetException | RuntimeException ex) {
                                        LOGGER.error("suppressed", ex);
                                    }
                                } else {
                                    // workaround to avoid BeanUtils limitation
                                    try {
                                        descriptor.getWriteMethod().invoke(object, (Object) null);
                                    } catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException ex) {
                                        LOGGER.error("suppressed", ex);
                                    }
                                }

                            } else {
                                if (customParams != null) {
                                    if (customParams.containsKey(name)) {
                                        // param value
                                        String value = null;
                                        try {
                                            value = field.getString("UTF-8");
                                        } catch (Exception ex) {
                                            LOGGER.error("suppressed", ex);
                                        }
                                        // setting custom param
                                        customParams.put(name, value);
                                    }
                                }
                            }
                            
                        }
                    }
                }
            }
        }
        return object;
    }
    
    public static Map<String, String> paramsFromRequest(Request request) {
        return paramsFromRequest(request, null, null);
    }
    
    public static Map<String, String> paramsFromRequest(Request request, List<SlimImage> slims, Map<String, List<PostedFile>> files) {
        Map<String, String> params = null;
        if (request != null) {
            // assure params original ordering
            params = new LinkedHashMap<>();

            if (StringUtils.startsWithIgnoreCase(request.contentType(), "multipart/form-data")) {

                // MULTIPART
                List<FileItem> fields = null;
                try {
                    fields = new ServletFileUpload(new DiskFileItemFactory()).parseRequest(request.raw());
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }

                if (fields != null) {
                    // multipart fields parsing
                    for (FileItem field : fields) {
                        if (field.isFormField()) {

                            // posted field
                            String name = field.getFieldName();
                            String value = null;
                            try {
                                value = field.getString("UTF-8");
                            } catch (Exception ex) {
                                LOGGER.error("suppressed", ex);
                            }

                            // setting value
                            if (StringUtils.isNotBlank(params.get(name))) {
                                // accumulating values for fields with the same name
                                params.put(name, params.get(name) + "|" + value);
                            } else {
                                params.put(name, value);
                            }

                            // setting slims
                            if (slims != null) {
                                if (StringUtils.startsWithIgnoreCase(name, "uploaded-files")) {
                                    if (StringUtils.isNotBlank(value)) {

                                        SlimImage slim = null;
                                        try {
                                            slim = Manager.deserializeFromJson(value, SlimImage.class);
                                        } catch (Exception ex) {
                                            LOGGER.error("suppressed", ex);
                                        }
                                        if (slim != null) {
                                            if (!slim.isEmpty()) {
                                                slims.add(slim);
                                            }
                                        }

                                    }
                                }
                            }
                            
                        } else {
                            if (files != null) {
                                // posted files (skip empty)
                                if (field.getSize() > 0L) {

                                    String name = FilenameUtils.getName(field.getName());
                                    String extension = FilenameUtils.getExtension(field.getName());

                                    try (InputStream content = field.getInputStream()) {
                                        String filename = TemporaryUtils.saveTemporaryFile(IOUtils.toByteArray(content), extension);
                                        if (!files.containsKey(field.getFieldName())) {
                                            files.put(field.getFieldName(), new ArrayList<>());
                                        }
                                        files.get(field.getFieldName()).add(new PostedFile(filename, name, field.getContentType(), extension));
                                    } catch (IOException ex) {
                                        LOGGER.error("unable to save file " + name, ex);
                                    }

                                }
                            }
                        }

                    }
                }

            } else {

                // STANDARD
                for (String name : request.queryParams()) {

                    // posted field
                    String[] values = request.queryParamsValues(name);

                    // setting value
                    if (values != null) {
                        if (values.length == 1) {
                            params.put(name, values[0]);
                        } else {
                            for (String value : values) {
                                if (StringUtils.isNotBlank(params.get(name))) {
                                    // accumulating values for fields with the same name
                                    params.put(name, params.get(name) + "|" + value);
                                } else {
                                    params.put(name, value);
                                }
                            }
                        }
                    }

                    // setting slims
                    if (slims != null) {
                        if (StringUtils.startsWithIgnoreCase(name, "uploaded-files")) {
                            
                            if (values != null) {
                                if (values.length == 1) {
                                    
                                    if (StringUtils.isNotBlank(values[0])) {
                                        SlimImage slim = null;
                                        try {
                                            slim = Manager.deserializeFromJson(values[0], SlimImage.class);
                                        } catch (Exception ex) {
                                            LOGGER.error("suppressed", ex);
                                        }
                                        if (slim != null) {
                                            if (!slim.isEmpty()) {
                                                slims.add(slim);
                                            }
                                        }
                                    }
                                    
                                }
                            }
                        }
                    }
                }

            }
        }
        return params;
    }

    public static <T> T createFromParams(Map<String, String> params, Class<T> objectClass) {
        T result = null;
        if (params != null) {
            try {
                result = objectClass.newInstance();
            } catch (InstantiationException | IllegalAccessException | RuntimeException ex) {
                LOGGER.error("suppressed", ex);
            }
            if (result != null) {
                result = mergeFromParams(params, result);
            }
        }
        return result;
    }
    
    public static <T> T mergeFromParams(Map<String, String> params, T object) {
        if (params != null) {
            if (object != null) {
                for (String name : params.keySet()) {
                    
                    // property descriptor
                    PropertyDescriptor descriptor = null;
                    try {
                        descriptor = PropertyUtils.getPropertyDescriptor(object, name);
                    } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException | RuntimeException ex) {
                        LOGGER.error("suppressed", ex);
                    }
                    
                    if (descriptor != null) {
                        
                        // param value
                        String value = params.get(name);
                        
                        // on objectId fields, nulling "-" values
                        if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "ObjectId")) {
                            value = !StringUtils.equals(value, "-") ? value : null;
                            value = !StringUtils.equals(value, "") ? value : null;
                        }
                        
                        // on date fields, nulling empty string values
                        if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "Date")) {
                            value = StringUtils.isNotBlank(value) ? value : null;
                        }
                        
                        // on integer fields, nulling empty string values
                        if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "Integer")) {
                            value = StringUtils.isNotBlank(value) ? value : null;
                        }
                        
                        // on double fields, handling "," as "."
                        if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "Double")) {
                            value = StringUtils.replace(value, ",", ".");
                        }
                        
                        // setting value from request param
                        if (value != null) {
                            // manual pre-conversion of objectId references
                            Object obj = value;
                            if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "ObjectId")) {
                                try {
                                    obj = new ObjectId(value);
                                } catch (Exception ex) {
                                    LOGGER.error("suppressed", ex);
                                }
                            }
                            // manual pre-conversion of date and datetime references
                            if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "Date")) {
                                if (value.length() > 10) {
                                    Date datetime = TimeUtils.toDate(value, "dd/MM/yyyy HH:mm");
                                    if (datetime != null) {
                                        obj = datetime;
                                    }
                                }
                            }
                            // manual pre-conversion of string[] references
                            if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "String[]")) {
                                if (StringUtils.equals(obj.getClass().getSimpleName(), "String")) {
                                    obj = StringUtils.split((String) obj, "|");
                                }
                            }
                            try {
                                BeanUtils.setProperty(object, name, obj);
                            } catch (IllegalAccessException | InvocationTargetException | RuntimeException ex) {
                                LOGGER.error("suppressed", ex);
                            }
                        } else {
                            // workaround to avoid BeanUtils limitation
                            try {
                                descriptor.getWriteMethod().invoke(object, (Object) null);
                            } catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException ex) {
                                LOGGER.error("suppressed", ex);
                            }
                        }
                        
                    }
                }
            }
        }
        return object;
    }

}
