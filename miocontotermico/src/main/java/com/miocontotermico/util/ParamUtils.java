package com.miocontotermico.util;

import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class ParamUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(ParamUtils.class.getName());
    
    public static String emptyToNull(String value) {
        return StringUtils.isEmpty(value) ? null : value;
    }

    public static String lineToNull(String value) {
        return emptyToNull(StringUtils.remove(value, "-"));
    }
    
    public static ObjectId toObjectId(String value) {
        ObjectId oid = null;
        try {
            oid = StringUtils.isNotBlank(value) ? (new ObjectId(value)) : null;
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        return oid;
    }

    public static String[] toStrings(String value) {
        String separator = "|";
        if (!StringUtils.contains(value, separator)) {
            separator = ",";
        }
        String[] result = StringUtils.isNotBlank(value) ? (StringUtils.split(value, separator)) : null;
        if (result != null) {
            if (result.length == 0) {
                result = null;
            }
        }
        return result;
    }
    
    public static ObjectId[] toObjectIds(String value) {
        return toObjectIds(toStrings(value));
    }

    public static ObjectId[] toObjectIds(String[] values) {
        ObjectId[] result = null;
        if ((values != null) &&
                (values.length > 0)) {
            result = new ObjectId[values.length];
            for (int i = 0; i < values.length; i++) {
                result[i] = toObjectId(values[i]);
            }
        }
        return result;
    }

}
