package com.miocontotermico.util;

import java.util.Base64;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class ImageUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(ImageUtils.class.getName());

    public static String toDatauri(String contentType, byte[] bytes) {
        String datauri = null;
        
        if (StringUtils.isNotBlank(contentType) &&
                (bytes != null)) {

                // create "data URI"
                StringBuilder sb = new StringBuilder();
                sb.append("data:");
                sb.append(contentType);
                sb.append(";base64,");
                sb.append(Base64.getEncoder().encodeToString(bytes));
                
                datauri = sb.toString();
        } else {
            LOGGER.error("empty content type or bytes");
        }
        
        return datauri;
    }
    
}
