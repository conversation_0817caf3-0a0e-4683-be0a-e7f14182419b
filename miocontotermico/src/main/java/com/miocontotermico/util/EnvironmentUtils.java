package com.miocontotermico.util;

import java.io.File;
import org.apache.commons.lang3.StringUtils;
import spark.Request;

/**
 *
 * <AUTHOR>
 */
public class EnvironmentUtils {

    private static Boolean local;
    
    public static boolean isLocal() {
        if (local == null) {
            local = (new File("/projects")).exists();
        }
        return local;
    }

    public static boolean isNotLocal() {
        return !isLocal();
    }

    public static boolean hasDebug() {
        return isLocal();
    }
    
    public static boolean hasNotDebug() {
        return !hasDebug();
    }
    
    public static boolean hasResourcesHotDeploy() {
        return isLocal();
    }
    
    public static boolean hasNotResourcesHotDeploy() {
        return !hasResourcesHotDeploy();
    }

    public static boolean hasDomainRedirect(Request request) {
        // domain redirect when request host name:
        // - exists
        // - is NOT an ip address
        // - contains a dot
        
        String host = request != null ? request.headers("Host") : null;
        return (host != null) &&
                !host.matches("^(?:[0-9]{1,3}\\.){3}[0-9]{1,3}$") &&
                !StringUtils.containsIgnoreCase(host, ".contabo") &&
                StringUtils.contains(StringUtils.substringBefore(host, ":"), ".");
    }
   
    public static boolean hasNotDomainRedirect(Request request) {
        return !hasDomainRedirect(request);
    }

    public static String domain(Request request) {
        return StringUtils.substringBefore(request.host(), ":");
    }
    
}
