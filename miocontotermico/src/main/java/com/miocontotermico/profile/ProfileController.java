package com.miocontotermico.profile;

import com.miocontotermico.core.Defaults;
import com.miocontotermico.core.Manager;
import com.miocontotermico.core.Paths;
import com.miocontotermico.core.Templates;
import com.miocontotermico.dao.SmtpDao;
import com.miocontotermico.dao.UserDao;
import com.miocontotermico.login.PasswordHash;
import com.miocontotermico.pojo.Smtp;
import com.miocontotermico.pojo.User;
import com.miocontotermico.pojo.types.ProfileType;
import com.miocontotermico.support.image.slim.SlimImage;
import com.miocontotermico.util.ParamUtils;
import com.miocontotermico.util.PojoUtils;
import com.miocontotermico.util.RouteUtils;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class ProfileController {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(ProfileController.class.getName());

    public static TemplateViewRoute profile = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        
        if (user != null) {
            user = UserDao.loadUser(user.getId());
            Manager.putSession(token, "user", user);
        }
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN) ;
            return Manager.renderEmpty();
        }

        // backUrl
        String backUrl = request.queryParams("backUrl");
        attributes.put("backUrl", backUrl);
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        return Manager.render(Templates.PROFILE, attributes, RouteUtils.pathType(request));
    };
    
    public static Route profile_save = (Request request, Response response) -> {
        // params
        ObjectId oid = ParamUtils.toObjectId(request.queryParams("oid"));
        String backUrl = request.queryParams("backUrl");
        String username = request.queryParams("username");
        String password = request.queryParams("password");
        String passwordConfirmation = request.queryParams("password-confirm");
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }
        if (oid != null) {
            if (isValidPassword(
                    password,
                    passwordConfirmation)) {
                
                if (StringUtils.isNotBlank(username)) {
                    
                    String oldPassword = user.getPassword();
                    
                    User userNew = PojoUtils.mergeFromRequest(request, user);
                    if (userNew != null) {
                        userNew.setEmail(username);
                        
                        UserDao.updateUser(user);
                        
                        if (!StringUtils.equals(oldPassword, password)) {
                            String newPasswordHash = PasswordHash.createHash(password);
                            UserDao.updateUserPassword(user.getId(), newPasswordHash);
                        }
                        user = UserDao.loadUser(user.getId());
                    
                        // image
                        String slim = request.queryParams("uploaded-files");
                        if (StringUtils.isNotBlank(slim)) {
                            SlimImage uploaded = null;  
                            try {
                                uploaded = Manager.deserializeFromJson(slim, SlimImage.class);
                            } catch (Exception ex) {
                                LOGGER.error("suppressed", ex);
                            }
                            if (uploaded != null) {
                                try {
                                    UserDao.updateUserImage(user.getUsername(), oid, uploaded);
                                } catch (Exception ex) {
                                    LOGGER.error("unable to save property image, exception is " + ex);
                                    return false;
                                }
                            }
                        } else {
                            UserDao.removeUserImage(oid);
                        }                

                        user = UserDao.loadUser(user.getId());
                        // update user on redis
                        Manager.putSession(token, "user", user);
                    }
                    if (StringUtils.isNotBlank(backUrl)) {
                        response.redirect(backUrl);
                    }
                    response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.PROFILE));
                } else {
                    response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.PROFILE));
                }
            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.PROFILE));
            }
        }
        
        return null;
    };
    
    public static TemplateViewRoute smtp = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN) ;
            return Manager.renderEmpty();
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        Smtp smtpData = SmtpDao.loadSmtp();
        attributes.put("smtp", smtpData);

        return Manager.render(Templates.SMTP, attributes, RouteUtils.pathType(request));
    };
    
    public static Route smtp_save = (Request request, Response response) -> {
        // params
        ObjectId smtpId = ParamUtils.toObjectId(request.queryParams("smtpId"));
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            response.redirect(RouteUtils.language(RouteUtils.contextPath(request) + Paths.HOME, Defaults.LANGUAGE));
            return Manager.renderEmpty();
        }        
        
        if (smtpId != null) {
            Smtp smtpOld = SmtpDao.loadSmtp();
            Smtp smtpData = PojoUtils.mergeFromRequest(request, smtpOld);
            if (isValidSmtp(smtpData)) {
                SmtpDao.updateSmtp(smtpData);
                response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.SMTP));
            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.SMTP));
            }
        } else {
            // params
            Smtp smtpData = PojoUtils.createFromRequest(request, Smtp.class);
            if (isValidSmtp(smtpData)) {
                SmtpDao.insertSmtp(smtpData);

                // navigation
                response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.SMTP));

            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.SMTP));
            }            
        }
        
        return null;
    };
    
    ////////////
    // internals


    private static boolean isValidPassword(
            String password,
            String passwordConfirmation) {

        boolean valid = StringUtils.isNotBlank(password) &&
                StringUtils.isNotBlank(passwordConfirmation) &&
                StringUtils.equals(password, passwordConfirmation)
                ;
        if (!valid) {
            LOGGER.warn("password " + password);
            LOGGER.warn("passwordConfirmation " + passwordConfirmation);
        }

        return valid;
    }    
    
    private static boolean isValidSmtp(Smtp entity) {
        boolean valid = (entity != null) &&
                StringUtils.isNotBlank(entity.getHostname()) &&
                StringUtils.isNotBlank(entity.getPassword()) &&
                (entity.getPort() != null && entity.getPort() > 0) &&
                StringUtils.isNotBlank(entity.getSender()) &&
                StringUtils.isNotBlank(entity.getUsername())
                ;
        
        if (!valid) {
            if (entity != null) {
                LOGGER.warn(
                        "smtp validation problem:\n" +
                        "- hostname " + entity.getHostname()+ "\n" +
                        "- password " + entity.getPassword()+ "\n" +
                        "- sender " + entity.getSender()+ "\n" + 
                        "- port " + entity.getPort()+ "\n" +
                        "- username " + entity.getUsername()+ "\n"
                );
            } else {
                LOGGER.warn(
                        "smtp validation problem:\n" +
                        "- empty"
                );
            }
        }
        
        return valid;
    } 
}
