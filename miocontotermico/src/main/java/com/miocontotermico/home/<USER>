package com.miocontotermico.home;

import com.miocontotermico.commons.NotificationCommons;
import com.miocontotermico.commons.ProcedureCommons;
import com.miocontotermico.commons.UserCommons;
import com.miocontotermico.core.MailTemplates;
import com.miocontotermico.core.Templates;
import com.miocontotermico.core.Manager;
import com.miocontotermico.core.Paths;
import com.miocontotermico.dao.PostDao;
import com.miocontotermico.dao.SmtpDao;
import com.miocontotermico.dao.UserDao;
import com.miocontotermico.login.PasswordHash;
import com.miocontotermico.message.MessageSender;
import com.miocontotermico.message.SmtpService;
import com.miocontotermico.pojo.Post;
import com.miocontotermico.pojo.Smtp;
import com.miocontotermico.pojo.User;
import com.miocontotermico.util.ParamUtils;
import com.miocontotermico.util.RouteUtils;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class HomeController {
     private static final Logger LOGGER = LoggerFactory.getLogger(HomeController.class.getName());

    public static TemplateViewRoute home = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        return Manager.render(Templates.HOME, attributes, RouteUtils.pathType(request));
    };
    
    public static TemplateViewRoute about = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        return Manager.render(Templates.ABOUT, attributes, RouteUtils.pathType(request));
    };
    
    public static TemplateViewRoute contotermico = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        return Manager.render(Templates.CONTOTERMICO, attributes, RouteUtils.pathType(request));
    };
    
    public static TemplateViewRoute inviapratica = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        return Manager.render(Templates.INVIAPRATICA, attributes, RouteUtils.pathType(request));
    };
    
    public static Route inviapratica_register = (Request request, Response response) -> {
        
        // posted values (multipart fields parsing)
        List<FileItem> fields = new ServletFileUpload(new DiskFileItemFactory()).parseRequest(request.raw());
        String profileType = null;
        String email = null;
        String password = null;
        String passwordConfirm = null;
        
        for (FileItem field : fields) {
            if (field.isFormField()) {
                switch (field.getFieldName()) {
                    case "email":
                        email = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        break;
                    case "password":
                        password = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        break;
                    case "password-confirm":
                        passwordConfirm = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        break;
                    case "profileType":
                        profileType = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        break;
                    default:
                        LOGGER.warn("received unknown field " + field.getFieldName());
                        break;
                }
            } else {
                // posted files
                switch (field.getFieldName()) {
                    default:
                        LOGGER.warn("received unknown file field " + field.getFieldName());
                        break;
                }
            }
        }

                
        if (UserCommons.isValidUserCustomerRegistration(
                email,
                password,
                passwordConfirm,
                profileType)) {
            
            User user = UserDao.loadUserByUsername(email);
            if (user == null) {
                
                // defaults
                String encodedPassword = PasswordHash.createHash(password);
                
                // inserting user
                User adding = new User();
                adding.setEmail(email);
                adding.setUsername(email);
                adding.setPassword(encodedPassword);
                adding.setProfileType(profileType);
                adding.setActive(false);
                
                ObjectId addingId = UserDao.insertUser(adding);

                // reload user
                user = UserDao.loadUser(addingId);
                if (user == null) {
                    throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Registrazione non riuscita");
                }
                // notify
                if (NotificationCommons.notifyRegistration(request, user)) {
                    // ...
                }
                if (NotificationCommons.notifyRegistrationAdmin(request, user)) {
                    // ...
                }
                
                // do login
                boolean remember = true;
                // create redis session
                String token = PasswordHash.getSecureRandom(32);
                Manager.createSession(request, response, token, remember);
                Manager.putSession(token, "user", user);

                
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Indirizzo email già presente");
            }
            
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Registrazione errata");
        }

        return "ok";
    };

    public static TemplateViewRoute contacts = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        
        return Manager.render(Templates.CONTACTS, attributes, RouteUtils.pathType(request));
    };
    
    public static Route contacts_send = (Request request, Response response) -> {
        // posted values (multipart fields parsing)
        List<FileItem> fields = new ServletFileUpload(new DiskFileItemFactory()).parseRequest(request.raw());
        String name = null;
        String email = null;
        String message = null;
        for (FileItem field : fields) {
            if (field.isFormField()) {
                switch (field.getFieldName()) {
                    case "name":
                        name = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        break;
                    case "email":
                        email = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        break;
                    case "message":
                        message = ParamUtils.emptyToNull(field.getString("UTF-8"));
                        break;
                    default:
                        LOGGER.warn("received unknown field " + field.getFieldName());
                        break;
                }
            } else {
                // posted files
                switch (field.getFieldName()) {
                    default:
                        LOGGER.warn("received unknown file field " + field.getFieldName());
                        break;
                }
            }
        }

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);

        if (MessageSender.validEmailAddress(email)) {

            // send registration message
            Map<String, Object> messageFields = new HashMap<>();
            messageFields.put("name", name);
            messageFields.put("email", email);
            messageFields.put("message", message);

            // smtp
            SmtpService smtp = null;
            Smtp tmp = SmtpDao.loadSmtp();
            if (tmp != null) {
                smtp = new SmtpService(
                        tmp.getHostname(),
                        tmp.getPort(),
                        tmp.getAuthentication(),
                        tmp.getUsername(),
                        tmp.getPassword(),
                        tmp.getEncryption(),
                        tmp.getStartTls(),
                        tmp.getApikey(),
                        tmp.getSender()
                );
            }

            if (smtp != null) { 
                // firm
                String sender = smtp.getSender();
                Manager.sendMail(smtp,
                    sender,
                    "no-reply",
                    sender,
                    MailTemplates.CONTACT,
                    messageFields,
                    RouteUtils.pathType(request));
            }  else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }             
            
            // ignoring send results...

        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        // status required
        return "1";
    };

    
    
    public static TemplateViewRoute news = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        // news
        List<Post> postList = PostDao.loadPostList();
        attributes.put("postList", postList);        
        return Manager.render(Templates.NEWS, attributes, RouteUtils.pathType(request));
    };
    
    public static TemplateViewRoute news_detail_identifier = (Request request, Response response) -> {
        String identifier = request.params("identifier");
        
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        
        if (StringUtils.isNotEmpty(identifier)) {
            Post post = PostDao.loadPostByIdentifier(identifier);
            Post firstPost = null;
            Post nextPost = null;
            List<Post> postList = PostDao.loadPostList();
            
            int indexPost = 0;
            
            Post tmpPost;
            for (int i = 0; i < postList.size(); i++) {
                tmpPost = postList.get(i);
                if (tmpPost.getId().equals(post.getId())) {
                    indexPost = i;
                    break;
                }
            }
            
            if (indexPost > 0) {
                firstPost = postList.get(indexPost - 1);
            }
            if (postList.size() > (indexPost + 1)) {
                nextPost = postList.get(indexPost + 1);
            }
            
            attributes.put("post", post);
            attributes.put("firstPost", firstPost);
            attributes.put("nextPost", nextPost);
        } else {
            response.redirect(RouteUtils.contextPath(request) + Paths.HOME);
        }

        return Manager.render(Templates.NEWS_DETAIL, attributes, RouteUtils.pathType(request));
    };    
    
}
