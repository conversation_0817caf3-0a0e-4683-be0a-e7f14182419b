package com.miocontotermico.login;

import com.miocontotermico.core.MailTemplates;
import com.miocontotermico.core.Manager;
import com.miocontotermico.core.Paths;
import com.miocontotermico.core.Templates;
import com.miocontotermico.dao.SmtpDao;
import com.miocontotermico.dao.UserDao;
import com.miocontotermico.message.MessageSender;
import com.miocontotermico.message.SmtpService;
import com.miocontotermico.pojo.Smtp;
import com.miocontotermico.pojo.User;
import com.miocontotermico.util.RouteUtils;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class LoginController {

    private static final Logger LOGGER = LoggerFactory.getLogger(LoginController.class.getName());

    public static TemplateViewRoute login = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));
        
        // backUrl
        String backUrl = request.queryParams("backUrl");
        attributes.put("backUrl", backUrl);

        return Manager.render(Templates.LOGIN, attributes, RouteUtils.pathType(request));
    };

    public static Route login_do = (Request request, Response response) -> {
        // params
        String email = request.queryParams("email");
        String password = request.queryParams("password");
        String backUrl = request.queryParams("backUrl");
        boolean remember = BooleanUtils.toBoolean(request.queryParams("remember"));

        if (areValidCredentials(email, password)) {
            
            User user = UserDao.loadUserByUsername(email);
            if (isAuthenticated(user, password)) {
                
                // create redis session
                String token = PasswordHash.getCookieSafeSecureRandom(32);
                Manager.createSession(request, response, token, remember);
                Manager.putSession(token, "user", user);

                // trace login
                // ??????

                // remove forgot
                if (user.getRecoverySendDate() != null) {
                    
                    UserDao.removeUserRecovery(user.getId());
                    
                    user = UserDao.loadUser(user.getId());

                    if (user != null) {

                        // update user on redis
                        Manager.putSession(token, "user", user);

                    } else {
                        response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.LOGIN));
                    }
                    
                }

                if (StringUtils.isNotBlank(backUrl)) {
                    // backUrl navigation
                    response.redirect(backUrl, HttpStatus.SEE_OTHER_303);
                } else {
                    // navigation
                    response.redirect(RouteUtils.contextPath(request) + Paths.PROCEDURES + "?selectedStatuses=opened%7Cprocessing%7C", HttpStatus.SEE_OTHER_303);
                }
            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.LOGIN) + Paths.back(backUrl)) ;
            }
            
        } else {
            response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.LOGIN) + Paths.back(backUrl)) ;
        }

        return null;
    };

    public static Route logout_do = (Request request, Response response) -> {
        
        // params
        // ...
        
        String token = Manager.getToken(request);
        if (StringUtils.isNotBlank(token)) {
            // trace logout
            // ??????

            // session destroing
            Manager.destroySession(response, token);
        }

        response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
        return null;
    };

    public static TemplateViewRoute forgot = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);

        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));
        
        return Manager.render(Templates.FORGOT, attributes, RouteUtils.pathType(request));
    };

    public static Route forgot_send = (Request request, Response response) -> {
        
        // params
        String email = request.queryParams("email");

        if (MessageSender.validEmailAddress(email)) {
            
            User user = UserDao.loadUserByUsername(email);
            if (user != null) {
                
                UserDao.updateUserRecovery(user.getId());
                
                user = UserDao.loadUser(user.getId());
                
                if (user != null) {
                    
                    // send registration message
                    Map<String, Object> messageFields = new HashMap<>();
                    messageFields.put("user", user);
                    messageFields.put("userUrl", RouteUtils.baseUrl(request) + Paths.LOGIN);
                    
                    // smtp
                    SmtpService smtp = null;
                    Smtp tmp = SmtpDao.loadSmtp();
                    if (tmp != null) {
                        smtp = new SmtpService(
                                tmp.getHostname(),
                                tmp.getPort(),
                                tmp.getAuthentication(),
                                tmp.getUsername(),
                                tmp.getPassword(),
                                tmp.getEncryption(),
                                tmp.getStartTls(),
                                tmp.getApikey(),
                                tmp.getSender()
                        );
                    }

                    if (smtp != null) { 
                        // firm
                        String sender = smtp.getSender();
                        Manager.sendMail(smtp,
                            sender,
                            "no-reply",
                            email,
                            MailTemplates.FORGOT,
                            messageFields,
                            RouteUtils.pathType(request));
                    }  else {
                        throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Recupero non riuscito");
                    }    
                    
                    // ignoring send results...
                    user.setPassword(PasswordHash.createHash(user.getPassword()));
                    UserDao.updateUser(user);

                    // navigation
                    response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.FORGOT));
                    
                } else {
                    response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.FORGOT));
                }
                
            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.FORGOT));
            }
            
        } else {
            response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.FORGOT));
        }

        return null;
    };


    ////////////
    // internals
    
    private static boolean areValidCredentials(String email, String password) {
        return StringUtils.isNotBlank(email) &&
                StringUtils.isNotBlank(password);
    }
    
    private static boolean isAuthenticated(User user, String password) {
        boolean done;
        
        try {
            done = (user != null) &&
                    PasswordHash.validatePassword(password, user.getPassword());
        } catch (NoSuchAlgorithmException | InvalidKeySpecException | ArrayIndexOutOfBoundsException ex) {
             LOGGER.error("unable to validate password, exception in " + ex);
             return false;
        }
        return done;
    }
    
}
