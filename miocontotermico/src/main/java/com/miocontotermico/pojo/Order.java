package com.miocontotermico.pojo;

import java.util.Date;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class Order extends Pojo {

    // ?????? @mike: grab names and concepts from https://www.deskera.com/erp/purchase-management
    
    private String protocol;                // protocollo dell'ordine
    private String status;                  // cart (max un cart per utente), opened, processing, delivered, annulled
    private String orderType;               // contract, proposal, bundle
    private Date lastStatusUpdate;          // ultimo aggiornamento di stato
    private String paymentStatus;           // vuoto (da pagare), paid
    private Date paymentDate;               // data pagamento
    private String note;                    // note
    
    // personalization
    private Boolean customized;             // si tratta di un ordine personalizzato
    private String customizedNote;          // note di personalizzazione
    
    // price
    private Integer credit;
    private Double totalPrice;              // prezzo totale (somma di (itemPrice - itemDiscount) di ogni item)
    private Double totalDiscount;           // sconto manuale sul prezzo totale
    private Double totalCoupon;             // sconto coupon sul prezzo totale
    private Double totalShipment;           // spese di spedizione
    private Double totalSupplement;         // supplemento per modalità di pagamento (es. paypal)
    private Double finalPrice;              // prezzo finito (somma di (totalPrice - totalDiscount - totalCoupon + totalShipment + totalSupplement))
        
    // management
    private Boolean cancelled;
    private Date date;                      // data ordine
    private ObjectId userId;                // inserito da
    private String cartToken;               // carrello e-commerce da utente non loggato
    
    // coupon
    private String couponCode;              // codice coupon

    // payment
    private String paymentCode;             // codice modalità di pagamento
    private Boolean paymentCodeBond;        // modalità forzata da vincolo
    private String transactionKey;          // chiave interna della transazione (per pagamenti online)
    private String externalPaymentKey;      // chiave esterna del pagamento (per pagamenti online)
    private String externalTransactionKey;  // chiave esterna della transazione (per pagamenti online)
    private String subscriptionId;
    
    private Boolean privacy;

    // invoice
    private Boolean invoice;                // fattura
    private String pec;                     // pec
    private String sdiNumber;               // identificativo sdi x fatturazione elettronica
    private String tin;
    private String vatNumber;
    private String fullname;
    private String name;
    private String lastname;
    
    private String city;
    private String address;
    private String provinceCode;
    private String postalCode;
    private String countryCode;
    private String phoneNumber;

    public String getProtocol() {
        return protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getOrderType() {
        return orderType;
    }

    public void setOrderType(String orderType) {
        this.orderType = orderType;
    }

    public Date getLastStatusUpdate() {
        return lastStatusUpdate;
    }

    public void setLastStatusUpdate(Date lastStatusUpdate) {
        this.lastStatusUpdate = lastStatusUpdate;
    }

    public String getPaymentStatus() {
        return paymentStatus;
    }

    public void setPaymentStatus(String paymentStatus) {
        this.paymentStatus = paymentStatus;
    }

    public Date getPaymentDate() {
        return paymentDate;
    }

    public void setPaymentDate(Date paymentDate) {
        this.paymentDate = paymentDate;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }

    public Boolean getCustomized() {
        return customized;
    }

    public void setCustomized(Boolean customized) {
        this.customized = customized;
    }

    public String getCustomizedNote() {
        return customizedNote;
    }

    public void setCustomizedNote(String customizedNote) {
        this.customizedNote = customizedNote;
    }

    public Integer getCredit() {
        return credit;
    }

    public void setCredit(Integer credit) {
        this.credit = credit;
    }

    public Double getTotalPrice() {
        return totalPrice;
    }

    public void setTotalPrice(Double totalPrice) {
        this.totalPrice = totalPrice;
    }

    public Double getTotalDiscount() {
        return totalDiscount;
    }

    public void setTotalDiscount(Double totalDiscount) {
        this.totalDiscount = totalDiscount;
    }

    public Double getTotalCoupon() {
        return totalCoupon;
    }

    public void setTotalCoupon(Double totalCoupon) {
        this.totalCoupon = totalCoupon;
    }

    public Double getTotalShipment() {
        return totalShipment;
    }

    public void setTotalShipment(Double totalShipment) {
        this.totalShipment = totalShipment;
    }

    public Double getTotalSupplement() {
        return totalSupplement;
    }

    public void setTotalSupplement(Double totalSupplement) {
        this.totalSupplement = totalSupplement;
    }

    public Double getFinalPrice() {
        return finalPrice;
    }

    public void setFinalPrice(Double finalPrice) {
        this.finalPrice = finalPrice;
    }

    public Boolean getCancelled() {
        return cancelled;
    }

    public void setCancelled(Boolean cancelled) {
        this.cancelled = cancelled;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public ObjectId getUserId() {
        return userId;
    }

    public void setUserId(ObjectId userId) {
        this.userId = userId;
    }

    public String getCartToken() {
        return cartToken;
    }

    public void setCartToken(String cartToken) {
        this.cartToken = cartToken;
    }

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    public String getPaymentCode() {
        return paymentCode;
    }

    public void setPaymentCode(String paymentCode) {
        this.paymentCode = paymentCode;
    }

    public Boolean getPaymentCodeBond() {
        return paymentCodeBond;
    }

    public void setPaymentCodeBond(Boolean paymentCodeBond) {
        this.paymentCodeBond = paymentCodeBond;
    }

    public String getTransactionKey() {
        return transactionKey;
    }

    public void setTransactionKey(String transactionKey) {
        this.transactionKey = transactionKey;
    }

    public String getExternalPaymentKey() {
        return externalPaymentKey;
    }

    public void setExternalPaymentKey(String externalPaymentKey) {
        this.externalPaymentKey = externalPaymentKey;
    }

    public String getExternalTransactionKey() {
        return externalTransactionKey;
    }

    public void setExternalTransactionKey(String externalTransactionKey) {
        this.externalTransactionKey = externalTransactionKey;
    }

    public String getSubscriptionId() {
        return subscriptionId;
    }

    public void setSubscriptionId(String subscriptionId) {
        this.subscriptionId = subscriptionId;
    }

    public Boolean getPrivacy() {
        return privacy;
    }

    public void setPrivacy(Boolean privacy) {
        this.privacy = privacy;
    }

    public Boolean getInvoice() {
        return invoice;
    }

    public void setInvoice(Boolean invoice) {
        this.invoice = invoice;
    }

    public String getPec() {
        return pec;
    }

    public void setPec(String pec) {
        this.pec = pec;
    }

    public String getSdiNumber() {
        return sdiNumber;
    }

    public void setSdiNumber(String sdiNumber) {
        this.sdiNumber = sdiNumber;
    }

    public String getTin() {
        return tin;
    }

    public void setTin(String tin) {
        this.tin = tin;
    }

    public String getVatNumber() {
        return vatNumber;
    }

    public void setVatNumber(String vatNumber) {
        this.vatNumber = vatNumber;
    }

    public String getFullname() {
        return fullname;
    }

    public void setFullname(String fullname) {
        this.fullname = fullname;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLastname() {
        return lastname;
    }

    public void setLastname(String lastname) {
        this.lastname = lastname;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }
    

}