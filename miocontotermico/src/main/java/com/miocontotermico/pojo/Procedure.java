package com.miocontotermico.pojo;

import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class Procedure extends Pojo {
    
    private String protocol;                // protocollo procedura
    private String status;                  // draft (max un draft per utente), opened, processing, approved, annulled
    private Date lastStatusUpdate;          // ultimo aggiornamento di stato
    
    private String service;
    
    // management
    private Boolean cancelled;
    private Date date;                      // data procedura
    private ObjectId userId;                // inserito da
    private ObjectId assignedUserId;        // inserito da
    private String profileType;             // admin, privato, azienda

    // basic info
    private String lastname;
    private String name;
    private String fullname;
    private String city;
    private String address;
    private String provinceCode;
    private String postalCode;
    private String countryCode;
    private String phoneNumber;
    private String pec;                     // pec
    private String sdiNumber;               // identificativo sdi x fatturazione elettronica
    private String tin;
    private String vatNumber;
    private String iban;
    private String coupon;

    // contact
    private String email;
    
    // privacy
    private Boolean privacy;

    // terms and conditions
    private Boolean terms;
    
    // priority
    private Boolean priority;

    // contact
    private String ownership;               // Proprietario o comproprietario  / Detentore\Utilizzatore
    private String pin;                     // GWA / GSE pin

    /*
        Delega ad operare nel PortalTermico
        Documento d'identità
        Codice fiscale
        Modulo di autorizzazione del proprietario dell'immobile
        Documento d'identità del proprietario dell'immobile
        Fatture e bonifici inerenti all'intervento
        Contratto di finanziamento
        Scheda raccolta dati
        Schede tecniche e certificato ambientale dei nuovi prodotti installati
        Documento di Smaltimento
        Mandato all’incasso
        Documento d’identità mandatario
    */
    private List<ObjectId> thermalPortalDelegationFileIds;
    private List<ObjectId> identityDocumentFileIds;
    private List<ObjectId> tinFileIds;
    private List<ObjectId> authorizationFormFileIds;
    private List<ObjectId> identityDocumentOwnerFormFileIds;
    private List<ObjectId> invoiceFileIds;
    private List<ObjectId> contractFileIds;
    private List<ObjectId> dataCollectionFormFileIds;
    private List<ObjectId> technicalSheetFileIds;
    private List<ObjectId> disposalDocumentFileIds;
    private List<ObjectId> identityDocumentAgentFileIds;
    private List<ObjectId> cashingMandateFileIds;
    
    /*
        Targhe dei generatori sostituiti e installati (di ciascuna delle unità che costituiscono i generatori): *
        Centrale termica, o il locale di installazione, ante-operam (presente il generatore sostituito) e post-operam (presente il generatore installato): *
        Generatori sostituiti e installati: *
        Valvole termostatiche o del sistema di regolazione modulante della portata:
    */
    private List<ObjectId> plate1FileIds;
    private List<ObjectId> thermalPlant1FileIds;
    private List<ObjectId> generator1FileIds;
    private List<ObjectId> valves1FileIds;
    
    /*
        Targhe dei generatori sostituiti e installati (di ciascuna delle unità che costituiscono i generatori)
        Centrale termica, o il locale di installazione, ante-operam (presente il generatore sostituito) e post-operam (presente il generatore installato)
        Generatori sostituiti e installati
        Valvole termostatiche o del sistema di regolazione modulante della portata
        Vista d'insieme del sistema di accumulo termico installato, dove previsto:
    */
    private List<ObjectId> plate2FileIds;
    private List<ObjectId> thermal2PlantFileIds;
    private List<ObjectId> generator2FileIds;
    private List<ObjectId> valves2FileIds;
    private List<ObjectId> globalStorage2FileIds;
    
    /*
        Vista di dettaglio del pannello solare installato: *
        Vista di dettaglio della targa dei collettori solari e/o degli impianti solari termici prefabbricati installati: *
        Vista di dettaglio del bollitore: *
        Vista d'insieme del campo solare in fase di installazione:
        Vista d'insieme del campo solare realizzato:
        Valvole termostatiche o del sistema di regolazione modulante della portata, ove previste
    */
    
    private List<ObjectId> detailPanel3FileIds;
    private List<ObjectId> detailPlate3FileIds;
    private List<ObjectId> detailBoiler3FileIds;
    private List<ObjectId> globalInstalling3FieldFileIds;
    private List<ObjectId> globalField3FileIds;
    private List<ObjectId> valves3FileIds;
    
    /*
        Vista di dettaglio dei generatori sostituiti e installati
        Vista d'insieme dei generatori sostituiti e installati
        Targa dei generatori installati
    */
    private List<ObjectId> detailGenerator4FileIds;
    private List<ObjectId> globalGenerator4FileIds;
    private List<ObjectId> plate4FileIds;

    /*
        Targhe dei generatori sostituiti e installati (di ciascuna delle unità che costituiscono i generatori)
        Generatori sostituiti e installati
        Centrale termica, o il locale di installazione, ante-operam (presente il generatore sostituito) e post-operam (presente il generatore installato)
        Valvole termostatiche o del sistema di regolazione modulante della portata
    */
    private List<ObjectId> plate5FileIds;
    private List<ObjectId> generator5FileIds;
    private List<ObjectId> thermalPlant5FileIds;
    private List<ObjectId> valves5FileIds;
    
    
    private List<ObjectId> finalConventionFileIds;
    private List<ObjectId> finalConventionToSignedIds;
    private List<ObjectId> internalFileIds;

    public String getProtocol() {
        return protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getLastStatusUpdate() {
        return lastStatusUpdate;
    }

    public void setLastStatusUpdate(Date lastStatusUpdate) {
        this.lastStatusUpdate = lastStatusUpdate;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public Boolean getCancelled() {
        return cancelled;
    }

    public void setCancelled(Boolean cancelled) {
        this.cancelled = cancelled;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public ObjectId getUserId() {
        return userId;
    }

    public void setUserId(ObjectId userId) {
        this.userId = userId;
    }

    public ObjectId getAssignedUserId() {
        return assignedUserId;
    }

    public void setAssignedUserId(ObjectId assignedUserId) {
        this.assignedUserId = assignedUserId;
    }

    public String getProfileType() {
        return profileType;
    }

    public void setProfileType(String profileType) {
        this.profileType = profileType;
    }

    public String getLastname() {
        return lastname;
    }

    public void setLastname(String lastname) {
        this.lastname = lastname;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFullname() {
        return fullname;
    }

    public void setFullname(String fullname) {
        this.fullname = fullname;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getPec() {
        return pec;
    }

    public void setPec(String pec) {
        this.pec = pec;
    }

    public String getSdiNumber() {
        return sdiNumber;
    }

    public void setSdiNumber(String sdiNumber) {
        this.sdiNumber = sdiNumber;
    }

    public String getTin() {
        return tin;
    }

    public void setTin(String tin) {
        this.tin = tin;
    }

    public String getVatNumber() {
        return vatNumber;
    }

    public void setVatNumber(String vatNumber) {
        this.vatNumber = vatNumber;
    }

    public String getIban() {
        return iban;
    }

    public void setIban(String iban) {
        this.iban = iban;
    }
    
    public String getCoupon() {
        return coupon;
    }

    public void setCoupon(String coupon) {
        this.coupon = coupon;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Boolean getPrivacy() {
        return privacy;
    }

    public void setPrivacy(Boolean privacy) {
        this.privacy = privacy;
    }

    public Boolean getTerms() {
        return terms;
    }

    public void setTerms(Boolean terms) {
        this.terms = terms;
    }

    public Boolean getPriority() {
        return priority;
    }

    public void setPriority(Boolean priority) {
        this.priority = priority;
    }    
    
    public String getOwnership() {
        return ownership;
    }

    public void setOwnership(String ownership) {
        this.ownership = ownership;
    }

    public String getPin() {
        return pin;
    }

    public void setPin(String pin) {
        this.pin = pin;
    }

    public List<ObjectId> getThermalPortalDelegationFileIds() {
        return thermalPortalDelegationFileIds;
    }

    public void setThermalPortalDelegationFileIds(List<ObjectId> thermalPortalDelegationFileIds) {
        this.thermalPortalDelegationFileIds = thermalPortalDelegationFileIds;
    }

    public List<ObjectId> getIdentityDocumentFileIds() {
        return identityDocumentFileIds;
    }

    public void setIdentityDocumentFileIds(List<ObjectId> identityDocumentFileIds) {
        this.identityDocumentFileIds = identityDocumentFileIds;
    }

    public List<ObjectId> getTinFileIds() {
        return tinFileIds;
    }

    public void setTinFileIds(List<ObjectId> tinFileIds) {
        this.tinFileIds = tinFileIds;
    }

    public List<ObjectId> getAuthorizationFormFileIds() {
        return authorizationFormFileIds;
    }

    public void setAuthorizationFormFileIds(List<ObjectId> authorizationFormFileIds) {
        this.authorizationFormFileIds = authorizationFormFileIds;
    }

    public List<ObjectId> getIdentityDocumentOwnerFormFileIds() {
        return identityDocumentOwnerFormFileIds;
    }

    public void setIdentityDocumentOwnerFormFileIds(List<ObjectId> identityDocumentOwnerFormFileIds) {
        this.identityDocumentOwnerFormFileIds = identityDocumentOwnerFormFileIds;
    }

    public List<ObjectId> getInvoiceFileIds() {
        return invoiceFileIds;
    }

    public void setInvoiceFileIds(List<ObjectId> invoiceFileIds) {
        this.invoiceFileIds = invoiceFileIds;
    }

    public List<ObjectId> getContractFileIds() {
        return contractFileIds;
    }

    public void setContractFileIds(List<ObjectId> contractFileIds) {
        this.contractFileIds = contractFileIds;
    }

    public List<ObjectId> getDataCollectionFormFileIds() {
        return dataCollectionFormFileIds;
    }

    public void setDataCollectionFormFileIds(List<ObjectId> dataCollectionFormFileIds) {
        this.dataCollectionFormFileIds = dataCollectionFormFileIds;
    }

    public List<ObjectId> getTechnicalSheetFileIds() {
        return technicalSheetFileIds;
    }

    public void setTechnicalSheetFileIds(List<ObjectId> technicalSheetFileIds) {
        this.technicalSheetFileIds = technicalSheetFileIds;
    }

    public List<ObjectId> getDisposalDocumentFileIds() {
        return disposalDocumentFileIds;
    }

    public void setDisposalDocumentFileIds(List<ObjectId> disposalDocumentFileIds) {
        this.disposalDocumentFileIds = disposalDocumentFileIds;
    }

    public List<ObjectId> getIdentityDocumentAgentFileIds() {
        return identityDocumentAgentFileIds;
    }

    public void setIdentityDocumentAgentFileIds(List<ObjectId> identityDocumentAgentFileIds) {
        this.identityDocumentAgentFileIds = identityDocumentAgentFileIds;
    }

    public List<ObjectId> getCashingMandateFileIds() {
        return cashingMandateFileIds;
    }

    public void setCashingMandateFileIds(List<ObjectId> cashingMandateFileIds) {
        this.cashingMandateFileIds = cashingMandateFileIds;
    }

    public List<ObjectId> getPlate1FileIds() {
        return plate1FileIds;
    }

    public void setPlate1FileIds(List<ObjectId> plate1FileIds) {
        this.plate1FileIds = plate1FileIds;
    }

    public List<ObjectId> getThermalPlant1FileIds() {
        return thermalPlant1FileIds;
    }

    public void setThermalPlant1FileIds(List<ObjectId> thermalPlant1FileIds) {
        this.thermalPlant1FileIds = thermalPlant1FileIds;
    }

    public List<ObjectId> getGenerator1FileIds() {
        return generator1FileIds;
    }

    public void setGenerator1FileIds(List<ObjectId> generator1FileIds) {
        this.generator1FileIds = generator1FileIds;
    }

    public List<ObjectId> getValves1FileIds() {
        return valves1FileIds;
    }

    public void setValves1FileIds(List<ObjectId> valves1FileIds) {
        this.valves1FileIds = valves1FileIds;
    }

    public List<ObjectId> getPlate2FileIds() {
        return plate2FileIds;
    }

    public void setPlate2FileIds(List<ObjectId> plate2FileIds) {
        this.plate2FileIds = plate2FileIds;
    }

    public List<ObjectId> getThermal2PlantFileIds() {
        return thermal2PlantFileIds;
    }

    public void setThermal2PlantFileIds(List<ObjectId> thermal2PlantFileIds) {
        this.thermal2PlantFileIds = thermal2PlantFileIds;
    }

    public List<ObjectId> getGenerator2FileIds() {
        return generator2FileIds;
    }

    public void setGenerator2FileIds(List<ObjectId> generator2FileIds) {
        this.generator2FileIds = generator2FileIds;
    }

    public List<ObjectId> getValves2FileIds() {
        return valves2FileIds;
    }

    public void setValves2FileIds(List<ObjectId> valves2FileIds) {
        this.valves2FileIds = valves2FileIds;
    }

    public List<ObjectId> getGlobalStorage2FileIds() {
        return globalStorage2FileIds;
    }

    public void setGlobalStorage2FileIds(List<ObjectId> globalStorage2FileIds) {
        this.globalStorage2FileIds = globalStorage2FileIds;
    }

    public List<ObjectId> getDetailPanel3FileIds() {
        return detailPanel3FileIds;
    }

    public void setDetailPanel3FileIds(List<ObjectId> detailPanel3FileIds) {
        this.detailPanel3FileIds = detailPanel3FileIds;
    }

    public List<ObjectId> getDetailPlate3FileIds() {
        return detailPlate3FileIds;
    }

    public void setDetailPlate3FileIds(List<ObjectId> detailPlate3FileIds) {
        this.detailPlate3FileIds = detailPlate3FileIds;
    }

    public List<ObjectId> getDetailBoiler3FileIds() {
        return detailBoiler3FileIds;
    }

    public void setDetailBoiler3FileIds(List<ObjectId> detailBoiler3FileIds) {
        this.detailBoiler3FileIds = detailBoiler3FileIds;
    }

    public List<ObjectId> getGlobalInstalling3FieldFileIds() {
        return globalInstalling3FieldFileIds;
    }

    public void setGlobalInstalling3FieldFileIds(List<ObjectId> globalInstalling3FieldFileIds) {
        this.globalInstalling3FieldFileIds = globalInstalling3FieldFileIds;
    }

    public List<ObjectId> getGlobalField3FileIds() {
        return globalField3FileIds;
    }

    public void setGlobalField3FileIds(List<ObjectId> globalField3FileIds) {
        this.globalField3FileIds = globalField3FileIds;
    }

    public List<ObjectId> getValves3FileIds() {
        return valves3FileIds;
    }

    public void setValves3FileIds(List<ObjectId> valves3FileIds) {
        this.valves3FileIds = valves3FileIds;
    }

    public List<ObjectId> getDetailGenerator4FileIds() {
        return detailGenerator4FileIds;
    }

    public void setDetailGenerator4FileIds(List<ObjectId> detailGenerator4FileIds) {
        this.detailGenerator4FileIds = detailGenerator4FileIds;
    }

    public List<ObjectId> getGlobalGenerator4FileIds() {
        return globalGenerator4FileIds;
    }

    public void setGlobalGenerator4FileIds(List<ObjectId> globalGenerator4FileIds) {
        this.globalGenerator4FileIds = globalGenerator4FileIds;
    }

    public List<ObjectId> getPlate4FileIds() {
        return plate4FileIds;
    }

    public void setPlate4FileIds(List<ObjectId> plate4FileIds) {
        this.plate4FileIds = plate4FileIds;
    }

    public List<ObjectId> getPlate5FileIds() {
        return plate5FileIds;
    }

    public void setPlate5FileIds(List<ObjectId> plate5FileIds) {
        this.plate5FileIds = plate5FileIds;
    }

    public List<ObjectId> getGenerator5FileIds() {
        return generator5FileIds;
    }

    public void setGenerator5FileIds(List<ObjectId> generator5FileIds) {
        this.generator5FileIds = generator5FileIds;
    }

    public List<ObjectId> getThermalPlant5FileIds() {
        return thermalPlant5FileIds;
    }

    public void setThermalPlant5FileIds(List<ObjectId> thermalPlant5FileIds) {
        this.thermalPlant5FileIds = thermalPlant5FileIds;
    }

    public List<ObjectId> getValves5FileIds() {
        return valves5FileIds;
    }

    public void setValves5FileIds(List<ObjectId> valves5FileIds) {
        this.valves5FileIds = valves5FileIds;
    }

    public List<ObjectId> getFinalConventionFileIds() {
        return finalConventionFileIds;
    }

    public void setFinalConventionFileIds(List<ObjectId> finalConventionFileIds) {
        this.finalConventionFileIds = finalConventionFileIds;
    }

    public List<ObjectId> getFinalConventionToSignedIds() {
        return finalConventionToSignedIds;
    }

    public void setFinalConventionToSignedIds(List<ObjectId> finalConventionToSignedIds) {
        this.finalConventionToSignedIds = finalConventionToSignedIds;
    }

    public List<ObjectId> getInternalFileIds() {
        return internalFileIds;
    }

    public void setInternalFileIds(List<ObjectId> internalFileIds) {
        this.internalFileIds = internalFileIds;
    }

}
