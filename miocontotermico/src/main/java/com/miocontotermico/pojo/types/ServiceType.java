package com.miocontotermico.pojo.types;

/**
 *
 * <AUTHOR>
 */
public enum ServiceType {
    enea_bonus_casa                       ("Bonus Casa", 45),
    enea_ecobonus_casa                    ("Ecobonus Casa", 75),
    conto_termico_pompa_di_calore         ("Pompa di Calore", 80),
    conto_termico_solare_termico          ("Solare Termico", 80),
    conto_termico_solare_stufa            ("Stufa, Inserto, Termostufa", 90),
    conto_termico_caldaia_biomassa        ("Caldaia biomassa", 120),
    conto_termico_sistema_ibrido          ("Sistema ibrido in pdc", 80),
    conto_termico_scaldacqua_pdc          ("Scaldacqua in pdc", 80),
    sconto_in_fattura_bonus_casa          ("Bonus Casa", 125),
    sconto_in_fattura_ecobonus_casa       ("Ecobonus Casa", 155)
    ;
    
    private final Integer credit;
    private final String description;

    private ServiceType(String description, Integer credit) {
        this.description = description;
        this.credit = credit;
    }

    public String getDescription() {
        return description;
    }    
    
    public Integer getCredit() {
        return credit;
    }
}
