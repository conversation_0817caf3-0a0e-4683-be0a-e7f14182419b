package com.miocontotermico.pojo;

import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class Evaluation extends Pojo {
    
    private String protocol;                // protocollo procedura
    private String status;                  // draft (max un draft per utente), opened, processing, approved, annulled
    private Date lastStatusUpdate;          // ultimo aggiornamento di stato
    
    private String service;
    
    // management
    private Boolean cancelled;
    private Date date;                      // data procedura
    private ObjectId userId;                // inserito da
    private ObjectId assignedUserId;        // inserito da
    private String profileType;             // admin, privato, azienda

    // basic info
    private String lastname;
    private String name;
    private String fullname;
    private String city;
    private String address;
    private String provinceCode;
    private String postalCode;
    private String countryCode;
    private String tin;
    private String vatNumber;
    private String brand;
    private String model;
    private String recessedInstaller;


    // note
    private String note;
    
    // contact
    private String email;
    
    // privacy
    private Boolean privacy;

    // terms and conditions
    private Boolean terms;

    // contact
    private String ownership;               // Proprietario o comproprietario  / Detentore\Utilizzatore

    /*
        - foto del locale con ripresa ad ampio raggio (campo obbligatorio)
        - foto da distante del generatore acceso (campo obbligatorio)
        - foto da vicino del generatore acceso (campo obbligatorio)
        - foto di visione di tutta la canna fumaria (campo obbligatorio)
        - foto dei collegamenti della canna fumaria nello schienale del generatore (campo obbligatorio)
        - foto dei collegamenti della canna fumaria al muro (campo obbligatorio)
        - foto dei collegamenti idraulici (campo non obbligatorio)
        - foto della targa del generatore (campo non obbligatorio)
    */
    private List<ObjectId> localFileIds;
    private List<ObjectId> distanceGeneratorFileIds;
    private List<ObjectId> nearGeneratorFileIds;
    private List<ObjectId> allFlueIds;
    private List<ObjectId> retroFlueFileIds;
    private List<ObjectId> wallFlueFileIds;
    private List<ObjectId> connectionsFlueFileIds;
    private List<ObjectId> plateGeneratorFileIds;

    public String getProtocol() {
        return protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getLastStatusUpdate() {
        return lastStatusUpdate;
    }

    public void setLastStatusUpdate(Date lastStatusUpdate) {
        this.lastStatusUpdate = lastStatusUpdate;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }

    public Boolean getCancelled() {
        return cancelled;
    }

    public void setCancelled(Boolean cancelled) {
        this.cancelled = cancelled;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public ObjectId getUserId() {
        return userId;
    }

    public void setUserId(ObjectId userId) {
        this.userId = userId;
    }

    public ObjectId getAssignedUserId() {
        return assignedUserId;
    }

    public void setAssignedUserId(ObjectId assignedUserId) {
        this.assignedUserId = assignedUserId;
    }

    public String getProfileType() {
        return profileType;
    }

    public void setProfileType(String profileType) {
        this.profileType = profileType;
    }

    public String getLastname() {
        return lastname;
    }

    public void setLastname(String lastname) {
        this.lastname = lastname;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFullname() {
        return fullname;
    }

    public void setFullname(String fullname) {
        this.fullname = fullname;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getTin() {
        return tin;
    }

    public void setTin(String tin) {
        this.tin = tin;
    }

    public String getVatNumber() {
        return vatNumber;
    }

    public void setVatNumber(String vatNumber) {
        this.vatNumber = vatNumber;
    }

    public String getBrand() {
        return brand;
    }

    public void setBrand(String brand) {
        this.brand = brand;
    }

    public String getModel() {
        return model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public String getRecessedInstaller() {
        return recessedInstaller;
    }

    public void setRecessedInstaller(String recessedInstaller) {
        this.recessedInstaller = recessedInstaller;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }
    
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Boolean getPrivacy() {
        return privacy;
    }

    public void setPrivacy(Boolean privacy) {
        this.privacy = privacy;
    }

    public Boolean getTerms() {
        return terms;
    }

    public void setTerms(Boolean terms) {
        this.terms = terms;
    }

    public String getOwnership() {
        return ownership;
    }

    public void setOwnership(String ownership) {
        this.ownership = ownership;
    }

    public List<ObjectId> getLocalFileIds() {
        return localFileIds;
    }

    public void setLocalFileIds(List<ObjectId> localFileIds) {
        this.localFileIds = localFileIds;
    }

    public List<ObjectId> getDistanceGeneratorFileIds() {
        return distanceGeneratorFileIds;
    }

    public void setDistanceGeneratorFileIds(List<ObjectId> distanceGeneratorFileIds) {
        this.distanceGeneratorFileIds = distanceGeneratorFileIds;
    }

    public List<ObjectId> getNearGeneratorFileIds() {
        return nearGeneratorFileIds;
    }

    public void setNearGeneratorFileIds(List<ObjectId> nearGeneratorFileIds) {
        this.nearGeneratorFileIds = nearGeneratorFileIds;
    }

    public List<ObjectId> getAllFlueIds() {
        return allFlueIds;
    }

    public void setAllFlueIds(List<ObjectId> allFlueIds) {
        this.allFlueIds = allFlueIds;
    }

    public List<ObjectId> getRetroFlueFileIds() {
        return retroFlueFileIds;
    }

    public void setRetroFlueFileIds(List<ObjectId> retroFlueFileIds) {
        this.retroFlueFileIds = retroFlueFileIds;
    }

    public List<ObjectId> getWallFlueFileIds() {
        return wallFlueFileIds;
    }

    public void setWallFlueFileIds(List<ObjectId> wallFlueFileIds) {
        this.wallFlueFileIds = wallFlueFileIds;
    }

    public List<ObjectId> getConnectionsFlueFileIds() {
        return connectionsFlueFileIds;
    }

    public void setConnectionsFlueFileIds(List<ObjectId> connectionsFlueFileIds) {
        this.connectionsFlueFileIds = connectionsFlueFileIds;
    }

    public List<ObjectId> getPlateGeneratorFileIds() {
        return plateGeneratorFileIds;
    }

    public void setPlateGeneratorFileIds(List<ObjectId> plateGeneratorFileIds) {
        this.plateGeneratorFileIds = plateGeneratorFileIds;
    }

}
