package com.miocontotermico.pojo;

/**
 *
 * <AUTHOR>
 */
public class Firm extends Pojo {

    // contacts
    private String siteEmail;                           // customer care su sito
    private String shopEmail;                           // customer care su shop

    // notifications
    private String shopNotification;                    // notifiche dallo shop all'ufficio ordini: never, always
    private String shopNotificationEmail;               // email dell'ufficio ordini (e-commerce)

    private String b2bNotification;                     // notifiche dal b2b all'ufficio ordini: never, always
    private String b2bNotificationEmail;                // email dell'ufficio ordini (b2b)

    private String customerNotification;                // notifiche dal b.e. all'acquirente: never, always
    private String vendorNotification;                  // notifiche dal b.e. al venditore: never, always

    // api
    private String apikey;                              // apikey
    
    public String getSiteEmail() {
        return siteEmail;
    }

    public void setSiteEmail(String siteEmail) {
        this.siteEmail = siteEmail;
    }

    public String getShopEmail() {
        return shopEmail;
    }

    public void setShopEmail(String shopEmail) {
        this.shopEmail = shopEmail;
    }

    public String getShopNotification() {
        return shopNotification;
    }

    public void setShopNotification(String shopNotification) {
        this.shopNotification = shopNotification;
    }

    public String getShopNotificationEmail() {
        return shopNotificationEmail;
    }

    public void setShopNotificationEmail(String shopNotificationEmail) {
        this.shopNotificationEmail = shopNotificationEmail;
    }

    public String getB2bNotification() {
        return b2bNotification;
    }

    public void setB2bNotification(String b2bNotification) {
        this.b2bNotification = b2bNotification;
    }

    public String getB2bNotificationEmail() {
        return b2bNotificationEmail;
    }

    public void setB2bNotificationEmail(String b2bNotificationEmail) {
        this.b2bNotificationEmail = b2bNotificationEmail;
    }

    public String getCustomerNotification() {
        return customerNotification;
    }

    public void setCustomerNotification(String customerNotification) {
        this.customerNotification = customerNotification;
    }

    public String getVendorNotification() {
        return vendorNotification;
    }

    public void setVendorNotification(String vendorNotification) {
        this.vendorNotification = vendorNotification;
    }

    public String getApikey() {
        return apikey;
    }

    public void setApikey(String apikey) {
        this.apikey = apikey;
    }

}
