package com.miocontotermico.pojo;

import java.util.List;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class Payment extends Pojo {

    // identification
    private String code;                                        // codice
    
    // description
    private String name;
    private String nameEnglish;
    private String description;                                 // descrizione
    private String descriptionEnglish;

    // sorting
    private String sorting;
    
    // categorization
    private String paymentType;                                 // manual, nexi, nexi_triveneto, paypal
    
    // management
    private Boolean shoppable;                                  // visibile nello shop
    private Boolean shopDefault;                                // default nello shop
    private Boolean vendorShoppable;                            // visibile portale agenti
    private Boolean vendorDefault;                              // default nel portale agenti
    private String channel;                                     // b2c, b2b, vendor
    
    private Boolean cancelled;
    
    // costs
    private String supplementType;                              // value, percentage
    private Double supplementValue;                             // maggiorazione a valore
    private Double supplementPercentage;                        // maggiorazione %
    
    private Double availableFromValue;                          // valore minimo dell'ordine
    
    // images
    private List<ObjectId> imageIds;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNameEnglish() {
        return nameEnglish;
    }

    public void setNameEnglish(String nameEnglish) {
        this.nameEnglish = nameEnglish;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDescriptionEnglish() {
        return descriptionEnglish;
    }

    public void setDescriptionEnglish(String descriptionEnglish) {
        this.descriptionEnglish = descriptionEnglish;
    }

    public String getSorting() {
        return sorting;
    }

    public void setSorting(String sorting) {
        this.sorting = sorting;
    }

    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public Boolean getShoppable() {
        return shoppable;
    }

    public void setShoppable(Boolean shoppable) {
        this.shoppable = shoppable;
    }

    public Boolean getShopDefault() {
        return shopDefault;
    }

    public void setShopDefault(Boolean shopDefault) {
        this.shopDefault = shopDefault;
    }

    public Boolean getVendorShoppable() {
        return vendorShoppable;
    }

    public void setVendorShoppable(Boolean vendorShoppable) {
        this.vendorShoppable = vendorShoppable;
    }

    public Boolean getVendorDefault() {
        return vendorDefault;
    }

    public void setVendorDefault(Boolean vendorDefault) {
        this.vendorDefault = vendorDefault;
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public Boolean getCancelled() {
        return cancelled;
    }

    public void setCancelled(Boolean cancelled) {
        this.cancelled = cancelled;
    }

    public String getSupplementType() {
        return supplementType;
    }

    public void setSupplementType(String supplementType) {
        this.supplementType = supplementType;
    }

    public Double getSupplementValue() {
        return supplementValue;
    }

    public void setSupplementValue(Double supplementValue) {
        this.supplementValue = supplementValue;
    }

    public Double getSupplementPercentage() {
        return supplementPercentage;
    }

    public void setSupplementPercentage(Double supplementPercentage) {
        this.supplementPercentage = supplementPercentage;
    }

    public Double getAvailableFromValue() {
        return availableFromValue;
    }

    public void setAvailableFromValue(Double availableFromValue) {
        this.availableFromValue = availableFromValue;
    }

    public List<ObjectId> getImageIds() {
        return imageIds;
    }

    public void setImageIds(List<ObjectId> imageIds) {
        this.imageIds = imageIds;
    }
    
}
