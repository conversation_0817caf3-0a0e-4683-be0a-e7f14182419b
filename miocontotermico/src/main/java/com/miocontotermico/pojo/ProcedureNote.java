package com.miocontotermico.pojo;

import org.bson.types.ObjectId;

/**
 * POJO for procedure notes
 * 
 * <AUTHOR> Augster
 */
public class ProcedureNote extends Pojo {
    
    private ObjectId procedureId;
    private String type;        // semplice, attenzione, problema
    private String content;
    private ObjectId userId;    // user who created the note
    
    public ObjectId getProcedureId() {
        return procedureId;
    }
    
    public void setProcedureId(ObjectId procedureId) {
        this.procedureId = procedureId;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public ObjectId getUserId() {
        return userId;
    }
    
    public void setUserId(ObjectId userId) {
        this.userId = userId;
    }
}
