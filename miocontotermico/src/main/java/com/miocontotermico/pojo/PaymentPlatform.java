package com.miocontotermico.pojo;

/**
 *
 * <AUTHOR>
 */
public class PaymentPlatform extends Pojo {
    
    private String paymentType;                                 // manual, nexi, nexi_triveneto, paypal
    private String environmentType;                             // sandbox, live
    private String alias;                                       // context
    private String secretKey;                                   // the secret key!!!
    private String endpoint;                                    // endpoint servizio di pagamento

    private Boolean cancelled;
    
    public String getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(String paymentType) {
        this.paymentType = paymentType;
    }

    public String getEnvironmentType() {
        return environmentType;
    }

    public void setEnvironmentType(String environmentType) {
        this.environmentType = environmentType;
    }

    public String getAlias() {
        return alias;
    }

    public void setAlias(String alias) {
        this.alias = alias;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public Boolean getCancelled() {
        return cancelled;
    }

    public void setCancelled(Boolean cancelled) {
        this.cancelled = cancelled;
    }

    public String getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }

}
