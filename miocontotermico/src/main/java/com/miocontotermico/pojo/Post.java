package com.miocontotermico.pojo;

import java.util.List;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class Post extends Pojo {

    private String title;
    private String description;

    // image
    private List<ObjectId> imageIds;        // la prima immagine è la cover
    
    private String identifier;
    
    private Boolean cancelled;
    
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public List<ObjectId> getImageIds() {
        return imageIds;
    }

    public void setImageIds(List<ObjectId> imageIds) {
        this.imageIds = imageIds;
    }

    public String getIdentifier() {
        return identifier;
    }

    public void setIdentifier(String identifier) {
        this.identifier = identifier;
    }

    public Boolean getCancelled() {
        return cancelled;
    }

    public void setCancelled(Boolean cancelled) {
        this.cancelled = cancelled;
    }

}
