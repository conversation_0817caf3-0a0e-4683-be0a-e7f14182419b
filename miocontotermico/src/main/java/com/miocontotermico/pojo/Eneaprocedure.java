package com.miocontotermico.pojo;

import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class Eneaprocedure extends Pojo {
    
    private String protocol;                // protocollo procedura
    private String status;                  // draft (max un draft per utente), opened, processing, approved, annulled
    private Date lastStatusUpdate;          // ultimo aggiornamento di stato
    
    private String service;
    
    // management
    private Boolean cancelled;
    private Date date;                      // data procedura
    private ObjectId userId;                // inserito da
    private ObjectId assignedUserId;        // inserito da
    private String profileType;             // admin, privato, azienda

    // basic info
    private String lastname;
    private String name;
    private String fullname;
    private String city;
    private String address;
    private String provinceCode;
    private String postalCode;
    private String countryCode;
    private String tin;
    private String vatNumber;


    // note
    private String note;
    
    // contact
    private String email;
    
    // privacy
    private Boolean privacy;

    // terms and conditions
    private Boolean terms;
    
    // priority
    private Boolean priority;


    // contact
    private String ownership;               // Proprietario o comproprietario  / Detentore\Utilizzatore

    /*
        - carta d'identità
        - fatture
        - bonifici
        - modulo raccolta dati
        - scheda tecnica prodotti installati (non obbligatorio)
    */
    private List<ObjectId> identityDocumentFileIds;
    private List<ObjectId> invoiceFileIds;
    private List<ObjectId> bankTransferFileIds;
    private List<ObjectId> collectionFormFileIds;
    private List<ObjectId> technicalDataFileIds;
    private List<ObjectId> adminFileIds;


    public String getProtocol() {
        return protocol;
    }

    public void setProtocol(String protocol) {
        this.protocol = protocol;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getLastStatusUpdate() {
        return lastStatusUpdate;
    }

    public void setLastStatusUpdate(Date lastStatusUpdate) {
        this.lastStatusUpdate = lastStatusUpdate;
    }

    public String getService() {
        return service;
    }

    public void setService(String service) {
        this.service = service;
    }
    
    public Boolean getCancelled() {
        return cancelled;
    }

    public void setCancelled(Boolean cancelled) {
        this.cancelled = cancelled;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public ObjectId getUserId() {
        return userId;
    }

    public void setUserId(ObjectId userId) {
        this.userId = userId;
    }

    public ObjectId getAssignedUserId() {
        return assignedUserId;
    }

    public void setAssignedUserId(ObjectId assignedUserId) {
        this.assignedUserId = assignedUserId;
    }

    public String getProfileType() {
        return profileType;
    }

    public void setProfileType(String profileType) {
        this.profileType = profileType;
    }

    public String getLastname() {
        return lastname;
    }

    public void setLastname(String lastname) {
        this.lastname = lastname;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFullname() {
        return fullname;
    }

    public void setFullname(String fullname) {
        this.fullname = fullname;
    }

    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getPostalCode() {
        return postalCode;
    }

    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getTin() {
        return tin;
    }

    public void setTin(String tin) {
        this.tin = tin;
    }

    public String getVatNumber() {
        return vatNumber;
    }

    public void setVatNumber(String vatNumber) {
        this.vatNumber = vatNumber;
    }

    public String getNote() {
        return note;
    }

    public void setNote(String note) {
        this.note = note;
    }
    
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Boolean getPrivacy() {
        return privacy;
    }

    public void setPrivacy(Boolean privacy) {
        this.privacy = privacy;
    }

    public Boolean getTerms() {
        return terms;
    }

    public void setTerms(Boolean terms) {
        this.terms = terms;
    }

    public Boolean getPriority() {
        return priority;
    }

    public void setPriority(Boolean priority) {
        this.priority = priority;
    }
    
    public String getOwnership() {
        return ownership;
    }

    public void setOwnership(String ownership) {
        this.ownership = ownership;
    }

    public List<ObjectId> getIdentityDocumentFileIds() {
        return identityDocumentFileIds;
    }

    public void setIdentityDocumentFileIds(List<ObjectId> identityDocumentFileIds) {
        this.identityDocumentFileIds = identityDocumentFileIds;
    }

    public List<ObjectId> getInvoiceFileIds() {
        return invoiceFileIds;
    }

    public void setInvoiceFileIds(List<ObjectId> invoiceFileIds) {
        this.invoiceFileIds = invoiceFileIds;
    }

    public List<ObjectId> getBankTransferFileIds() {
        return bankTransferFileIds;
    }

    public void setBankTransferFileIds(List<ObjectId> bankTransferFileIds) {
        this.bankTransferFileIds = bankTransferFileIds;
    }

    public List<ObjectId> getCollectionFormFileIds() {
        return collectionFormFileIds;
    }

    public void setCollectionFormFileIds(List<ObjectId> collectionFormFileIds) {
        this.collectionFormFileIds = collectionFormFileIds;
    }

    public List<ObjectId> getTechnicalDataFileIds() {
        return technicalDataFileIds;
    }

    public void setTechnicalDataFileIds(List<ObjectId> technicalDataFileIds) {
        this.technicalDataFileIds = technicalDataFileIds;
    }

    public List<ObjectId> getAdminFileIds() {
        return adminFileIds;
    }

    public void setAdminFileIds(List<ObjectId> adminFileIds) {
        this.adminFileIds = adminFileIds;
    }

}
