package com.miocontotermico.property;

import java.util.Iterator;
import java.util.List;
import org.apache.commons.lang3.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class PropertyUtils {

    public static List<StatusEntry> cleanStatusList(List<StatusEntry> list) {
        if ((list != null) &&
                (!list.isEmpty())) {
            Iterator<StatusEntry> item = list.listIterator();
            while(item.hasNext()){
                if(StringUtils.isBlank(item.next().getStatus())){
                    item.remove();
                }
            }            
        }
        return list;
    }

    public static List<ProvinciaEntry> cleanProvinciaList(List<ProvinciaEntry> list) {
        if ((list != null) &&
                (!list.isEmpty())) {
            Iterator<ProvinciaEntry> item = list.listIterator();
            while(item.hasNext()){
                if(StringUtils.isBlank(item.next().getProvincia())){
                    item.remove();
                }
            }            
        }
        return list;
    }
    
    public static List<CittaEntry> cleanCittaList(List<CittaEntry> list) {
        if ((list != null) &&
                (!list.isEmpty())) {
            Iterator<CittaEntry> item = list.listIterator();
            while(item.hasNext()){
                if(StringUtils.isBlank(item.next().getCitta())){
                    item.remove();
                }
            }            
        }
        return list;
    }
    
}
