package com.miocontotermico.evaluation;

import com.miocontotermico.commons.NotificationCommons;
import com.miocontotermico.commons.EvaluationCommons;
import com.miocontotermico.core.Manager;
import com.miocontotermico.core.Paths;
import com.miocontotermico.core.Templates;
import com.miocontotermico.dao.CounterDao;
import com.miocontotermico.dao.FileDao;
import com.miocontotermico.dao.EvaluationDao;
import com.miocontotermico.dao.UserDao;
import com.miocontotermico.pojo.Evaluation;
import com.miocontotermico.pojo.User;
import com.miocontotermico.pojo.types.FileType;
import com.miocontotermico.pojo.types.ProfileType;
import com.miocontotermico.pojo.types.StatusType;
import com.miocontotermico.property.PropertyUtils;
import com.miocontotermico.property.StatusEntry;
import com.miocontotermico.support.file.posted.PostedFile;
import com.miocontotermico.util.ParamUtils;
import com.miocontotermico.util.PojoUtils;
import com.miocontotermico.util.RouteUtils;
import com.miocontotermico.util.TimeUtils;
import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class EvaluationController {

    private static final Logger LOGGER = LoggerFactory.getLogger(EvaluationController.class.getName());

    public static TemplateViewRoute evaluations = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user != null) {
            user = UserDao.loadUser(user.getId());
            Manager.putSession(token, "user", user);
        }
        
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN) ;
            return Manager.renderEmpty();
        }
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));
        
        List<StatusEntry> statusList = EvaluationDao.loadEvaluationStatusList();
        attributes.put("statusList", PropertyUtils.cleanStatusList(statusList));
        
        
        // order (optional) filters
        Date startDate = DateUtils.addMonths(new Date(), -3);
        Date endDate = TimeUtils.today();
        
        if (StringUtils.isNotBlank(request.queryParams("startDate"))) {
            Date tmp = TimeUtils.toDate(request.queryParams("startDate"));
            if (tmp != null) {
                startDate = tmp;
            }
        }
        if (StringUtils.isNotBlank(request.queryParams("endDate"))) {
            Date tmp = TimeUtils.toDate(request.queryParams("endDate"));
            if (tmp != null) {
                endDate = tmp;
            }
        }
        
        attributes.put("startDate", startDate);
        attributes.put("endDate", endDate);
        
        String[] selectedStatuses = null;
        if (StringUtils.isNotBlank(request.queryParams("selectedStatuses"))) {
            selectedStatuses = StringUtils.split(request.queryParams("selectedStatuses"), "|");
        }
        attributes.put("selectedStatuses", selectedStatuses);
        
        ObjectId userId = null;
        ObjectId assignedUserId = null;
        if (!StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.admin.toString())) {
            if (StringUtils.equalsIgnoreCase(user.getProfileType(), ProfileType.operatore.toString())) {
                assignedUserId = user.getId();
            } else {
                userId = user.getId();
            }
        }
//        if (user.getProfileType() !=)
        List<Evaluation> evaluationList = EvaluationDao.loadEvaluationListByDateRangeAndStatus(startDate, endDate, selectedStatuses, userId, assignedUserId);
        attributes.put("evaluationList", EvaluationCommons.toEntries(evaluationList));
        
        return Manager.render(Templates.EVALUATIONS, attributes, RouteUtils.pathType(request));
    };

    public static TemplateViewRoute evaluations_add = (Request request, Response response) -> {
        
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user != null) {
            user = UserDao.loadUser(user.getId());
            Manager.putSession(token, "user", user);
        }
        
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN) ;
            return Manager.renderEmpty();
        }

        // draft
        Evaluation draft = EvaluationDao.loadDraftEvaluation(user.getId());
        if (draft == null) {
            draft = EvaluationCommons.initDraft(user);
//        } else {
//            if (!user.getProfileType().equals("privato")) {
//                draft = EvaluationCommons.initExistingDraft(draft);
//            }
        }
        attributes.put("evaluation", draft);
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        // step
        attributes.put("step", NumberUtils.toInt(request.queryParams("step"), 0));
        
        attributes.put("tab", request.queryParams("tab"));
        
        return Manager.render(Templates.EVALUATIONS_ADD, attributes, RouteUtils.pathType(request));
    };
    
    public static Route evaluations_add_info_save = (Request request, Response response) -> {
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        user = UserDao.loadUser(user.getId());
        
        // draft
        Evaluation draft = EvaluationDao.loadDraftEvaluation(user.getId());
        if (draft == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        // merge
        Map<String, List<PostedFile>> files = new HashMap<>();
        Map<String, String> params = PojoUtils.paramsFromRequest(request, null, files);
        PojoUtils.mergeFromParams(params, draft);
        
        // files
        if (!files.isEmpty()) {
            
            String fieldname = "localFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getLocalFileIds()== null) {
                    draft.setLocalFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getLocalFileIds().addAll(ids);
                }
            }
            
            fieldname = "distanceGeneratorFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getDistanceGeneratorFileIds()== null) {
                    draft.setDistanceGeneratorFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getDistanceGeneratorFileIds().addAll(ids);
                }
            }
            
            fieldname = "nearGeneratorFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getNearGeneratorFileIds()== null) {
                    draft.setNearGeneratorFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getNearGeneratorFileIds().addAll(ids);
                }
            }
            
            fieldname = "allFlueIds";
            if (files.containsKey(fieldname)) {
                if (draft.getAllFlueIds()== null) {
                    draft.setAllFlueIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getAllFlueIds().addAll(ids);
                }
            }
            
            fieldname = "retroFlueFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getRetroFlueFileIds()== null) {
                    draft.setRetroFlueFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getRetroFlueFileIds().addAll(ids);
                }
            }
            
            fieldname = "wallFlueFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getWallFlueFileIds()== null) {
                    draft.setWallFlueFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getWallFlueFileIds().addAll(ids);
                }
            }
            
            fieldname = "connectionsFlueFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getConnectionsFlueFileIds()== null) {
                    draft.setConnectionsFlueFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getConnectionsFlueFileIds().addAll(ids);
                }
            }
            
            fieldname = "plateGeneratorFileIds";
            if (files.containsKey(fieldname)) {
                if (draft.getPlateGeneratorFileIds()== null) {
                    draft.setPlateGeneratorFileIds(new ArrayList<>());
                }
                List<ObjectId> ids = insertFiles(files.get(fieldname));
                if ((ids != null) && !ids.isEmpty()) {
                    draft.getPlateGeneratorFileIds().addAll(ids);
                }
            }
        
        }
        
        // save
        try {
            EvaluationDao.updateEvaluation(draft);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        
        return "ok";
    };
    
    public static Route evaluations_add_save = (Request request, Response response) -> {
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        user = UserDao.loadUser(user.getId());
        
        // draft
        Evaluation draft = EvaluationDao.loadDraftEvaluation(user.getId());
        if (draft == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        // merge
        Map<String, String> params = PojoUtils.paramsFromRequest(request);
        PojoUtils.mergeFromParams(params, draft);
        
        // data
        draft.setStatus(StatusType.opened.toString());
        draft.setLastStatusUpdate(TimeUtils.now());
        draft.setDate(TimeUtils.now());

        // protocol
        String protocolKey = "evaluation-protocol";
        int next = CounterDao.next(protocolKey);
        draft.setProtocol("" + next);
        
        // save
        try {
            EvaluationDao.updateEvaluation(draft);

            // INVIARE MAIL A SOGENIT
            if (NotificationCommons.notifyEvaluationReceive(request, draft)) {
                // ...
            }
            if (draft.getUserId() != null) {
                User evaluationUser = UserDao.loadUser(draft.getUserId());
                if (NotificationCommons.notifyEvaluationReceiveToUser(request, draft, evaluationUser)) {
                    // ...
                }
            }
            
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        
        return "ok";
    };
    
    public static Route evaluations_add_fileid_remove = (Request request, Response response) -> {
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        user = UserDao.loadUser(user.getId());
        
        // draft
        Evaluation draft = EvaluationDao.loadDraftEvaluation(user.getId());
        if (draft == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        // list
        String listName = request.queryParams("listName");
        if (StringUtils.isEmpty(listName)) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        // id
        ObjectId fileId = ParamUtils.toObjectId(request.queryParams("fileId"));
        if (fileId == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        List<ObjectId> ids = null;

        switch (listName) {
            case "localFileIds":
                ids = draft.getLocalFileIds();
                break;
            case "distanceGeneratorFileIds":
                ids = draft.getDistanceGeneratorFileIds();
                break;
            case "nearGeneratorFileIds":
                ids = draft.getNearGeneratorFileIds();
                break;
            case "allFlueIds":
                ids = draft.getAllFlueIds();
                break;
            case "retroFlueFileIds":
                ids = draft.getRetroFlueFileIds();
                break;
            case "wallFlueFileIds":
                ids = draft.getWallFlueFileIds();
                break;
            case "connectionsFlueFileIds":
                ids = draft.getConnectionsFlueFileIds();
                break;
            case "plateGeneratorFileIds":
                ids = draft.getPlateGeneratorFileIds();
                break;
            default:
                break;
        }
        if (ids == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        // remove id
        Iterator<ObjectId> iter = ids.iterator();
        while (iter.hasNext()) {
            if (iter.next().equals(fileId)) {
                iter.remove();
                break;
            }
        }
        
        // manca rimozione lista
        // ??????
        
        // save
        try {
            EvaluationDao.updateEvaluation(draft);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        
        return "ok";
    };
    
    public static TemplateViewRoute evaluation_edit = (Request request, Response response) -> {
        
        ObjectId evaluationId = ParamUtils.toObjectId(request.queryParams("evaluationId"));
        
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN) ;
            return Manager.renderEmpty();
        }

        Evaluation evaluation = null;
        if (evaluationId != null) {
            evaluation = EvaluationDao.loadEvaluation(evaluationId);
        }
        attributes.put("evaluation", evaluation);
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));
        
        List<User> userList = UserDao.loadSystemUserList();
        attributes.put("userList", userList);

        return Manager.render(Templates.EVALUATION_EDIT, attributes, RouteUtils.pathType(request));
    };
    
    public static Route evaluation_edit_save = (Request request, Response response) -> {
        // params
        ObjectId evaluationId = ParamUtils.toObjectId(request.queryParams("evaluationId"));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }
        
        // merge
        Map<String, List<PostedFile>> files = new HashMap<>();
        Map<String, String> params = PojoUtils.paramsFromRequest(request, null, files);
        
        if (evaluationId != null) {
            // params
            Evaluation evaluation = EvaluationDao.loadEvaluation(evaluationId);
            if (evaluation != null) {
                Boolean sendDocumentMail = false;
                Boolean sendFinalMail = false;
                Boolean sendAssignedMail = false;
                Boolean sendUpdateStatusMail = false;
                
                if (StringUtils.isNotBlank(params.get("status"))) {
                    if (!StringUtils.equalsIgnoreCase(evaluation.getStatus(), params.get("status"))) {
                        sendUpdateStatusMail = true;
                    }
                    evaluation.setStatus(params.get("status"));
                }
                if (ParamUtils.toObjectId(params.get("assignedUserId")) != null) {
                    if (evaluation.getAssignedUserId() != null) {
                        if (!evaluation.getAssignedUserId().equals(ParamUtils.toObjectId(params.get("assignedUserId")))) {
                            sendAssignedMail = true;
                        }
                    } else {
                        sendAssignedMail = true;
                    }
                    evaluation.setAssignedUserId(ParamUtils.toObjectId(params.get("assignedUserId")));
                }
                
                evaluation.setNote(params.get("note"));
                
                // save
                try {
                    EvaluationDao.updateEvaluation(evaluation);
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                
                // notify
                if (sendFinalMail) {
                    if (NotificationCommons.notifyEvaluationFinalUpload(request, evaluation)) {
                        // ...
                    }
                }
                if (sendDocumentMail) {
                    if (NotificationCommons.notifyEvaluationUpload(request, evaluation)) {
                        // ...
                    }
                }
                if (sendAssignedMail) {
                    if (evaluation.getUserId() != null) {
                        User userAssigned = UserDao.loadUser(evaluation.getAssignedUserId());
                        if (NotificationCommons.notifyEvaluationAssigned(request, evaluation, userAssigned)) {
                            // ...
                        }
                    }
                }
                if (sendUpdateStatusMail) {
                    if (evaluation.getUserId() != null) {
                        User evaluationUser = UserDao.loadUser(evaluation.getUserId());
                        if (NotificationCommons.notifyEvaluation(request, evaluation, evaluationUser)) {
                            // ...
                        }
                    }
                }
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        // files
        
        

        return "ok";        

    };  
    
    public static Route evaluation_remove = (Request request, Response response) -> {
        
        ObjectId evaluationId = ParamUtils.toObjectId(request.queryParams("evaluationId"));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        
        if (evaluationId != null) {
            // params
            Evaluation evaluation = EvaluationDao.loadEvaluation(evaluationId);
            if (evaluation != null) {
                EvaluationDao.updateEvaluationCancelled(evaluationId, true);
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
                    
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return "ok";        
    };          
    
    public static Route evaluation_status_update = (Request request, Response response) -> {
        
        ObjectId evaluationId = ParamUtils.toObjectId(request.queryParams("evaluationId"));
        String status = request.queryParams("status");
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
        
        if (evaluationId != null) {
            // params
            Evaluation evaluation = EvaluationDao.loadEvaluation(evaluationId);
            if (evaluation != null) {
                evaluation.setStatus(status);
                EvaluationDao.updateEvaluation(evaluation);
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
            // notify
            if (evaluation.getUserId() != null) {
                User evaluationUser = UserDao.loadUser(evaluation.getUserId());
                if (NotificationCommons.notifyEvaluation(request, evaluation, evaluationUser)) {
                    // ...
                }
            }
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return "ok";        
    };



    ////////////
    // internals

    private static List<ObjectId> insertFiles(List<PostedFile> posteds) {
        if (posteds == null) {
            return null;
        }
        if (posteds.isEmpty()) {
            return null;
        }
        List<ObjectId> ids = new ArrayList<>();
        for (PostedFile posted : posteds) {
            
            ObjectId oid = null;
            try {
                
                // filename
                String filename = FileDao.composeFilename(FileType.attachment, posted.getExtension());
                
                // save file
                File fll = new File(posted.getFilename());
                oid = FileDao.insertFile(filename, 
                        posted.getName(),
                        posted.getContentType(), 
                        FileUtils.readFileToByteArray(fll)
                );
                
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }

            if (oid != null) {
                ids.add(oid);
            }
        }
        return ids;
    }
    
}
