package com.miocontotermico.paperwork;

import com.miocontotermico.core.Manager;
import com.miocontotermico.core.Paths;
import com.miocontotermico.core.Templates;
import com.miocontotermico.dao.EneaprocedureDao;
import com.miocontotermico.dao.FileDao;
import com.miocontotermico.dao.PaperworkDao;
import com.miocontotermico.dao.UserDao;
import com.miocontotermico.pojo.Eneaprocedure;
import com.miocontotermico.pojo.Paperwork;
import com.miocontotermico.pojo.User;
import com.miocontotermico.pojo.types.FileType;
import com.miocontotermico.support.file.posted.PostedFile;
import com.miocontotermico.util.ParamUtils;
import com.miocontotermico.util.PojoUtils;
import com.miocontotermico.util.RouteUtils;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class PaperworkController {
    private static final Logger LOGGER = LoggerFactory.getLogger(PaperworkController.class.getName());    
    
    public static TemplateViewRoute paperworks = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN) ;
            return Manager.renderEmpty();
        }
        
        attributes.put("user", user);
        
        List<Paperwork> paperworkList = PaperworkDao.loadPaperworkList();
        attributes.put("paperworkList", paperworkList);        
        
        return Manager.render(Templates.PAPERWORKS, attributes, RouteUtils.pathType(request));
    };    
    
    public static TemplateViewRoute paperworks_view = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user != null) {
            user = UserDao.loadUser(user.getId());
            Manager.putSession(token, "user", user);
        }
        
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN) ;
            return Manager.renderEmpty();
        }
        
        
        List<Paperwork> paperworkList = PaperworkDao.loadPaperworkListWhereVisible();
        attributes.put("paperworkList", paperworkList);        
        
        return Manager.render(Templates.PAPERWORKS_VIEW, attributes, RouteUtils.pathType(request));
    };    

    public static TemplateViewRoute paperwork_edit = (Request request, Response response) -> {
        
        ObjectId paperworkId = ParamUtils.toObjectId(request.queryParams("paperworkId"));
        
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.HOME) ;
            return Manager.renderEmpty();
        }
        
        
        Paperwork paperwork;
        if (paperworkId != null) {
            paperwork = PaperworkDao.loadPaperwork(paperworkId);
        } else {
            paperwork = new Paperwork();
        }
        attributes.put("paperwork", paperwork);
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        
        return Manager.render(Templates.PAPERWORK_EDIT, attributes, RouteUtils.pathType(request));
    };
    
    public static Route paperwork_edit_save = (Request request, Response response) -> {
        // params
        ObjectId paperworkId = ParamUtils.toObjectId(request.queryParams("paperworkId"));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }
        
        Map<String, List<PostedFile>> files = new HashMap<>();
        Map<String, String> params = PojoUtils.paramsFromRequest(request, null, files);
        
        if (paperworkId != null) {
            Paperwork paperwork = PaperworkDao.loadPaperwork(paperworkId);
            paperwork = PojoUtils.mergeFromParams(params, paperwork);
            
            if (isValidPaperwork(paperwork)) {
                if (!files.isEmpty()) {
                    String fieldname = "fileIds";
                    if (files.containsKey(fieldname)) {
                        if (paperwork.getFileIds()== null) {
                            paperwork.setFileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            paperwork.getFileIds().addAll(ids);
                        }
                    }
                }                
                PaperworkDao.updatePaperwork(paperwork);

                // navigation
                response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.PAPERWORKS));

            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.PAPERWORK_EDIT) + "?paperworkId=" + paperwork.getId());
            }
        } else {
            // params
            Paperwork paperwork = PojoUtils.createFromParams(params, Paperwork.class);
            
            if (isValidPaperwork(paperwork)) {
                if (!files.isEmpty()) {
                    String fieldname = "fileIds";
                    if (files.containsKey(fieldname)) {
                        if (paperwork.getFileIds()== null) {
                            paperwork.setFileIds(new ArrayList<>());
                        }
                        List<ObjectId> ids = insertFiles(files.get(fieldname));
                        if ((ids != null) && !ids.isEmpty()) {
                            paperwork.getFileIds().addAll(ids);
                        }
                    }
                }
                
                paperworkId = PaperworkDao.insertPaperwork(paperwork);

                // navigation
                response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.PAPERWORKS));

            } else {
                response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.PAPERWORK_EDIT));
            }
        }        

        return null;
    };  
    
    public static Route paperwork_remove = (Request request, Response response) -> {
        
        ObjectId paperworkId = ParamUtils.toObjectId(request.queryParams("paperworkId"));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            Spark.halt(HttpStatus.UNAUTHORIZED_401);
            return null;
        }
        
        if (paperworkId != null) {
            // params
            Paperwork paperwork = PaperworkDao.loadPaperwork(paperworkId);
            if (paperwork != null) {
                PaperworkDao.updatePaperworkCancelled(paperworkId, true);
            } else {
                Spark.halt(HttpStatus.BAD_REQUEST_400);
            }
                    
        } else {
            Spark.halt(HttpStatus.BAD_REQUEST_400);
        }

        return "ok";        
    };    

    public static Route paperwork_edit_fileid_remove = (Request request, Response response) -> {
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        // paperworkid
        ObjectId paperworkId = ParamUtils.toObjectId(request.queryParams("paperworkId"));
        if (paperworkId == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        Paperwork paperwork = PaperworkDao.loadPaperwork(paperworkId);
        if (paperwork == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        // id
        ObjectId fileId = ParamUtils.toObjectId(request.queryParams("fileId"));
        if (fileId == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        List<ObjectId> ids = paperwork.getFileIds();

        if (ids == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400);
        }
        
        // remove id
        Iterator<ObjectId> iter = ids.iterator();
        while (iter.hasNext()) {
            if (iter.next().equals(fileId)) {
                iter.remove();
                break;
            }
        }
        
        // save
        try {
            PaperworkDao.updatePaperwork(paperwork);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        
        return "ok";
    };
    
    ////////////
    // internals
    
    private static boolean isValidPaperwork(Paperwork entity) {
        boolean valid = (entity != null) &&
                StringUtils.isNotBlank(entity.getTitle())
                ;
        
        // ?????? @leonardo: inserire gli attuali vincoli
        if (!valid) {
            if (entity != null) {
                LOGGER.warn(
                        "Paperwork validation problem:\n" +
                        "- ref " + entity.getTitle()+ "\n"
                );
            } else {
                LOGGER.warn(
                        "Paperwork validation problem:\n" +
                        "- empty"
                );
            }
        }
        
        return valid;
    }

    ////////////
    // internals

    private static List<ObjectId> insertFiles(List<PostedFile> posteds) {
        if (posteds == null) {
            return null;
        }
        if (posteds.isEmpty()) {
            return null;
        }
        List<ObjectId> ids = new ArrayList<>();
        for (PostedFile posted : posteds) {
            
            ObjectId oid = null;
            try {
                
                // filename
                String filename = FileDao.composeFilename(FileType.paperwork, posted.getExtension());
                
                // save file
                File fll = new File(posted.getFilename());
                oid = FileDao.insertFile(filename, 
                        posted.getName(),
                        posted.getContentType(), 
                        FileUtils.readFileToByteArray(fll)
                );
                
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }

            if (oid != null) {
                ids.add(oid);
            }
        }
        return ids;
    }

}

