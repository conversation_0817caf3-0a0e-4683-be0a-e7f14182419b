package com.miocontotermico.support;

import com.miocontotermico.core.Defaults;
import com.miocontotermico.dao.EneaprocedureDao;
import com.miocontotermico.dao.EvaluationDao;
import com.miocontotermico.dao.FileDao;
import com.miocontotermico.dao.InvoicediscountDao;
import com.miocontotermico.dao.ProcedureDao;
import com.miocontotermico.pojo.Eneaprocedure;
import com.miocontotermico.pojo.Evaluation;
import com.miocontotermico.pojo.Invoicediscount;
import com.miocontotermico.pojo.Procedure;
import com.miocontotermico.support.file.Filex;
import com.miocontotermico.util.EnvironmentUtils;
import com.miocontotermico.util.ParamUtils;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;

/**
 *
 * <AUTHOR>
 */
public class FileController {

    private static final Logger LOGGER = LoggerFactory.getLogger(FileController.class.getName());
    
    public static Route file = (Request request, Response response) -> {
        
        // params
        ObjectId oid = ParamUtils.toObjectId(request.queryParams("oid"));

        if (oid != null) {

            Filex filex = null;
            try {
                filex = FileDao.loadFile(oid);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }

            if (filex != null) {
                response.type(filex.getContentType());
                
                if (EnvironmentUtils.hasNotResourcesHotDeploy()) {
                    // file caching
                    response.header("Cache-Control", "private, max-age=" + Defaults.FILE_RESOURCE_EXPIRATION_TIME);
                    response.header("Expires", new Date(System.currentTimeMillis() + (Defaults.FILE_RESOURCE_EXPIRATION_TIME * 1000)).toString());
                }
                
                String filename = filex.getOriginalFilename();
                filename = sanitizeFilename(filename);
                response.header("Content-Disposition", "inline; filename=\"" + filename + "\"");
                
                response.raw().getOutputStream().write(filex.getBytes());
                response.raw().getOutputStream().close();
            } else {
                LOGGER.warn("empty file oid " + oid);
            }
        } else {
            LOGGER.warn("unexistent file oid " + oid);
        }

        return null;
    };

    public static Route createProcedureZipFile = (Request request, Response response) -> {
        ObjectId procedureId = ParamUtils.toObjectId(request.queryParams("procedureId"));
        ObjectId evaluationId = ParamUtils.toObjectId(request.queryParams("evaluationId"));
        ObjectId eneaprocedureId = ParamUtils.toObjectId(request.queryParams("eneaprocedureId"));
        ObjectId invoicediscountId = ParamUtils.toObjectId(request.queryParams("invoicediscountId"));

        // temporary folder
        Path tempfolder = null;
        try {
            tempfolder = Files.createTempDirectory("miocontotermico");
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        if (tempfolder == null) {
            return null;
        }
        
        // temporary file
        Path path = null;
        try {
            path = Files.createTempFile(tempfolder, "miocontotermico", "." + "zip");
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        if (path == null) {
            return null;
        }
        
        List<ObjectId> ids = new ArrayList<>();

        Procedure procedure = null;
        if (procedureId != null) {
            try {
                procedure = ProcedureDao.loadProcedure(procedureId);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
            if (procedure == null) {
                return null;
            }

            List<ObjectId> docs = procedure.getThermalPortalDelegationFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getIdentityDocumentFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getTinFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getAuthorizationFormFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getIdentityDocumentOwnerFormFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getInvoiceFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getContractFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getDataCollectionFormFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getTechnicalSheetFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getDisposalDocumentFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getIdentityDocumentAgentFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getCashingMandateFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getPlate1FileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getThermalPlant1FileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getGenerator1FileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getValves1FileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getPlate2FileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getThermal2PlantFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getGenerator2FileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getValves2FileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getGlobalStorage2FileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getDetailPanel3FileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getDetailPlate3FileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getDetailBoiler3FileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getGlobalInstalling3FieldFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getGlobalField3FileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getValves3FileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getDetailGenerator4FileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getGlobalGenerator4FileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getPlate4FileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getPlate5FileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getGenerator5FileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getThermalPlant5FileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = procedure.getValves5FileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
        }

        Evaluation evaluation = null;
        if (evaluationId != null) {
            
            try {
                evaluation = EvaluationDao.loadEvaluation(evaluationId);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
            if (evaluation == null) {
                return null;
            }
    
            List<ObjectId> docs = evaluation.getLocalFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = evaluation.getDistanceGeneratorFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = evaluation.getNearGeneratorFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = evaluation.getAllFlueIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = evaluation.getRetroFlueFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = evaluation.getWallFlueFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = evaluation.getConnectionsFlueFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = evaluation.getPlateGeneratorFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
        }    
        
        Eneaprocedure eneaprocedure = null;
        if (eneaprocedureId != null) {
            
            try {
                eneaprocedure = EneaprocedureDao.loadEneaprocedure(eneaprocedureId);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
            if (eneaprocedure == null) {
                return null;
            }
    
            List<ObjectId> docs = eneaprocedure.getIdentityDocumentFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = eneaprocedure.getInvoiceFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = eneaprocedure.getBankTransferFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = eneaprocedure.getCollectionFormFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = eneaprocedure.getTechnicalDataFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = eneaprocedure.getAdminFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
        }    
        
        Invoicediscount invoicediscount = null;
        if (invoicediscountId != null) {
            
            try {
                invoicediscount = InvoicediscountDao.loadInvoicediscount(invoicediscountId);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
            if (invoicediscount == null) {
                return null;
            }
    
            List<ObjectId> docs = invoicediscount.getIdentityDocumentFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = invoicediscount.getInvoiceFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = invoicediscount.getBankTransferFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = invoicediscount.getCollectionFormFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = invoicediscount.getTechnicalDataFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = invoicediscount.getAdminFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = invoicediscount.getRevenueAgencyDelegationFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = invoicediscount.getRevenueAgencyFormFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            docs = invoicediscount.getSelfDeclarationFileIds();
            if ((docs != null) && !docs.isEmpty()) {
                ids.addAll(docs);
            }
            
        }    
        
        if (ids.isEmpty()) {
            return null;
        }

        File tempfile = path.toFile();
        try (FileOutputStream fos = new FileOutputStream(tempfile); ZipOutputStream zipOut = new ZipOutputStream(fos)) {
            int unique = 0;
            for (ObjectId id : ids) {
                
                Filex filex = null;
                try {
                    filex = FileDao.loadFile(id);
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
                if (filex == null) {
                    continue;
                }
                
                String filename = id.toString();
                if (filex.getOriginalFilename() != null) {
                    filename = filex.getOriginalFilename();
                }
                filename = StringUtils.substringBeforeLast(filename, ".") + "-" + unique + "." + StringUtils.substringAfterLast(filename, ".");
                unique++;
                
                try (InputStream fis = new ByteArrayInputStream(filex.getBytes())) {
                    ZipEntry zipEntry = new ZipEntry(filename);
                    zipOut.putNextEntry(zipEntry);
                    
                    byte[] bytes = new byte[1024];
                    int length;
                    while((length = fis.read(bytes)) >= 0) {
                        zipOut.write(bytes, 0, length);
                    }
                }
            }
        }

        byte[] bytes = FileUtils.readFileToByteArray(tempfile);
        
        response.type("application/zip");
        String filename = "";
        if (procedure != null) {
            filename = "documenti-" + procedure.getProtocol() + ".zip";
        }
        if (invoicediscount != null) {
            filename = "documenti-" + invoicediscount.getProtocol() + ".zip";
        }
        filename = sanitizeFilename(filename);
        response.header("Content-Disposition", "inline; filename=\"" + filename + "\"");
        response.raw().getOutputStream().write(bytes);
        response.raw().getOutputStream().close();
        
        return null;
    };
 
    ////////////
    // internals
    
    private static String sanitizeFilename(String name) {
        return StringUtils.replace(StringUtils.replacePattern(name, "[\\\\/:*?\"<>|]", ""), " ", "_");
    }
    
}
