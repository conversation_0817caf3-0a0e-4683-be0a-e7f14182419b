package com.miocontotermico.support.file;

/**
 *
 * <AUTHOR>
 */
public class Filex {

    private String filename;
    private String originalFilename;
    private String contentType;
    private byte[] bytes;

    public Filex() {
    }

    public Filex(String filename, String originalFilename, String contentType, byte[] bytes) {
        this.filename = filename;
        this.originalFilename = originalFilename;
        this.contentType = contentType;
        this.bytes = bytes;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    public String getOriginalFilename() {
        return originalFilename;
    }

    public void setOriginalFilename(String originalFilename) {
        this.originalFilename = originalFilename;
    }
    
    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public byte[] getBytes() {
        return bytes;
    }

    public void setBytes(byte[] bytes) {
        this.bytes = bytes;
    }
    
}
