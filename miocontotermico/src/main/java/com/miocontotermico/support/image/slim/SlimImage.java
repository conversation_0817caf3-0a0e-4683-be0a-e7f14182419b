package com.miocontotermico.support.image.slim;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class SlimImage {

/* slim uploaded image json sample
{
	"server":null,
	"meta":null,
	"input":
		{
			"name":"Screenshot (1).png",
			"type":"image/png",
			"size":151615,
			"width":1280,
			"height":1024
		},
	"output":
		{
			"width":300,
			"height":169,
			"image":"data:image/png;base64,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"
		},
	"actions":
		{
			"crop":
				{
					"x":0,
					"y":152,
					"height":720,
					"width":1280,
					"type":"auto"
				},
			"size":
				{
					"width":300,
					"height":300
				}
		}
}
*/
    
    private String server;
    private Object meta;
    private SlimInput input;
    private SlimOutput output;

    public boolean isEmpty() {
        return (output == null) ||
                StringUtils.isBlank(output.getImage()) ||
                !StringUtils.startsWithIgnoreCase(output.getImage(), "data:image/") ||
                !StringUtils.containsIgnoreCase(output.getImage(), ";base64,") ||
                ((StringUtils.indexOf(output.getImage(), ";base64,") + 8) >= StringUtils.length(output.getImage()));
    }
    
    public String getExtension() {
        return (input != null) ? FilenameUtils.getExtension(input.getName()) : null;
    }

    public String getType() {
        return (input != null) ? input.getType() : null;
    }
    
    public byte[] getBytes() {
        byte[] data = null;
        if (!isEmpty()) {
            String txt = StringUtils.substringAfter(output.getImage(), ";base64,");
            if (StringUtils.isNotBlank(txt)) {
                data = Base64.decodeBase64(txt);
            }
        }
        return data;
    }
    
    public String getServer() {
        return server;
    }

    public void setServer(String server) {
        this.server = server;
    }

    public Object getMeta() {
        return meta;
    }

    public void setMeta(Object meta) {
        this.meta = meta;
    }

    public SlimInput getInput() {
        return input;
    }

    public void setInput(SlimInput input) {
        this.input = input;
    }

    public SlimOutput getOutput() {
        return output;
    }

    public void setOutput(SlimOutput output) {
        this.output = output;
    }
    
}
