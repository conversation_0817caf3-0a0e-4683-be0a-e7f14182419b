package com.miocontotermico.support.image.limitless;

/**
 *
 * <AUTHOR>
 */
public class Image {

    private String contentType;
    private byte[] bytes;

    public Image() {
    }

    public Image(String contentType, byte[] bytes) {
        this.contentType = contentType;
        this.bytes = bytes;
    }
    
    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    public byte[] getBytes() {
        return bytes;
    }

    public void setBytes(byte[] bytes) {
        this.bytes = bytes;
    }
    
}
