package com.miocontotermico.support;

import com.miocontotermico.core.Manager;
import com.miocontotermico.core.Templates;
import com.miocontotermico.dao.AgencyDao;
import com.miocontotermico.pojo.Agency;
import com.miocontotermico.util.RouteUtils;
import java.util.HashMap;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.jetty.http.HttpStatus;
import spark.CustomErrorPages;
import spark.ModelAndView;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;

/**
 *
 * <AUTHOR>
 */
public class ErrorController {
    
    public static Route notFound = (Request request, Response response) -> {
        if (StringUtils.contains(request.pathInfo(), "api/")) {
            response.status(HttpStatus.NOT_FOUND_404);
            return CustomErrorPages.NOT_FOUND;
        }
        if (!StringUtils.containsIgnoreCase(RouteUtils.mimetype(request), "html")) {
            response.status(HttpStatus.NOT_FOUND_404);
            return CustomErrorPages.NOT_FOUND;
        }
		
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        
        // agency
        Agency agenzia = AgencyDao.loadAgencyFirst();
        attributes.put("agency", agenzia);
        
        // manual rendering
        ModelAndView mv = Manager.render(Templates.ERROR_404, attributes, RouteUtils.pathType(request));
        String html = Manager.engine.render(mv);
        
        // return html as string
        return html;
    };
    
}
