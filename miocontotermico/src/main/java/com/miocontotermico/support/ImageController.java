package com.miocontotermico.support;

import com.miocontotermico.core.Defaults;
import com.miocontotermico.dao.ImageDao;
import com.miocontotermico.support.image.limitless.Image;
import com.miocontotermico.util.EnvironmentUtils;
import com.miocontotermico.util.ParamUtils;
import java.awt.AlphaComposite;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.security.InvalidParameterException;
import java.util.Date;
import javax.imageio.ImageIO;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.Response;
import spark.Route;

/**
 *
 * <AUTHOR>
 */
public class ImageController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ImageController.class.getName());

    public static Route image = (Request request, Response response) -> {

        // params
        ObjectId oid = ParamUtils.toObjectId(request.queryParams("oid"));

        if (oid != null) {

            Image img = null;
            try {
                img = ImageDao.loadImage(oid);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }

            if (img != null) {
                response.type(img.getContentType());
                
                if (EnvironmentUtils.hasNotResourcesHotDeploy()) {
                    // image caching
                    response.header("Cache-Control", "private, max-age=" + Defaults.IMAGE_RESOURCE_EXPIRATION_TIME);
                    response.header("Expires", new Date(System.currentTimeMillis() + (Defaults.IMAGE_RESOURCE_EXPIRATION_TIME * 1000)).toString());
                }
                
                try {
                    response.raw().getOutputStream().write(img.getBytes());
                    response.raw().getOutputStream().close();
                } catch (Exception ex) {
                    LOGGER.error("cannot return image; id " + oid + " exception class is " + ex.getClass().getSimpleName(), ex);
                }
                
            } else {
                LOGGER.warn("empty image oid " + oid);
            }
        } else {
            LOGGER.warn("empty oid " + oid);
        }

        // status needed to avoid spark filter exception
        return "";
    };
    
    public static Route thumbnail = (Request request, Response response) -> {

        // params
        ObjectId oid = ParamUtils.toObjectId(request.queryParams("oid"));
        double scaleFactor = NumberUtils.toDouble(request.queryParams("scaleFactor"), 1D);

        if (oid != null) {

            Image img = null;
            try {
                img = ImageDao.loadImage(oid);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }

            if (img != null) {
                response.type(img.getContentType());

                if (EnvironmentUtils.hasNotResourcesHotDeploy()) {
                    // image caching
                    response.header("Cache-Control", "private, max-age=" + Defaults.IMAGE_RESOURCE_EXPIRATION_TIME);
                    response.header("Expires", new Date(System.currentTimeMillis() + (Defaults.IMAGE_RESOURCE_EXPIRATION_TIME * 1000)).toString());
                }
                
                byte[] bytes = img.getBytes();
                if (scaleFactor != 1.0D) {
                    try {
                        bytes = thumbnail(scaleFactor, img.getContentType(), img.getBytes());
                    } catch (Exception ex) {
                        LOGGER.error("suppressed", ex);
                    }
                }

                if (bytes != null) {
                    response.raw().getOutputStream().write(bytes);
                    response.raw().getOutputStream().close();
                }
            }
        } else {
            LOGGER.warn("unexistent image oid " + oid);
        }

        // status needed to avoid spark filter exception
        return "";
    };

    private static byte[] thumbnail(double scaleFactor, String contentType, byte[] bytes) throws Exception {
        if (scaleFactor <= 0.0D) {
            throw new InvalidParameterException("invalid scaleFactor");
        }
        if (scaleFactor > 1.0D) {
            throw new InvalidParameterException("invalid scaleFactor");
        }

        byte[] thumb = null;

        // source image
        BufferedImage originalImage = null;
        try {

            originalImage = ImageIO.read(new ByteArrayInputStream(bytes));

            // scaled width and height
            int widthToScale = (int) (originalImage.getWidth() * scaleFactor);
            int heightToScale = (int) (originalImage.getHeight() * scaleFactor);

            ByteArrayOutputStream out = null;
            if ((widthToScale > 0) && (heightToScale > 0)) {

                // destination image
                BufferedImage resizedImage = null;
                try {

                    resizedImage = new BufferedImage(widthToScale, heightToScale, originalImage.getType());
                    Graphics2D g = resizedImage.createGraphics();

                    // draw resized image
                    g.setComposite(AlphaComposite.Src);
                    g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
                    g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
                    g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                    g.drawImage(originalImage, 0, 0, widthToScale, heightToScale, null);
                    g.dispose();

                    // identify image type
                    String type = "jpg";
                    if (StringUtils.containsIgnoreCase(contentType, "png")) {
                        type = "png";
                    }

                    // retrieving thumbnail bytes
                    out = new ByteArrayOutputStream();
                    ImageIO.write(resizedImage, type, out);

                    // result
                    if (out.size() > 0) {
                        thumb = out.toByteArray();
                    }
                    
                } finally {
                    // flush resources
                    if (resizedImage != null) {
                        try {
                            resizedImage.flush();
                        } catch (Exception ex) {
                            LOGGER.error("suppressed", ex);
                        }
                    }
                }

            }
        } finally {
            if (originalImage != null) {
                // flush resources
                try {
                    originalImage.flush();
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                }
            }
        }

        return thumb;
    }    

    ////////////
    // internals
    
}
