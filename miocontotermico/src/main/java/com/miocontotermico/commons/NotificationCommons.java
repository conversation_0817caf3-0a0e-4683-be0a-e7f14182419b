package com.miocontotermico.commons;

import com.miocontotermico.core.Defaults;
import com.miocontotermico.core.MailTemplates;
import com.miocontotermico.core.Manager;
import com.miocontotermico.dao.FirmDao;
import com.miocontotermico.dao.MailDao;
import com.miocontotermico.dao.SmtpDao;
import com.miocontotermico.dao.UserDao;
import com.miocontotermico.message.MessageSender;
import com.miocontotermico.message.SmtpService;
import com.miocontotermico.pojo.Eneaprocedure;
import com.miocontotermico.pojo.Evaluation;
import com.miocontotermico.pojo.Firm;
import com.miocontotermico.pojo.Invoicediscount;
import com.miocontotermico.pojo.Mail;
import com.miocontotermico.pojo.Order;
import com.miocontotermico.pojo.Procedure;
import com.miocontotermico.pojo.Smtp;
import com.miocontotermico.pojo.User;
import com.miocontotermico.pojo.types.NotificationType;
import com.miocontotermico.util.RouteUtils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.logging.Level;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;

/**
 *
 * <AUTHOR>
 */
public class NotificationCommons {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(NotificationCommons.class.getName());
    
    public static boolean notifyRegistration(Request request, User user) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (user == null) {
            throw new NullPointerException("empty user");
        }
        
        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }
        
        // language
        String language = Defaults.LANGUAGE;
        
        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();
        
        // customer notification
        if (StringUtils.isNotBlank(firm.getCustomerNotification()) &&
                !StringUtils.equalsIgnoreCase(firm.getCustomerNotification(), NotificationType.never.toString()) &&
                (user.getEmail() != null)) {
            senders.add(firm.getSiteEmail());
            recipients.add(user.getEmail());
            templates.add(MailTemplates.WELCOME);
        }
        
        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }
        
        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("user", user);
        
        Mail mail = null;
        try {
            mail = MailDao.loadMailByTemplate("WELCOME");
        } catch (Exception ex) {
            // suppressed
        }
        
        if (mail != null) {
            messageFields.put("textMail", mail.getDescription());
        }
        
        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);
        
        // result
        return sent;
    }
    
    public static boolean notifyRegistrationAdmin(Request request, User user) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (user == null) {
            throw new NullPointerException("empty user");
        }
        
        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }
        
        // language
        String language = Defaults.LANGUAGE;
        
        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();
        
        // customer notification
        senders.add(firm.getSiteEmail());
        recipients.add("<EMAIL>");
        templates.add(MailTemplates.WELCOME_ADMIN);
        
        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }
        
        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("user", user);
        
        Mail mail = null;
        try {
            mail = MailDao.loadMailByTemplate("WELCOME_ADMIN");
        } catch (Exception ex) {
            // suppressed
        }
        
        if (mail != null) {
            messageFields.put("textMail", mail.getDescription());
        }
        
        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);
        
        // result
        return sent;
    }
    public static boolean notifyProcedureUpload(Request request, Procedure procedure) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (procedure == null) {
            throw new NullPointerException("empty procedure");
        }
        
        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }
        
        // language
        String language = Defaults.LANGUAGE;
        
        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();
        
        // customer notification
        senders.add(firm.getSiteEmail());
        recipients.add("<EMAIL>");
        templates.add(MailTemplates.PROCEDURE_UPLOAD);
        
        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }
        
        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("procedure", procedure);
        
        Mail mail = null;
        try {
            mail = MailDao.loadMailByTemplate("PROCEDURE_UPLOAD");
        } catch (Exception ex) {
            // suppressed
        }
        
        if (mail != null) {
            messageFields.put("textMail", mail.getDescription());
        }
        
        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);
        
        // result
        return sent;
    }
    
    public static boolean notifyProcedureFinalUpload(Request request, Procedure procedure) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (procedure == null) {
            throw new NullPointerException("empty procedure");
        }
        
        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }
        
        // language
        String language = Defaults.LANGUAGE;
        
        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();
        
        // customer notification
        senders.add(firm.getSiteEmail());
        recipients.add("<EMAIL>");
        templates.add(MailTemplates.PROCEDURE_FINAL_UPLOAD);
        
        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }
        
        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("procedure", procedure);
        
        Mail mail = null;
        try {
            mail = MailDao.loadMailByTemplate("PROCEDURE_FINAL_UPLOAD");
        } catch (Exception ex) {
            // suppressed
        }
        
        if (mail != null) {
            messageFields.put("textMail", mail.getDescription());
        }
        
        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);
        
        // result
        return sent;
    }
    
    public static boolean notifyConfirmUser(Request request, User user) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (user == null) {
            throw new NullPointerException("empty user");
        }
        
        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }
        
        // language
        String language = Defaults.LANGUAGE;
        
        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();
        
        // customer notification
        if (StringUtils.isNotBlank(firm.getCustomerNotification()) &&
                !StringUtils.equalsIgnoreCase(firm.getCustomerNotification(), NotificationType.never.toString()) &&
                (user.getEmail() != null)) {
            senders.add(firm.getSiteEmail());
            recipients.add(user.getEmail());
            templates.add(MailTemplates.USER_CONFIRM);
        }
        
        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }
        
        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("user", user);
        
        Mail mail = null;
        try {
            mail = MailDao.loadMailByTemplate("USER_CONFIRM");
        } catch (Exception ex) {
            // suppressed
        }
        
        if (mail != null) {
            messageFields.put("textMail", mail.getDescription());
        }
        
        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);
        
        // result
        return sent;
    }
    
    public static boolean notifyProcedure(Request request, Procedure procedure, User procedureUser) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (procedure == null) {
            throw new NullPointerException("empty procedure");
        }
        
        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }
        
        // language
        String language = Defaults.LANGUAGE;
        
        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();
        
        // customer notification
        if (StringUtils.isNotBlank(firm.getCustomerNotification()) &&
                !StringUtils.equalsIgnoreCase(firm.getCustomerNotification(), NotificationType.never.toString()) &&
                (procedureUser.getEmail() != null)) {
            senders.add(firm.getSiteEmail());
            recipients.add(procedureUser.getEmail());
            templates.add(MailTemplates.PROCEDURE_STATUS_NOTIFICATION);
        }
        
        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }
        
        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("procedure", procedure);
        
        Mail mail = null;
        try {
            mail = MailDao.loadMailByTemplate("PROCEDURE_STATUS_NOTIFICATION");
        } catch (Exception ex) {
            // suppressed
        }
        
        if (mail != null) {
            messageFields.put("textMail", mail.getDescription());
        }
        
        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);
        
        // result
        return sent;
    }
    
    public static boolean notifyPaid(Request request, Order order) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (order == null) {
            throw new NullPointerException("empty order");
        }

        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }
        
        // language
        String language = Defaults.LANGUAGE;
        
        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();
        
        User userOrder = null;
        try {
            // user notification
            userOrder = UserDao.loadUser(order.getUserId());
        } catch (Exception ex) {
            //
        }
        if (StringUtils.isNotBlank(firm.getCustomerNotification()) &&
                !StringUtils.equalsIgnoreCase(firm.getCustomerNotification(), NotificationType.never.toString()) &&
                (userOrder != null && userOrder.getEmail() != null)) {
            senders.add(firm.getSiteEmail());
            recipients.add(userOrder.getEmail());
            templates.add(MailTemplates.PAID);
            
            senders.add(firm.getSiteEmail());
            recipients.add("<EMAIL>");
            templates.add(MailTemplates.ORDER_RECEIVE);
        }
        
        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }
        
        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("order", order);
        messageFields.put("user", userOrder);
        
        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);
        
        // result
        return sent;
    }
    
    public static boolean notifyOrderStatus(Request request, Order order) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (order == null) {
            throw new NullPointerException("empty order");
        }

        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }
        
        // language
        String language = Defaults.LANGUAGE;
        
        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();
        
        User userOrder = null;
        try {
            // user notification
            userOrder = UserDao.loadUser(order.getUserId());
        } catch (Exception ex) {
            //
        }
        if (StringUtils.isNotBlank(firm.getCustomerNotification()) &&
                !StringUtils.equalsIgnoreCase(firm.getCustomerNotification(), NotificationType.never.toString()) &&
                (userOrder != null && userOrder.getEmail() != null)) {
            senders.add(firm.getSiteEmail());
            recipients.add(userOrder.getEmail());
            templates.add(MailTemplates.ORDER_UPDATE);
        }
        
        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }
        
        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("order", order);
        messageFields.put("user", userOrder);
        
        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);
        
        // result
        return sent;
    }
    
    public static boolean notifyProcedureAssigned(Request request, Procedure procedure, User assignedUser) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (procedure == null) {
            throw new NullPointerException("empty procedure");
        }
        
        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }
        
        // language
        String language = Defaults.LANGUAGE;
        
        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();
        
        // customer notification
        if (StringUtils.isNotBlank(firm.getCustomerNotification()) &&
                !StringUtils.equalsIgnoreCase(firm.getCustomerNotification(), NotificationType.never.toString()) &&
                (assignedUser.getEmail() != null)) {
            senders.add(firm.getSiteEmail());
            recipients.add(assignedUser.getEmail());
            templates.add(MailTemplates.PROCEDURE_ASSIGN);
        }
        
        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }
        
        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("procedure", procedure);
        
        Mail mail = null;
        try {
            mail = MailDao.loadMailByTemplate("PROCEDURE_ASSIGN");
        } catch (Exception ex) {
            // suppressed
        }
        
        if (mail != null) {
            messageFields.put("textMail", mail.getDescription());
        }
        
        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);
        
        // result
        return sent;
    }
    
    public static boolean notifyProcedureReceiveToUser(Request request, Procedure procedure, User procedureUser) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (procedure == null) {
            throw new NullPointerException("empty procedure");
        }
        if (procedureUser == null) {
            throw new NullPointerException("empty procedureUser");
        }
        
        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }
        
        // language
        String language = Defaults.LANGUAGE;
        
        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();
        
        // customer notification
        if (StringUtils.isNotBlank(firm.getCustomerNotification()) &&
                !StringUtils.equalsIgnoreCase(firm.getCustomerNotification(), NotificationType.never.toString()) &&
                (procedureUser.getEmail() != null)) {
            senders.add(firm.getSiteEmail());
            recipients.add(procedureUser.getEmail());
            templates.add(MailTemplates.PROCEDURE_CONFIRM);
        }
        
        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }
        
        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("procedure", procedure);
        
        Mail mail = null;
        try {
            mail = MailDao.loadMailByTemplate("PROCEDURE_CONFIRM");
        } catch (Exception ex) {
            // suppressed
        }
        
        if (mail != null) {
            messageFields.put("textMail", mail.getDescription());
        }
        
        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);
        
        // result
        return sent;
    }
    public static boolean notifyProcedureReceive(Request request, Procedure procedure) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (procedure == null) {
            throw new NullPointerException("empty procedure");
        }
        
        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }
        
        // language
        String language = Defaults.LANGUAGE;
        
        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();
        
        // customer notification
        if (StringUtils.isNotBlank(firm.getCustomerNotification()) &&
                !StringUtils.equalsIgnoreCase(firm.getCustomerNotification(), NotificationType.never.toString()) &&
                (procedure.getEmail() != null)) {
            senders.add(firm.getSiteEmail());
            recipients.add("<EMAIL>");
            templates.add(MailTemplates.PROCEDURE_RECEIVE);
        }
        
        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }
        
        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("procedure", procedure);
        
        Mail mail = null;
        try {
            mail = MailDao.loadMailByTemplate("PROCEDURE_RECEIVE");
        } catch (Exception ex) {
            // suppressed
        }
        
        if (mail != null) {
            messageFields.put("textMail", mail.getDescription());
        }
        
        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);
        
        // result
        return sent;
    }
    
    public static boolean notifyEvaluationUpload(Request request, Evaluation evaluation) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (evaluation == null) {
            throw new NullPointerException("empty evaluation");
        }
        
        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }
        
        // language
        String language = Defaults.LANGUAGE;
        
        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();
        
        // customer notification
        senders.add(firm.getSiteEmail());
        recipients.add("<EMAIL>");
        templates.add(MailTemplates.EVALUATION_UPLOAD);
        
        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }
        
        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("evaluation", evaluation);
        
        Mail mail = null;
        try {
            mail = MailDao.loadMailByTemplate("EVALUATION_UPLOAD");
        } catch (Exception ex) {
            // suppressed
        }
        
        if (mail != null) {
            messageFields.put("textMail", mail.getDescription());
        }
        
        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);
        
        // result
        return sent;
    }
    
    public static boolean notifyEvaluationFinalUpload(Request request, Evaluation evaluation) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (evaluation == null) {
            throw new NullPointerException("empty evaluation");
        }
        
        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }
        
        // language
        String language = Defaults.LANGUAGE;
        
        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();
        
        // customer notification
        senders.add(firm.getSiteEmail());
        recipients.add("<EMAIL>");
        templates.add(MailTemplates.EVALUATION_FINAL_UPLOAD);
        
        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }
        
        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("evaluation", evaluation);
        
        Mail mail = null;
        try {
            mail = MailDao.loadMailByTemplate("EVALUATION_FINAL_UPLOAD");
        } catch (Exception ex) {
            // suppressed
        }
        
        if (mail != null) {
            messageFields.put("textMail", mail.getDescription());
        }
        
        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);
        
        // result
        return sent;
    }
    
    public static boolean notifyEvaluation(Request request, Evaluation evaluation, User evaluationUser) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (evaluation == null) {
            throw new NullPointerException("empty evaluation");
        }
        
        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }
        
        // language
        String language = Defaults.LANGUAGE;
        
        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();
        
        // customer notification
        if (StringUtils.isNotBlank(firm.getCustomerNotification()) &&
                !StringUtils.equalsIgnoreCase(firm.getCustomerNotification(), NotificationType.never.toString()) &&
                (evaluationUser.getEmail() != null)) {
            senders.add(firm.getSiteEmail());
            recipients.add(evaluationUser.getEmail());
            templates.add(MailTemplates.EVALUATION_STATUS_NOTIFICATION);
        }
        
        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }
        
        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("evaluation", evaluation);
        
        Mail mail = null;
        try {
            mail = MailDao.loadMailByTemplate("EVALUATION_STATUS_NOTIFICATION");
        } catch (Exception ex) {
            // suppressed
        }
        
        if (mail != null) {
            messageFields.put("textMail", mail.getDescription());
        }
        
        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);
        
        // result
        return sent;
    }
    
    public static boolean notifyEvaluationAssigned(Request request, Evaluation evaluation, User assignedUser) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (evaluation == null) {
            throw new NullPointerException("empty evaluation");
        }
        
        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }
        
        // language
        String language = Defaults.LANGUAGE;
        
        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();
        
        // customer notification
        if (StringUtils.isNotBlank(firm.getCustomerNotification()) &&
                !StringUtils.equalsIgnoreCase(firm.getCustomerNotification(), NotificationType.never.toString()) &&
                (assignedUser.getEmail() != null)) {
            senders.add(firm.getSiteEmail());
            recipients.add(assignedUser.getEmail());
            templates.add(MailTemplates.EVALUATION_ASSIGN);
        }
        
        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }
        
        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("evaluation", evaluation);
        
        Mail mail = null;
        try {
            mail = MailDao.loadMailByTemplate("EVALUATION_ASSIGN");
        } catch (Exception ex) {
            // suppressed
        }
        
        if (mail != null) {
            messageFields.put("textMail", mail.getDescription());
        }
        
        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);
        
        // result
        return sent;
    }
    
    public static boolean notifyEvaluationReceiveToUser(Request request, Evaluation evaluation, User evaluationUser) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (evaluation == null) {
            throw new NullPointerException("empty evaluation");
        }
        if (evaluationUser == null) {
            throw new NullPointerException("empty evaluationUser");
        }
        
        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }
        
        // language
        String language = Defaults.LANGUAGE;
        
        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();
        
        // customer notification
        if (StringUtils.isNotBlank(firm.getCustomerNotification()) &&
                !StringUtils.equalsIgnoreCase(firm.getCustomerNotification(), NotificationType.never.toString()) &&
                (evaluationUser.getEmail() != null)) {
            senders.add(firm.getSiteEmail());
            recipients.add(evaluationUser.getEmail());
            templates.add(MailTemplates.EVALUATION_CONFIRM);
        }
        
        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }
        
        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("evaluation", evaluation);
        
        Mail mail = null;
        try {
            mail = MailDao.loadMailByTemplate("EVALUATION_CONFIRM");
        } catch (Exception ex) {
            // suppressed
        }
        
        if (mail != null) {
            messageFields.put("textMail", mail.getDescription());
        }
        
        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);
        
        // result
        return sent;
    }
    public static boolean notifyEvaluationReceive(Request request, Evaluation evaluation) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (evaluation == null) {
            throw new NullPointerException("empty evaluation");
        }
        
        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }
        
        // language
        String language = Defaults.LANGUAGE;
        
        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();
        
        // customer notification
        if (StringUtils.isNotBlank(firm.getCustomerNotification()) &&
                !StringUtils.equalsIgnoreCase(firm.getCustomerNotification(), NotificationType.never.toString())) {
            senders.add(firm.getSiteEmail());
            recipients.add("<EMAIL>");
            templates.add(MailTemplates.EVALUATION_RECEIVE);
        }
        
        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }
        
        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("evaluation", evaluation);
        
        Mail mail = null;
        try {
            mail = MailDao.loadMailByTemplate("EVALUATION_RECEIVE");
        } catch (Exception ex) {
            // suppressed
        }
        
        if (mail != null) {
            messageFields.put("textMail", mail.getDescription());
        }
        
        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);
        
        // result
        return sent;
    }
    public static boolean notifyEneaprocedureUpload(Request request, Eneaprocedure eneaprocedure) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (eneaprocedure == null) {
            throw new NullPointerException("empty eneaprocedure");
        }
        
        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }
        
        // language
        String language = Defaults.LANGUAGE;
        
        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();
        
        // customer notification
        senders.add(firm.getSiteEmail());
        recipients.add("<EMAIL>");
        templates.add(MailTemplates.ENEAPROCEDURE_UPLOAD);
        
        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }
        
        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("eneaprocedure", eneaprocedure);
        
        Mail mail = null;
        try {
            mail = MailDao.loadMailByTemplate("ENEAPROCEDURE_UPLOAD");
        } catch (Exception ex) {
            // suppressed
        }
        
        if (mail != null) {
            messageFields.put("textMail", mail.getDescription());
        }
        
        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);
        
        // result
        return sent;
    }
    
    public static boolean notifyEneaprocedureFinalUpload(Request request, Eneaprocedure eneaprocedure) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (eneaprocedure == null) {
            throw new NullPointerException("empty eneaprocedure");
        }
        
        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }
        
        // language
        String language = Defaults.LANGUAGE;
        
        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();
        
        // customer notification
        senders.add(firm.getSiteEmail());
        recipients.add("<EMAIL>");
        templates.add(MailTemplates.ENEAPROCEDURE_FINAL_UPLOAD);
        
        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }
        
        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("eneaprocedure", eneaprocedure);
        
        Mail mail = null;
        try {
            mail = MailDao.loadMailByTemplate("ENEAPROCEDURE_FINAL_UPLOAD");
        } catch (Exception ex) {
            // suppressed
        }
        
        if (mail != null) {
            messageFields.put("textMail", mail.getDescription());
        }
        
        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);
        
        // result
        return sent;
    }
    
    public static boolean notifyEneaprocedure(Request request, Eneaprocedure eneaprocedure, User eneaprocedureUser) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (eneaprocedure == null) {
            throw new NullPointerException("empty eneaprocedure");
        }
        
        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }
        
        // language
        String language = Defaults.LANGUAGE;
        
        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();
        
        // customer notification
        if (StringUtils.isNotBlank(firm.getCustomerNotification()) &&
                !StringUtils.equalsIgnoreCase(firm.getCustomerNotification(), NotificationType.never.toString()) &&
                (eneaprocedureUser.getEmail() != null)) {
            senders.add(firm.getSiteEmail());
            recipients.add(eneaprocedureUser.getEmail());
            templates.add(MailTemplates.ENEAPROCEDURE_STATUS_NOTIFICATION);
        }
        
        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }
        
        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("eneaprocedure", eneaprocedure);
        
        Mail mail = null;
        try {
            mail = MailDao.loadMailByTemplate("ENEAPROCEDURE_STATUS_NOTIFICATION");
        } catch (Exception ex) {
            // suppressed
        }
        
        if (mail != null) {
            messageFields.put("textMail", mail.getDescription());
        }
        
        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);
        
        // result
        return sent;
    }
    
    public static boolean notifyEneaprocedureAssigned(Request request, Eneaprocedure eneaprocedure, User assignedUser) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (eneaprocedure == null) {
            throw new NullPointerException("empty eneaprocedure");
        }
        
        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }
        
        // language
        String language = Defaults.LANGUAGE;
        
        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();
        
        // customer notification
        if (StringUtils.isNotBlank(firm.getCustomerNotification()) &&
                !StringUtils.equalsIgnoreCase(firm.getCustomerNotification(), NotificationType.never.toString()) &&
                (assignedUser.getEmail() != null)) {
            senders.add(firm.getSiteEmail());
            recipients.add(assignedUser.getEmail());
            templates.add(MailTemplates.ENEAPROCEDURE_ASSIGN);
        }
        
        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }
        
        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("eneaprocedure", eneaprocedure);
        
        Mail mail = null;
        try {
            mail = MailDao.loadMailByTemplate("ENEAPROCEDURE_ASSIGN");
        } catch (Exception ex) {
            // suppressed
        }
        
        if (mail != null) {
            messageFields.put("textMail", mail.getDescription());
        }
        
        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);
        
        // result
        return sent;
    }
    
    public static boolean notifyEneaprocedureReceiveToUser(Request request, Eneaprocedure eneaprocedure, User eneaprocedureUser) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (eneaprocedure == null) {
            throw new NullPointerException("empty eneaprocedure");
        }
        if (eneaprocedureUser == null) {
            throw new NullPointerException("empty eneaprocedureUser");
        }
        
        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }
        
        // language
        String language = Defaults.LANGUAGE;
        
        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();
        
        // customer notification
        if (StringUtils.isNotBlank(firm.getCustomerNotification()) &&
                !StringUtils.equalsIgnoreCase(firm.getCustomerNotification(), NotificationType.never.toString()) &&
                (eneaprocedureUser.getEmail() != null)) {
            senders.add(firm.getSiteEmail());
            recipients.add(eneaprocedureUser.getEmail());
            templates.add(MailTemplates.ENEAPROCEDURE_CONFIRM);
        }
        
        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }
        
        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("eneaprocedure", eneaprocedure);
        
        Mail mail = null;
        try {
            mail = MailDao.loadMailByTemplate("ENEAPROCEDURE_CONFIRM");
        } catch (Exception ex) {
            // suppressed
        }
        
        if (mail != null) {
            messageFields.put("textMail", mail.getDescription());
        }
        
        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);
        
        // result
        return sent;
    }
    public static boolean notifyEneaprocedureReceive(Request request, Eneaprocedure eneaprocedure) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (eneaprocedure == null) {
            throw new NullPointerException("empty eneaprocedure");
        }
        
        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }
        
        // language
        String language = Defaults.LANGUAGE;
        
        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();
        
        // customer notification
        if (StringUtils.isNotBlank(firm.getCustomerNotification()) &&
                !StringUtils.equalsIgnoreCase(firm.getCustomerNotification(), NotificationType.never.toString())) {
            senders.add(firm.getSiteEmail());
            recipients.add("<EMAIL>");
            templates.add(MailTemplates.ENEAPROCEDURE_RECEIVE);
        }
        
        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }
        
        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("eneaprocedure", eneaprocedure);
        
        Mail mail = null;
        try {
            mail = MailDao.loadMailByTemplate("ENEAPROCEDURE_RECEIVE");
        } catch (Exception ex) {
            // suppressed
        }
        
        if (mail != null) {
            messageFields.put("textMail", mail.getDescription());
        }
        
        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);
        
        // result
        return sent;
    }
    
    public static boolean notifyInvoicediscountUpload(Request request, Invoicediscount invoicediscount) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (invoicediscount == null) {
            throw new NullPointerException("empty invoicediscount");
        }
        
        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }
        
        // language
        String language = Defaults.LANGUAGE;
        
        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();
        
        // customer notification
        senders.add(firm.getSiteEmail());
        recipients.add("<EMAIL>");
        templates.add(MailTemplates.INVOICEDISCOUNT_UPLOAD);
        
        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }
        
        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("invoicediscount", invoicediscount);
        
        Mail mail = null;
        try {
            mail = MailDao.loadMailByTemplate("INVOICEDISCOUNT_UPLOAD");
        } catch (Exception ex) {
            // suppressed
        }
        
        if (mail != null) {
            messageFields.put("textMail", mail.getDescription());
        }
        
        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);
        
        // result
        return sent;
    }
    
    public static boolean notifyInvoicediscountFinalUpload(Request request, Invoicediscount invoicediscount) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (invoicediscount == null) {
            throw new NullPointerException("empty invoicediscount");
        }
        
        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }
        
        // language
        String language = Defaults.LANGUAGE;
        
        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();
        
        // customer notification
        senders.add(firm.getSiteEmail());
        recipients.add("<EMAIL>");
        templates.add(MailTemplates.INVOICEDISCOUNT_FINAL_UPLOAD);
        
        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }
        
        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("invoicediscount", invoicediscount);
        
        Mail mail = null;
        try {
            mail = MailDao.loadMailByTemplate("INVOICEDISCOUNT_FINAL_UPLOAD");
        } catch (Exception ex) {
            // suppressed
        }
        
        if (mail != null) {
            messageFields.put("textMail", mail.getDescription());
        }
        
        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);
        
        // result
        return sent;
    }
    
    public static boolean notifyInvoicediscount(Request request, Invoicediscount invoicediscount, User invoicediscountUser) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (invoicediscount == null) {
            throw new NullPointerException("empty invoicediscount");
        }
        
        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }
        
        // language
        String language = Defaults.LANGUAGE;
        
        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();
        
        // customer notification
        if (StringUtils.isNotBlank(firm.getCustomerNotification()) &&
                !StringUtils.equalsIgnoreCase(firm.getCustomerNotification(), NotificationType.never.toString()) &&
                (invoicediscountUser.getEmail() != null)) {
            senders.add(firm.getSiteEmail());
            recipients.add(invoicediscountUser.getEmail());
            templates.add(MailTemplates.INVOICEDISCOUNT_STATUS_NOTIFICATION);
        }
        
        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }
        
        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("invoicediscount", invoicediscount);
        
        Mail mail = null;
        try {
            mail = MailDao.loadMailByTemplate("INVOICEDISCOUNT_STATUS_NOTIFICATION");
        } catch (Exception ex) {
            // suppressed
        }
        
        if (mail != null) {
            messageFields.put("textMail", mail.getDescription());
        }
        
        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);
        
        // result
        return sent;
    }
    
    public static boolean notifyInvoicediscountAssigned(Request request, Invoicediscount invoicediscount, User assignedUser) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (invoicediscount == null) {
            throw new NullPointerException("empty invoicediscount");
        }
        
        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }
        
        // language
        String language = Defaults.LANGUAGE;
        
        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();
        
        // customer notification
        if (StringUtils.isNotBlank(firm.getCustomerNotification()) &&
                !StringUtils.equalsIgnoreCase(firm.getCustomerNotification(), NotificationType.never.toString()) &&
                (assignedUser.getEmail() != null)) {
            senders.add(firm.getSiteEmail());
            recipients.add(assignedUser.getEmail());
            templates.add(MailTemplates.INVOICEDISCOUNT_ASSIGN);
        }
        
        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }
        
        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("invoicediscount", invoicediscount);
        
        Mail mail = null;
        try {
            mail = MailDao.loadMailByTemplate("INVOICEDISCOUNT_ASSIGN");
        } catch (Exception ex) {
            // suppressed
        }
        
        if (mail != null) {
            messageFields.put("textMail", mail.getDescription());
        }
        
        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);
        
        // result
        return sent;
    }
    
    public static boolean notifyInvoicediscountReceiveToUser(Request request, Invoicediscount invoicediscount, User invoicediscountUser) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (invoicediscount == null) {
            throw new NullPointerException("empty invoicediscount");
        }
        if (invoicediscountUser == null) {
            throw new NullPointerException("empty invoicediscountUser");
        }
        
        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }
        
        // language
        String language = Defaults.LANGUAGE;
        
        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();
        
        // customer notification
        if (StringUtils.isNotBlank(firm.getCustomerNotification()) &&
                !StringUtils.equalsIgnoreCase(firm.getCustomerNotification(), NotificationType.never.toString()) &&
                (invoicediscountUser.getEmail() != null)) {
            senders.add(firm.getSiteEmail());
            recipients.add(invoicediscountUser.getEmail());
            templates.add(MailTemplates.INVOICEDISCOUNT_CONFIRM);
        }
        
        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }
        
        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("invoicediscount", invoicediscount);
        
        Mail mail = null;
        try {
            mail = MailDao.loadMailByTemplate("INVOICEDISCOUNT_CONFIRM");
        } catch (Exception ex) {
            // suppressed
        }
        
        if (mail != null) {
            messageFields.put("textMail", mail.getDescription());
        }
        
        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);
        
        // result
        return sent;
    }
    public static boolean notifyInvoicediscountReceive(Request request, Invoicediscount invoicediscount) {
        if (request == null) {
            throw new NullPointerException("empty request");
        }
        if (invoicediscount == null) {
            throw new NullPointerException("empty invoicediscount");
        }
        
        // firm
        Firm firm = firm();
        if (firm == null) {
            return false;
        }
        
        // language
        String language = Defaults.LANGUAGE;
        
        // all available recipients and templates
        List<String> senders = new ArrayList<>();
        List<String> recipients = new ArrayList<>();
        List<String> templates = new ArrayList<>();
        
        // customer notification
        if (StringUtils.isNotBlank(firm.getCustomerNotification()) &&
                !StringUtils.equalsIgnoreCase(firm.getCustomerNotification(), NotificationType.never.toString())) {
            senders.add(firm.getSiteEmail());
            recipients.add("<EMAIL>");
            templates.add(MailTemplates.INVOICEDISCOUNT_RECEIVE);
        }
        
        // none to notify
        if (senders.isEmpty()) {
            return false;
        }
        if (recipients.isEmpty()) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }
        
        // attributes
        Map<String, Object> messageFields = new HashMap<>();
        messageFields.put("language", language);
        messageFields.put("invoicediscount", invoicediscount);
        
        Mail mail = null;
        try {
            mail = MailDao.loadMailByTemplate("INVOICEDISCOUNT_RECEIVE");
        } catch (Exception ex) {
            // suppressed
        }
        
        if (mail != null) {
            messageFields.put("textMail", mail.getDescription());
        }
        
        // notify all
        boolean sent = notify(request, messageFields, senders, recipients, templates);
        
        // result
        return sent;
    }
    
    private static boolean notify(Request request, Map<String, Object> messageFields, List<String> senders, List<String> recipients, List<String> templates) {
        if (request == null) {
            return false;
        }
        if (messageFields == null) {
            return false;
        }
        if (senders == null) {
            return false;
        }
        if (recipients == null) {
            return false;
        }
        if (templates.isEmpty()) {
            return false;
        }
        if (senders.size() != recipients.size()) {
            return false;
        }
        if (recipients.size() != templates.size()) {
            return false;
        }

        // smtp
        SmtpService smtp = null;
        Smtp smtpConfig = null;
        try {
            smtpConfig = SmtpDao.loadSmtp();
        } catch (Exception ex) {
            LOGGER.error("unable to load smtp", ex);
        }
        if (smtpConfig != null) {
            smtp = new SmtpService(
                smtpConfig.getHostname(),
                smtpConfig.getPort(),
                smtpConfig.getAuthentication(),
                smtpConfig.getUsername(),
                smtpConfig.getPassword(),
                smtpConfig.getEncryption(),
                smtpConfig.getStartTls(),
                smtpConfig.getApikey(),
                smtpConfig.getSender()
            );
        }
        if ((smtp != null) && !MessageSender.isValidHost(
                smtp.getHostname(),
                smtp.getPort(),
                smtp.getAuthentication(),
                smtp.getUsername(),
                smtp.getPassword(),
                smtp.getEncryption(),
                smtp.getStartTls(),
                smtp.getApikey())
                ) {
            LOGGER.info("wrong smtp config");
        }
        if (smtp == null) {
            LOGGER.info("switching to default smtp");
            smtp = Manager.defaultSmtpService();
        }
        if ((smtp != null) && !MessageSender.isValidHost(
                smtp.getHostname(),
                smtp.getPort(),
                smtp.getAuthentication(),
                smtp.getUsername(),
                smtp.getPassword(),
                smtp.getEncryption(),
                smtp.getStartTls(),
                smtp.getApikey())
                ) {
            LOGGER.error("wrong default smtp config");
            smtp = null;
        }
        if (smtp == null) {
            return false;
        }
        
        // notify all
        int count = 0;
        for (int i = 0; i < recipients.size(); i++) {
            if (MessageSender.validEmailAddress(recipients.get(i))) {
                
//                if (EnvironmentUtils.isNotLocal()) {
                //if (true) {
                    boolean sent = Manager.sendMail(smtp,
                            senders.get(i),
                            "no-reply",
                            recipients.get(i),
                            templates.get(i),
                            messageFields,
                            RouteUtils.pathType(request)
                    );
                    
                    if (sent) {
                        count++;
                    }
//                } else {
//                    LOGGER.info("EMAIL from " + senders.get(i) + " to " + recipients.get(i));
//                }
            } else {
                LOGGER.error("wrong recipient", recipients.get(i));
            }
        }
        
        return (count > 0);
    }

    private static Firm firm() {
        Firm firm = null;
        try {
            firm = FirmDao.loadFirm();
        } catch (Exception ex) {
            LOGGER.error("unable to load firm");
        }
        return firm;
    }
    
}
