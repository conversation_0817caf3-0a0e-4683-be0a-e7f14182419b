package com.miocontotermico.commons;

import com.miocontotermico.dao.PaymentDao;
import com.miocontotermico.pojo.Payment;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class PaymentCommons {

    private static final Logger LOGGER = LoggerFactory.getLogger(PaymentCommons.class.getName());
    
    public static boolean isAvailable(String paymentCode) {
        return exists(paymentCode);
    }    
    
    public static boolean isNotAvailable(String paymentCode) {
        return !isAvailable(paymentCode);
    }    
    
    public static boolean isValidPayment(Payment payment) {
        boolean valid = (payment != null) &&
                StringUtils.isNotBlank(payment.getName())&&
                StringUtils.isNotBlank(payment.getCode()) &&
                !exists(payment.getCode())
                ;
        
        if (!valid) {
            if (payment != null) {
                LOGGER.warn(
                        "payment validation problem:\n" +
                        "- name " + payment.getName() + "\n" +
                        "- code " + payment.getCode() + "\n" +
                        "- channel " + payment.getChannel() + "\n"
                );
            } else {
                LOGGER.warn(
                        "payment validation problem:\n" +
                        "- empty"
                );
            }
        }
        
        return valid;
    }    
    
    public static boolean exists(String code) {
        if (StringUtils.isBlank(code)) {
            return false;
        }
        Payment payment = null;
        try {
            payment = PaymentDao.loadPaymentByCode(code);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }
        return (payment != null);
    }

}
