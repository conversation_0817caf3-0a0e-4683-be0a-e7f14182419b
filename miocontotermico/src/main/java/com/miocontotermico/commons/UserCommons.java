package com.miocontotermico.commons;

import com.miocontotermico.login.PasswordHash;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;
import org.apache.commons.lang3.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class UserCommons {
    
    public static boolean areValidCredentials(
            String email,
            String password) {
        
        return
                StringUtils.isNotBlank(email) &&
                StringUtils.isNotBlank(password)
                ;
    }
    
    public static boolean isValidUserCustomerRegistration(
            String email,
            String password,
            String passwordConfirm,
            String profileType) {
        
        return
                StringUtils.isNotBlank(email) &&
                StringUtils.isNotBlank(password) &&
                StringUtils.isNotBlank(passwordConfirm) &&
                StringUtils.equals(password, passwordConfirm) &&
                StringUtils.isNotBlank(profileType)
                ;
    }
    
    public static boolean isValidUserCustomerPasswordChange(
            String passwordOldSaved,
            String passwordOld,
            String password,
            String passwordConfirm) {
        boolean done;
        
        try {
            done = PasswordHash.validatePassword(passwordOld, passwordOldSaved) &&
                StringUtils.isNotBlank(password) &&
                (StringUtils.length(password) >= 8) &&
                StringUtils.isNotBlank(passwordConfirm) &&
                StringUtils.equals(password, passwordConfirm)
                ;
        } catch (NoSuchAlgorithmException | InvalidKeySpecException | ArrayIndexOutOfBoundsException ex) {
             return false;
        }
        return done;
    }
}
