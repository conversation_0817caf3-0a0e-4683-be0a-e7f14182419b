package com.miocontotermico.commons;

import com.miocontotermico.core.Defaults;
import com.miocontotermico.dao.ProcedureDao;
import com.miocontotermico.dao.UserDao;
import com.miocontotermico.pojo.Procedure;
import com.miocontotermico.pojo.User;
import com.miocontotermico.pojo.types.StatusType;
import com.miocontotermico.procedure.ProcedureEntry;
import com.miocontotermico.util.TimeUtils;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class ProcedureCommons {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProcedureCommons.class.getName());
    
    public static Procedure initDraft(User user) {
        if (user == null) {
            return null;
        }
        
        Procedure draft = new Procedure();
        
        draft.setProfileType(user.getProfileType());
        draft.setDate(TimeUtils.now());
        draft.setStatus(StatusType.draft.toString());
        draft.setLastStatusUpdate(TimeUtils.now());
        draft.setUserId(user.getId());
        draft.setCountryCode(Defaults.COUNTRY);
        
        if (user.getProfileType().equals("privato")) {
            // init shipping
            draft.setName(user.getName());
            draft.setLastname(user.getLastname());
            draft.setFullname(user.getFullname());
            if (StringUtils.isBlank(user.getFullname())) {
                String fullname = (StringUtils.isNotBlank(user.getName()) ? (user.getName() + " ") : "") + (StringUtils.isNotBlank(user.getLastname()) ? user.getLastname() : "");
                draft.setFullname(fullname);
            }
            draft.setAddress(user.getAddress());
            draft.setCity(user.getCity());
            draft.setPostalCode(user.getPostalCode());
            draft.setProvinceCode(user.getProvinceCode());
            draft.setCountryCode(user.getCountryCode());
            if (StringUtils.isBlank(user.getCountryCode())) {
                draft.setCountryCode(Defaults.COUNTRY);
            }
            draft.setEmail(user.getEmail());
            draft.setPhoneNumber(user.getPhoneNumber());

            draft.setVatNumber(user.getVatNumber());
            draft.setTin(user.getTin());
            draft.setPec(user.getPec());
            draft.setSdiNumber(user.getSdiNumber());
            draft.setCoupon(user.getCoupon());
        }


        // save
        ObjectId procedureId = null;
        try {
            procedureId = ProcedureDao.insertProcedure(draft);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }

        // reload
        if (procedureId != null) {
            try {
                draft = ProcedureDao.loadProcedure(procedureId);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
        }

        return draft;
    }
    
    public static Procedure initExistingDraft(Procedure draft) {
        if (draft == null) {
            return null;
        }
        
        Procedure newDraft = new Procedure();
        
        newDraft.setId(draft.getId());
        newDraft.setProfileType(draft.getProfileType());
        newDraft.setDate(TimeUtils.now());
        newDraft.setStatus(StatusType.draft.toString());
        newDraft.setLastStatusUpdate(TimeUtils.now());
        newDraft.setUserId(draft.getUserId());
        newDraft.setCountryCode(Defaults.COUNTRY);

        // save
        try {
            ProcedureDao.updateProcedure(newDraft);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }

        // reload
        if (newDraft.getId() != null) {
            try {
                newDraft = ProcedureDao.loadProcedure(newDraft.getId());
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
        }

        return newDraft;
    }
    
 
    public static List<ProcedureEntry> toEntries(List<Procedure> procedureList) {
        List<ProcedureEntry> procedureEntryList = null;
        if ((procedureList != null) &&
            (procedureList.size() > 0)) {
            for (Procedure procedure : procedureList) {
                ProcedureEntry entry = toEntry(procedure);
                if (procedureEntryList == null) {
                    procedureEntryList = new ArrayList<>();
                }
                procedureEntryList.add(entry);                
            }   
        }
        return procedureEntryList;
    }
    
    public static ProcedureEntry toEntry(Procedure procedure) {
        ProcedureEntry entry = null;
        if (procedure != null) {
            
            entry = new ProcedureEntry();
            entry.setProcedure(procedure);
            
            if (procedure.getUserId() != null) {
                try {
                    User user = UserDao.loadUser(procedure.getUserId());
                    entry.setUser(user);
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                    return null;
                }
            }

        }
        return entry;
    }    
    
}
