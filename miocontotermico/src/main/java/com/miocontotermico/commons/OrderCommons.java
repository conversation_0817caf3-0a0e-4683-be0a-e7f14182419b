package com.miocontotermico.commons;

import com.miocontotermico.dao.UserDao;
import com.miocontotermico.orders.OrderEntry;
import com.miocontotermico.pojo.Order;
import com.miocontotermico.pojo.User;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class OrderCommons {
    private static final Logger LOGGER = LoggerFactory.getLogger(OrderCommons.class.getName());    
    
    public static List<OrderEntry> toEntries(List<Order> orderList) {
        List<OrderEntry> orderEntryList = null;
        if ((orderList != null) &&
            (orderList.size() > 0)) {
            for (Order order : orderList) {
                OrderEntry entry = toEntry(order);
                if (orderEntryList == null) {
                    orderEntryList = new ArrayList<>();
                }
                orderEntryList.add(entry);                
            }   
        }
        return orderEntryList;
    }
    
    public static OrderEntry toEntry(Order order) {
        OrderEntry entry = null;
        if (order != null) {
            
            entry = new OrderEntry();
            entry.setOrder(order);
            
            if (order.getUserId() != null) {
                try {
                    User user = UserDao.loadUser(order.getUserId());
                    entry.setUser(user);
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                    return null;
                }
            }
        }
        return entry;
    }   

    public static final String describe(Order order) {
        String description = "";
        if ((order != null) && (order.getCredit() != null)) {
            description = order.getCredit() + " Crediti";
        }
        return description;
    }
}
