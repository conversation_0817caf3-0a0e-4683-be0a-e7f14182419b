package com.miocontotermico.commons;

import com.miocontotermico.core.Defaults;
import com.miocontotermico.dao.EvaluationDao;
import com.miocontotermico.dao.UserDao;
import com.miocontotermico.pojo.Evaluation;
import com.miocontotermico.pojo.User;
import com.miocontotermico.pojo.types.StatusType;
import com.miocontotermico.evaluation.EvaluationEntry;
import com.miocontotermico.util.TimeUtils;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class EvaluationCommons {

    private static final Logger LOGGER = LoggerFactory.getLogger(EvaluationCommons.class.getName());
    
    public static Evaluation initDraft(User user) {
        if (user == null) {
            return null;
        }
        
        Evaluation draft = new Evaluation();
        
        draft.setProfileType(user.getProfileType());
        draft.setDate(TimeUtils.now());
        draft.setStatus(StatusType.draft.toString());
        draft.setLastStatusUpdate(TimeUtils.now());
        draft.setUserId(user.getId());
        draft.setCountryCode(Defaults.COUNTRY);
        
        if (user.getProfileType().equals("privato")) {
            // init shipping
            draft.setName(user.getName());
            draft.setLastname(user.getLastname());
            draft.setFullname(user.getFullname());
            if (StringUtils.isBlank(user.getFullname())) {
                String fullname = (StringUtils.isNotBlank(user.getName()) ? (user.getName() + " ") : "") + (StringUtils.isNotBlank(user.getLastname()) ? user.getLastname() : "");
                draft.setFullname(fullname);
            }
            draft.setAddress(user.getAddress());
            draft.setCity(user.getCity());
            draft.setPostalCode(user.getPostalCode());
            draft.setProvinceCode(user.getProvinceCode());
            draft.setCountryCode(user.getCountryCode());
            if (StringUtils.isBlank(user.getCountryCode())) {
                draft.setCountryCode(Defaults.COUNTRY);
            }
            draft.setEmail(user.getEmail());
            draft.setVatNumber(user.getVatNumber());
            draft.setTin(user.getTin());
        }


        // save
        ObjectId evaluationId = null;
        try {
            evaluationId = EvaluationDao.insertEvaluation(draft);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }

        // reload
        if (evaluationId != null) {
            try {
                draft = EvaluationDao.loadEvaluation(evaluationId);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
        }

        return draft;
    }
    
    public static Evaluation initExistingDraft(Evaluation draft) {
        if (draft == null) {
            return null;
        }
        
        Evaluation newDraft = new Evaluation();
        
        newDraft.setId(draft.getId());
        newDraft.setProfileType(draft.getProfileType());
        newDraft.setDate(TimeUtils.now());
        newDraft.setStatus(StatusType.draft.toString());
        newDraft.setLastStatusUpdate(TimeUtils.now());
        newDraft.setUserId(draft.getUserId());
        newDraft.setCountryCode(Defaults.COUNTRY);

        // save
        try {
            EvaluationDao.updateEvaluation(newDraft);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }

        // reload
        if (newDraft.getId() != null) {
            try {
                newDraft = EvaluationDao.loadEvaluation(newDraft.getId());
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
        }

        return newDraft;
    }
    
 
    public static List<EvaluationEntry> toEntries(List<Evaluation> evaluationList) {
        List<EvaluationEntry> evaluationEntryList = null;
        if ((evaluationList != null) &&
            (evaluationList.size() > 0)) {
            for (Evaluation evaluation : evaluationList) {
                EvaluationEntry entry = toEntry(evaluation);
                if (evaluationEntryList == null) {
                    evaluationEntryList = new ArrayList<>();
                }
                evaluationEntryList.add(entry);                
            }   
        }
        return evaluationEntryList;
    }
    
    public static EvaluationEntry toEntry(Evaluation evaluation) {
        EvaluationEntry entry = null;
        if (evaluation != null) {
            
            entry = new EvaluationEntry();
            entry.setEvaluation(evaluation);
            
            if (evaluation.getUserId() != null) {
                try {
                    User user = UserDao.loadUser(evaluation.getUserId());
                    entry.setUser(user);
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                    return null;
                }
            }

        }
        return entry;
    }    
    
}
