package com.miocontotermico.commons;

import com.miocontotermico.core.Defaults;
import com.miocontotermico.dao.EneaprocedureDao;
import com.miocontotermico.dao.UserDao;
import com.miocontotermico.eneaprocedure.EneaprocedureEntry;
import com.miocontotermico.pojo.Eneaprocedure;
import com.miocontotermico.pojo.User;
import com.miocontotermico.pojo.types.StatusType;
import com.miocontotermico.util.TimeUtils;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class EneaprocedureCommons {

    private static final Logger LOGGER = LoggerFactory.getLogger(EneaprocedureCommons.class.getName());
    
    public static Eneaprocedure initDraft(User user) {
        if (user == null) {
            return null;
        }
        
        Eneaprocedure draft = new Eneaprocedure();
        
        draft.setProfileType(user.getProfileType());
        draft.setDate(TimeUtils.now());
        draft.setStatus(StatusType.draft.toString());
        draft.setLastStatusUpdate(TimeUtils.now());
        draft.setUserId(user.getId());
        draft.setCountryCode(Defaults.COUNTRY);
        
        if (user.getProfileType().equals("privato")) {
            // init shipping
            draft.setName(user.getName());
            draft.setLastname(user.getLastname());
            draft.setFullname(user.getFullname());
            if (StringUtils.isBlank(user.getFullname())) {
                String fullname = (StringUtils.isNotBlank(user.getName()) ? (user.getName() + " ") : "") + (StringUtils.isNotBlank(user.getLastname()) ? user.getLastname() : "");
                draft.setFullname(fullname);
            }
            draft.setAddress(user.getAddress());
            draft.setCity(user.getCity());
            draft.setPostalCode(user.getPostalCode());
            draft.setProvinceCode(user.getProvinceCode());
            draft.setCountryCode(user.getCountryCode());
            if (StringUtils.isBlank(user.getCountryCode())) {
                draft.setCountryCode(Defaults.COUNTRY);
            }
            draft.setEmail(user.getEmail());
            draft.setVatNumber(user.getVatNumber());
            draft.setTin(user.getTin());
        }


        // save
        ObjectId eneaprocedureId = null;
        try {
            eneaprocedureId = EneaprocedureDao.insertEneaprocedure(draft);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }

        // reload
        if (eneaprocedureId != null) {
            try {
                draft = EneaprocedureDao.loadEneaprocedure(eneaprocedureId);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
        }

        return draft;
    }
    
    public static Eneaprocedure initExistingDraft(Eneaprocedure draft) {
        if (draft == null) {
            return null;
        }
        
        Eneaprocedure newDraft = new Eneaprocedure();
        
        newDraft.setId(draft.getId());
        newDraft.setProfileType(draft.getProfileType());
        newDraft.setDate(TimeUtils.now());
        newDraft.setStatus(StatusType.draft.toString());
        newDraft.setLastStatusUpdate(TimeUtils.now());
        newDraft.setUserId(draft.getUserId());
        newDraft.setCountryCode(Defaults.COUNTRY);

        // save
        try {
            EneaprocedureDao.updateEneaprocedure(newDraft);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }

        // reload
        if (newDraft.getId() != null) {
            try {
                newDraft = EneaprocedureDao.loadEneaprocedure(newDraft.getId());
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
        }

        return newDraft;
    }
    
 
    public static List<EneaprocedureEntry> toEntries(List<Eneaprocedure> eneaprocedureList) {
        List<EneaprocedureEntry> eneaprocedureEntryList = null;
        if ((eneaprocedureList != null) &&
            (eneaprocedureList.size() > 0)) {
            for (Eneaprocedure eneaprocedure : eneaprocedureList) {
                EneaprocedureEntry entry = toEntry(eneaprocedure);
                if (eneaprocedureEntryList == null) {
                    eneaprocedureEntryList = new ArrayList<>();
                }
                eneaprocedureEntryList.add(entry);                
            }   
        }
        return eneaprocedureEntryList;
    }
    
    public static EneaprocedureEntry toEntry(Eneaprocedure eneaprocedure) {
        EneaprocedureEntry entry = null;
        if (eneaprocedure != null) {
            
            entry = new EneaprocedureEntry();
            entry.setEneaprocedure(eneaprocedure);
            
            if (eneaprocedure.getUserId() != null) {
                try {
                    User user = UserDao.loadUser(eneaprocedure.getUserId());
                    entry.setUser(user);
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                    return null;
                }
            }

        }
        return entry;
    }    
    
}
