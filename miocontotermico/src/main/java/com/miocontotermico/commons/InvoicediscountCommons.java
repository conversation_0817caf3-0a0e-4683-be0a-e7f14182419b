package com.miocontotermico.commons;

import com.miocontotermico.core.Defaults;
import com.miocontotermico.dao.InvoicediscountDao;
import com.miocontotermico.dao.UserDao;
import com.miocontotermico.invoicediscount.InvoicediscountEntry;
import com.miocontotermico.pojo.Invoicediscount;
import com.miocontotermico.pojo.User;
import com.miocontotermico.pojo.types.StatusType;
import com.miocontotermico.util.TimeUtils;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class InvoicediscountCommons {

    private static final Logger LOGGER = LoggerFactory.getLogger(InvoicediscountCommons.class.getName());
    
    public static Invoicediscount initDraft(User user) {
        if (user == null) {
            return null;
        }
        
        Invoicediscount draft = new Invoicediscount();
        
        draft.setProfileType(user.getProfileType());
        draft.setDate(TimeUtils.now());
        draft.setStatus(StatusType.draft.toString());
        draft.setLastStatusUpdate(TimeUtils.now());
        draft.setUserId(user.getId());
        draft.setCountryCode(Defaults.COUNTRY);
        
        if (user.getProfileType().equals("privato")) {
            // init shipping
            draft.setName(user.getName());
            draft.setLastname(user.getLastname());
            draft.setFullname(user.getFullname());
            if (StringUtils.isBlank(user.getFullname())) {
                String fullname = (StringUtils.isNotBlank(user.getName()) ? (user.getName() + " ") : "") + (StringUtils.isNotBlank(user.getLastname()) ? user.getLastname() : "");
                draft.setFullname(fullname);
            }
            draft.setAddress(user.getAddress());
            draft.setCity(user.getCity());
            draft.setPostalCode(user.getPostalCode());
            draft.setProvinceCode(user.getProvinceCode());
            draft.setCountryCode(user.getCountryCode());
            if (StringUtils.isBlank(user.getCountryCode())) {
                draft.setCountryCode(Defaults.COUNTRY);
            }
            draft.setEmail(user.getEmail());
            draft.setVatNumber(user.getVatNumber());
            draft.setTin(user.getTin());
        }


        // save
        ObjectId invoicediscountId = null;
        try {
            invoicediscountId = InvoicediscountDao.insertInvoicediscount(draft);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }

        // reload
        if (invoicediscountId != null) {
            try {
                draft = InvoicediscountDao.loadInvoicediscount(invoicediscountId);
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
        }

        return draft;
    }
    
    public static Invoicediscount initExistingDraft(Invoicediscount draft) {
        if (draft == null) {
            return null;
        }
        
        Invoicediscount newDraft = new Invoicediscount();
        
        newDraft.setId(draft.getId());
        newDraft.setProfileType(draft.getProfileType());
        newDraft.setDate(TimeUtils.now());
        newDraft.setStatus(StatusType.draft.toString());
        newDraft.setLastStatusUpdate(TimeUtils.now());
        newDraft.setUserId(draft.getUserId());
        newDraft.setCountryCode(Defaults.COUNTRY);

        // save
        try {
            InvoicediscountDao.updateInvoicediscount(newDraft);
        } catch (Exception ex) {
            LOGGER.error("suppressed", ex);
        }

        // reload
        if (newDraft.getId() != null) {
            try {
                newDraft = InvoicediscountDao.loadInvoicediscount(newDraft.getId());
            } catch (Exception ex) {
                LOGGER.error("suppressed", ex);
            }
        }

        return newDraft;
    }
    
 
    public static List<InvoicediscountEntry> toEntries(List<Invoicediscount> invoicediscountList) {
        List<InvoicediscountEntry> invoicediscountEntryList = null;
        if ((invoicediscountList != null) &&
            (invoicediscountList.size() > 0)) {
            for (Invoicediscount invoicediscount : invoicediscountList) {
                InvoicediscountEntry entry = toEntry(invoicediscount);
                if (invoicediscountEntryList == null) {
                    invoicediscountEntryList = new ArrayList<>();
                }
                invoicediscountEntryList.add(entry);                
            }   
        }
        return invoicediscountEntryList;
    }
    
    public static InvoicediscountEntry toEntry(Invoicediscount invoicediscount) {
        InvoicediscountEntry entry = null;
        if (invoicediscount != null) {
            
            entry = new InvoicediscountEntry();
            entry.setInvoicediscount(invoicediscount);
            
            if (invoicediscount.getUserId() != null) {
                try {
                    User user = UserDao.loadUser(invoicediscount.getUserId());
                    entry.setUser(user);
                } catch (Exception ex) {
                    LOGGER.error("suppressed", ex);
                    return null;
                }
            }

        }
        return entry;
    }    
    
}
