package com.miocontotermico.extensions;

/**
 *
 * <AUTHOR>
 */

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class NewLineFunction implements Function {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    
    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("text");
        return names;        
    }

    @Override
    public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
        String text = (String) args.get("text");
        text = StringUtils.replace(text, "\r\n", "<br />");
        text = StringUtils.replace(text, "\n", "<br />");
        return text;
    }

}