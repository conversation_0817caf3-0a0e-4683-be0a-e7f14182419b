package com.miocontotermico.extensions;

import com.miocontotermico.dao.FileDao;
import com.miocontotermico.dao.ImageDao;
import com.miocontotermico.support.file.Filex;
import com.miocontotermico.support.image.Image;
import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class FileinfoFunction implements Function {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    
    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("table");
        names.add("oid");
        names.add("name");
        return names;        
    }
    
    @Override
    public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
        
        String table = (String) args.get("table");
        ObjectId oid = (ObjectId) args.get("oid");
        String name = (String) args.get("name");
        
        String info = "";
        
        switch (table) {
            case "file":
                info = infoFromFile(oid, name);
                break;
            case "image":
                info = infoFromImage(oid, name);
                break;
            default:
                logger.error("unrecognized table " + table);
                break;
        }
        
        return info;
    }
    
    private String infoFromFile(ObjectId oid, String name) {
        String info = "";
        if ((oid != null) &&
                StringUtils.isNotBlank(name)) {
            Filex item = null;
            try {
                item = FileDao.loadFileInfo(oid);
            } catch (Exception ex) {
                logger.error("suppressed", ex);
            }
            if (item != null) {
                try {
                    info = BeanUtils.getProperty(item, name);
                } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException | RuntimeException ex) {
                    logger.error("suppressed", ex);
                }
            } else {
                logger.error("unrecognized file ", name + " " + oid);
            }
        }
        return info;
    }
    
    private String infoFromImage(ObjectId oid, String name) {
        String info = "";
        if ((oid != null) &&
                StringUtils.isNotBlank(name)) {
            Image item = null;
            try {
                item = ImageDao.loadImageInfo(oid);
            } catch (Exception ex) {
                logger.error("suppressed", ex);
            }
            if (item != null) {
                try {
                    info = BeanUtils.getProperty(item, name);
                } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException | RuntimeException ex) {
                    logger.error("suppressed", ex);
                }
            } else {
                logger.error("unrecognized file ", name + " " + oid);
            }
        }
        return info;
    }
    
}
