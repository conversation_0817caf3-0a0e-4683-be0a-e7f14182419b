package com.miocontotermico.extensions;

/**
 *
 * <AUTHOR>
 */

import com.miocontotermico.util.ImageUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import java.io.IOException;
import org.apache.commons.httpclient.DefaultHttpMethodRetryHandler;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpException;
import org.apache.commons.httpclient.HttpStatus;
import org.apache.commons.httpclient.methods.GetMethod;
import org.apache.commons.httpclient.params.HttpMethodParams;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DataUriFunction implements Function {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());

    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("url");
        return names;
    }

    @Override
    public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
        String datauri = null;

        String url = (String) args.get("url");

        if (StringUtils.isNotBlank(url)) {

            // create an instance of HttpClient
            HttpClient client = new HttpClient();

            // create a method instance
            GetMethod method = new GetMethod(url);

            // provide custom retry handler is necessary
            method.getParams().setParameter(HttpMethodParams.RETRY_HANDLER,
                    new DefaultHttpMethodRetryHandler(3, false));

            try {
                // execute the method
                int statusCode = client.executeMethod(method);

                if (statusCode != HttpStatus.SC_OK) {
                    logger.error("Method failed: " + method.getStatusLine());
                }

                // read the response body
                byte[] responseBody = method.getResponseBody();

                // read the content type
                String contentType = (method.getResponseHeader("Content-Type") != null) ? method.getResponseHeader("Content-Type").getValue() : null;

                // deal with the response (use caution: ensure correct character encoding and is not binary data)

                if (StringUtils.isNotBlank(contentType) &&
                        StringUtils.containsIgnoreCase(contentType, "png") &&
                        (responseBody != null) &&
                        (responseBody.length > 0)) {
                    datauri = ImageUtils.toDatauri(contentType, responseBody);
                }

            } catch (HttpException ex) {
                logger.error("Fatal protocol violation", ex);
            } catch (IOException ex) {
                logger.error("Fatal transport error", ex);
            } finally {
                // release the connection
                method.releaseConnection();
            }

        }

        return datauri;
    }

}