package com.miocontotermico.extensions;

import com.miocontotermico.dao.CountryDao;
import com.miocontotermico.dao.ProvinceDao;
import com.miocontotermico.pojo.Country;
import com.miocontotermico.pojo.Province;
import com.miocontotermico.pojo.types.ServiceType;
import com.mitchellbosecke.pebble.extension.Function;
import com.miocontotermico.dao.UserDao;
import com.miocontotermico.pojo.User;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class DecodeFunction implements Function {
    
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    
    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("table");
        names.add("value");
        return names;        
    }
    
    @Override
    public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
        
        String table = (String) args.get("table");
        String value = (args.get("value") != null) ? args.get("value").toString() : null;
        
        String description = "-";
        
        switch (table) {
            case "country":
                description = decodeCountry(value);
                break;
            case "province":
                description = decodeProvince(value);
                break;            
            case "user":
                description = decodeUser(value);
                break;
            case "service":
                description = decodeEnum(ServiceType.class, value);
                break;
            default:
                logger.error("unrecognized table " + table);
                break;
        }
        
        return description;
    }
    
    private String decodeCountry(String value) {
        String description = "-";
        if (StringUtils.isNotBlank(value)) {
            Country item = null;
            try {
                item = CountryDao.loadCountry(value);
            } catch (Exception ex) {
                logger.error("suppressed", ex);
            }
            if (item != null) {
                if (StringUtils.isNotBlank(item.getDescription())) {
                    description = item.getDescription();
                }
            } else {
                logger.error("unrecognized code ", value);
            }
        }
        return description;
    }
    
    private String decodeProvince(String value) {
        String description = "-";
        if (StringUtils.isNotBlank(value)) {
            Province item = null;
            try {
                item = ProvinceDao.loadProvince(value);
            } catch (Exception ex) {
                logger.error("suppressed", ex);
            }
            if (item != null) {
                if (StringUtils.isNotBlank(item.getDescription())) {
                    description = item.getDescription();
                }
            } else {
                logger.error("unrecognized code ", value);
            }
        }
        return description;
    }
    
    private String decodeUser(String value) {
        String description = "-";
        if (StringUtils.isNotBlank(value)) {
            User item = null;
            try {
                item = UserDao.loadUser(new ObjectId(value));
            } catch (Exception ex) {
                logger.error("suppressed", ex);
            }
            if (item != null) {
                if (StringUtils.isNotBlank(item.getName())) {
                    description = item.getName() + " " + item.getLastname();
                }
            } else {
                logger.error("unrecognized code ", value);
            }
        }
        return description;
    }
    

    private <E extends Enum<E>> String decodeEnum(Class<E> enumClass, String value) {
        String description = "-";
        if (StringUtils.isNotBlank(value)) {
            E item = null;
            try {
                item = Enum.valueOf(enumClass, value);
            } catch (IllegalArgumentException ignored) {
                // ignored
            }
            if (item != null) {
                try {
                    String dsc = BeanUtils.getProperty(item, "description");
                    if (StringUtils.isNotBlank(dsc)) {
                        description = dsc;
                    }
                } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException | RuntimeException ex) {
                    // ignored
                }
            } else {
                logger.error("unrecognized code ", value);
            }
        }
        return description;
    }

}
