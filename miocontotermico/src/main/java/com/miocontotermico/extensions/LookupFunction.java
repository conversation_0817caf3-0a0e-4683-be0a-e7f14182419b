package com.miocontotermico.extensions;

import com.miocontotermico.dao.CountryDao;
import com.miocontotermico.dao.ProvinceDao;
import com.miocontotermico.pojo.Country;
import com.miocontotermico.pojo.Province;
import com.miocontotermico.pojo.types.ServiceType;
import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.apache.commons.beanutils.BeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.utils.StringUtils;

/**
 *
 * <AUTHOR>
 */
public class LookupFunction implements Function {

    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    
    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("table");
        return names;        
    }
    
    @Override
    public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
        
        String table = (String) args.get("table");
        
        List items = null;
        
        switch (table) {
            case "country":
                items = loadCountry();
                break;
            case "province":
                items = loadProvince();
                break;
            case "service":
                items = loadEnum(ServiceType.class, "credit");
                break;
            default:
                logger.error("unrecognized table " + table);
                break;
        }
        
        return items;
    }

    private List<Country> loadCountry() {
        List<Country> items = null;
        try {
            items = CountryDao.loadCountryList();
        } catch (Exception ex) {
            logger.error("suppressed", ex);
        }
        return items;
    }
    
    private List<Province> loadProvince() {
        List<Province> items = null;
        try {
            items = ProvinceDao.loadProvinceList();
        } catch (Exception ex) {
            logger.error("suppressed", ex);
        }
        return items;
    }

    private <E extends Enum<E>> List<String[]> loadEnum(Class<E> enumClass) {
        return loadEnum(enumClass, null);
    }
    
    private <E extends Enum<E>> List<String[]> loadEnum(Class<E> enumClass, String extra) {
        List<String[]> items = new ArrayList<>();
        
        List<E> values = new ArrayList<>(Arrays.asList(enumClass.getEnumConstants()));
        for (E item : values) {
            String[] kv = new String[2 + (StringUtils.isNotBlank(extra) ? 1 : 0)];
            kv[0] = item.name();

            String dsc = null;
            try {
                dsc = BeanUtils.getProperty(item, "description");
            } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException | RuntimeException ex) {
                // ignored
            }
            kv[1] = dsc;
            
            // get "extra" property from enum
            if (StringUtils.isNotBlank(extra)) {
                
                String extraDsc = null;
                try {
                    extraDsc = BeanUtils.getProperty(item, extra);
                } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException | RuntimeException ex) {
                    // ignored
                }
                kv[2] = extraDsc;
                
            }
            
            items.add(kv);
        }
        
        return items;
    }


}
