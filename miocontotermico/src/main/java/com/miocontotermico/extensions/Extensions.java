package com.miocontotermico.extensions;

/**
 *
 * <AUTHOR>
 */

import java.util.HashMap;
import java.util.Map;

import com.mitchellbosecke.pebble.extension.AbstractExtension;
import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.extension.Filter;

public class Extensions extends AbstractExtension {

    @Override
    public Map<String, Filter> getFilters() {
        Map<String, Filter> filters = new HashMap<>();
        filters.put("striphtml", new StripHtmlFilter());
        return filters;
    }
    
    @Override
    public Map<String, Function> getFunctions() {
        Map<String, Function> functions = new HashMap<>();
        // path
        functions.put("paths", new PathsFunction());
        // lookup
        functions.put("lookup", new LookupFunction());
        functions.put("decode", new DecodeFunction());
        // file
        functions.put("fileinfo", new FileinfoFunction());
        // date
        functions.put("before", new BeforeFunction());
        functions.put("after", new AfterFunction());
        functions.put("now", new NowFunction());
        functions.put("today", new TodayFunction());
        functions.put("daysbetween", new DaysBetweenFunction());
        functions.put("addminutes", new AddMinutesFunction());
        functions.put("addhours", new AddHoursFunction());
        functions.put("adddays", new AddDaysFunction());
        functions.put("addmonths", new AddMonthsFunction());
        // text
        functions.put("newline", new NewLineFunction());
        // images
        functions.put("datauri", new DataUriFunction());
        return functions;
    }

}
