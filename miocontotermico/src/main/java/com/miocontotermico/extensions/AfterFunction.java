package com.miocontotermico.extensions;

import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 */
public class AfterFunction implements Function {

    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("first");
        names.add("second");
        return names;        
    }
    
    @Override
    public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
        
        Date first = (Date) args.get("first");
        Date second = (Date) args.get("second");
        
        return ((first != null) && (second != null)) ? first.after(second) : false;
    }
    
}
