package com.miocontotermico.mail;

import com.miocontotermico.core.Manager;
import com.miocontotermico.core.Paths;
import com.miocontotermico.core.Templates;
import com.miocontotermico.dao.MailDao;
import com.miocontotermico.pojo.Mail;
import com.miocontotermico.pojo.User;
import com.miocontotermico.util.ParamUtils;
import com.miocontotermico.util.PojoUtils;
import com.miocontotermico.util.RouteUtils;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.bson.types.ObjectId;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.TemplateViewRoute;

/**
 *
 * <AUTHOR>
 */
public class MailController {
    public static TemplateViewRoute mails = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN) ;
            return Manager.renderEmpty();
        }
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));
        
        List<Mail> mailList = MailDao.loadMailList();
        attributes.put("mailList", mailList);

        return Manager.render(Templates.MAILS, attributes, RouteUtils.pathType(request));
    };
        
    public static TemplateViewRoute mail_detail = (Request request, Response response) -> {
        
        ObjectId mailId = ParamUtils.toObjectId(request.queryParams("mailId"));
        
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        attributes.put("user", user);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN) ;
            return Manager.renderEmpty();
        }

        Mail mail = null;
        if (mailId != null) {
            mail = MailDao.loadMail(mailId);
        }
        attributes.put("mail", mail);
        
        // flash message
        attributes.put("success", Paths.hasSuccess(request));
        attributes.put("error", Paths.hasError(request));

        return Manager.render(Templates.MAIL_DETAIL, attributes, RouteUtils.pathType(request));
    };    
    
    public static Route mail_detail_save = (Request request, Response response) -> {
        // params
        ObjectId mailId = ParamUtils.toObjectId(request.queryParams("mailId"));
        
        // logged user
        String token = Manager.getToken(request);
        User user = Manager.getUser(token, response);
        if (user == null) {
            response.redirect(RouteUtils.contextPath(request) + Paths.LOGIN);
            return null;
        }
        
        if (mailId != null) {
            Mail mailOld = MailDao.loadMail(mailId);
            Mail mail = PojoUtils.mergeFromRequest(request, mailOld);
                         
            MailDao.updateMail(mail);
                                
            // navigation
            response.redirect(RouteUtils.contextPath(request) + Paths.success(Paths.MAILS));

        } else {
            response.redirect(RouteUtils.contextPath(request) + Paths.error(Paths.MAILS));
        }

        return null;
    };      
}
