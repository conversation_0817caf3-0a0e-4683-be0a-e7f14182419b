package com.miocontotermico.order;

import com.miocontotermico.core.Manager;
import com.miocontotermico.pojo.Order;
import com.miocontotermico.pojo.types.OrderStatusType;
import com.miocontotermico.pojo.types.OrderType;
import com.miocontotermico.pojo.types.StatusType;
import com.miocontotermico.util.MongoUtils;
import com.miocontotermico.util.TimeUtils;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import static com.mongodb.client.model.Filters.gte;
import static com.mongodb.client.model.Filters.lte;
import static com.mongodb.client.model.Filters.ne;
import java.security.InvalidParameterException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class OrderDao {

    public static Order loadOrder(ObjectId orderId) throws Exception {
        if (orderId == null) {
            throw new InvalidParameterException("empty orderId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("order");
        Document doc = collection.find(eq("_id", orderId)).first();
        return Manager.fromDocument(doc, Order.class);
    }
    
    public static Order loadCartOrder(ObjectId userId) throws Exception {
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("order");
        Document doc = collection.find(
                and(
                        ne("cancelled", true),
                        eq("orderType", OrderType.contract.toString()),
                        eq("status", OrderStatusType.cart.toString()),
                        eq("userId", userId)))
                .first();
        return Manager.fromDocument(doc, Order.class);
    }
    
    public static Order loadCartOrderByTransactionKey(String transactionKey) throws Exception {
        if (StringUtils.isBlank(transactionKey)) {
            throw new InvalidParameterException("empty transactionKey");
        }
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("order");
        Document doc = collection.find(
                and(ne("cancelled", true),
                        eq("status", "cart"),
                        eq("transactionKey", transactionKey)))
                .first();
        return Manager.fromDocument(doc, Order.class);
    }
    
    public static void updateOrder(Order order) throws Exception {
        if (order == null) {
            throw new InvalidParameterException("empty order");
        }

        // defaults
        Date now = new Date();
        
        // internals
        order.setLastUpdate(now);

        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("order");
        collection.replaceOne(
                new Document("_id", order.getId()),
                Manager.toDocument(order)
        );
        
    }    
    
    public static ObjectId insertOrder(Order order) throws Exception {
        if (order == null) {
            throw new InvalidParameterException("empty order");
        }
        
        // defaults
        Date now = new Date();
        
        // internals
        order.setCreation(now);
        order.setLastUpdate(now);
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("order");
        Document doc = Manager.toDocument(order);
        collection.insertOne(doc);
        return doc.get("_id", ObjectId.class);
    }
    
    public static List<Order> loadOrderListByDateRange(Date from, Date to) throws Exception {
        return loadOrderListByDateRange(from, to, false);
    }
    
    public static List<Order> loadOrderListByDateRange(Date from, Date to, boolean skipAnnulled) throws Exception {
        if (from == null) {
            throw new InvalidParameterException("empty from");
        }
        if (to == null) {
            throw new InvalidParameterException("empty to");
        }
        
        from = MongoUtils.toComparableDate(TimeUtils.beginOfDay(from));
        to = MongoUtils.toComparableDate(TimeUtils.endOfDay(to));
        
        List<Bson> filters = new ArrayList<>();
        filters.add(ne("cancelled", true));
        filters.add(eq("orderType", OrderType.contract.toString()));
        filters.add(ne("status", OrderStatusType.cart.toString()));
        if (skipAnnulled) {
            filters.add(ne("status", OrderStatusType.annulled.toString()));
        }
        filters.add(gte("date", from));
        filters.add(lte("date", to));
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("order");
        FindIterable<Document> list = collection.find(and(filters));
        
        return Manager.fromDocumentList(list, Order.class);
    }


    public static List<Order> loadOrderListByDateRangeAndUserId(Date from, Date to, ObjectId userId) throws Exception {
        if (from == null) {
            throw new InvalidParameterException("empty from");
        }
        if (to == null) {
            throw new InvalidParameterException("empty to");
        }
        if (userId == null) {
            throw new InvalidParameterException("empty userId");
        }
        
        from = MongoUtils.toComparableDate(TimeUtils.beginOfDay(from));
        to = MongoUtils.toComparableDate(TimeUtils.endOfDay(to));
        
        MongoCollection<Document> collection = Manager.mongoDatabase.getCollection("order");
        FindIterable<Document> list = collection.find(and(
                eq("userId", userId),
                eq("orderType", OrderType.contract.toString()),
                ne("status", OrderStatusType.cart.toString()),
                ne("cancelled", true),
                gte("date", from),
                lte("date", to))
        );
        
        return Manager.fromDocumentList(list, Order.class);
    }
    
}
