<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<groupId>com.miocontotermico</groupId>
	<artifactId>miocontotermico</artifactId>
	<version>1.0</version>
	<packaging>war</packaging>
	<name>miocontotermico</name>
	<properties>
		<endorsed.dir>${project.build.directory}/endorsed</endorsed.dir>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>
	<dependencies>
		<dependency>
			<groupId>com.sparkjava</groupId>
			<artifactId>spark-core</artifactId>
			<version>2.7.1</version>
		</dependency>
        <dependency>
            <groupId>io.pebbletemplates</groupId>
            <artifactId>pebble</artifactId>
            <version>3.1.5</version>
        </dependency>        
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-api</artifactId>
			<version>1.7.21</version>
		</dependency>
		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-classic</artifactId>
			<version>1.1.7</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.4</version>
		</dependency>
		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
			<version>2.9.0</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.8.3</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-email</artifactId>
			<version>1.5</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-io</artifactId>
			<version>1.3.2</version>
		</dependency>
		<dependency>
			<groupId>org.mongodb</groupId>
			<artifactId>mongo-java-driver</artifactId>
			<version>3.3.0</version>
		</dependency>
		<dependency>
			<groupId>commons-codec</groupId>
			<artifactId>commons-codec</artifactId>
			<version>1.10</version>
		</dependency>
		<dependency>
			<groupId>commons-fileupload</groupId>
			<artifactId>commons-fileupload</artifactId>
			<version>1.3.2</version>
		</dependency>
		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.5</version>
		</dependency>
		<dependency>
			<groupId>commons-beanutils</groupId>
			<artifactId>commons-beanutils</artifactId>
			<version>1.9.3</version>
		</dependency>
		<dependency>
			<groupId>com.openhtmltopdf</groupId>
			<artifactId>openhtmltopdf-core</artifactId>
			<version>0.0.1-RC8</version>
		</dependency>
		<dependency>
			<groupId>com.openhtmltopdf</groupId>
			<artifactId>openhtmltopdf-pdfbox</artifactId>
			<version>0.0.1-RC8</version>
		</dependency>
		<dependency>
			<groupId>com.openhtmltopdf</groupId>
			<artifactId>openhtmltopdf-jsoup-dom-converter</artifactId>
			<version>0.0.1-RC8</version>
		</dependency>
		<dependency>
			<groupId>com.google.maps</groupId>
			<artifactId>google-maps-services</artifactId>
			<version>0.1.17</version>
		</dependency>
                <dependency>
                    <groupId>io.github.nexipayments</groupId>
                    <artifactId>sdk-java-npg</artifactId>
                    <version>0.0.5-RELEASE</version>
                </dependency>
                <dependency>
                    <groupId>com.paypal.sdk</groupId>
                    <artifactId>checkout-sdk</artifactId>
                    <version>1.0.4</version>
                </dependency>
                <dependency>
                    <groupId>com.github.slugify</groupId>
                    <artifactId>slugify</artifactId>
                    <version>2.1.9</version>
                </dependency>
                <dependency>
                   <groupId>com.squareup.okhttp3</groupId>
                   <artifactId>okhttp</artifactId>
                   <version>4.12.0</version>
                </dependency>
                <dependency>
                 <groupId>commons-httpclient</groupId>
                 <artifactId>commons-httpclient</artifactId>
                 <version>3.1</version>
                 <type>jar</type>
                </dependency>
  
                <!-- JDK 11 FIX -->
                <!-- API, java.xml.bind module -->
                <dependency>
                    <groupId>jakarta.xml.bind</groupId>
                    <artifactId>jakarta.xml.bind-api</artifactId>
                    <version>2.3.2</version>
                </dependency>
                <!-- Runtime, com.sun.xml.bind module -->
                <dependency>
                    <groupId>org.glassfish.jaxb</groupId>
                    <artifactId>jaxb-runtime</artifactId>
                    <version>2.3.2</version>
                </dependency>
                <!-- /JDK 11 FIX -->
                 <dependency>
                  <groupId>com.sendgrid</groupId>
                  <artifactId>sendgrid-java</artifactId>
                  <version>4.3.0</version>
                  <type>jar</type>
                 </dependency>
                <dependency>
			<groupId>javax</groupId>
			<artifactId>javaee-web-api</artifactId>
			<version>7.0</version>
			<scope>provided</scope>
		</dependency>
	</dependencies>
    <scm>
        <connection>scm:git:ssh://**************:gregnanin/miocontotermico-bundle.git</connection>
        <developerConnection>scm:git:ssh://**************:gregnanin/miocontotermico-bundle.git</developerConnection>
        <tag>HEAD</tag>
    </scm>
	<build>
        <finalName>miocontotermico</finalName>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.1</version>
				<configuration>
					<source>11</source>
					<target>11</target>
					<compilerArguments>
						<endorseddirs>${endorsed.dir}</endorseddirs>
					</compilerArguments>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-war-plugin</artifactId>
				<version>2.3</version>
				<configuration>
                    <archive>                   
                        <manifest>
                            <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                        </manifest>
                        <manifestEntries>
                            <Implementation-Build>${buildNumber}</Implementation-Build>
                        </manifestEntries>
                    </archive>                    
					<failOnMissingWebXml>false</failOnMissingWebXml>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-dependency-plugin</artifactId>
				<version>2.6</version>
				<executions>
					<execution>
						<phase>validate</phase>
						<goals>
							<goal>copy</goal>
						</goals>
						<configuration>
							<outputDirectory>${endorsed.dir}</outputDirectory>
							<silent>true</silent>
							<artifactItems>
								<artifactItem>
									<groupId>javax</groupId>
									<artifactId>javaee-endorsed-api</artifactId>
									<version>7.0</version>
									<type>jar</type>
								</artifactItem>
							</artifactItems>
						</configuration>
					</execution>
				</executions>
			</plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>buildnumber-maven-plugin</artifactId>
                <version>1.4</version>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <goals>
                            <goal>create</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
		</plugins>
	</build>
    <repositories>
       <repository>
          <id>Central Maven repository</id>
          <name>Central Maven repository https</name>
          <url>https://repo.maven.apache.org/maven2</url>
       </repository>
    </repositories>
    <pluginRepositories>
        <pluginRepository>
            <releases>
                <updatePolicy>never</updatePolicy>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <id>central</id>
            <name>Central Repository</name>
            <url>https://repo.maven.apache.org/maven2</url>
        </pluginRepository>
    </pluginRepositories>
</project>
